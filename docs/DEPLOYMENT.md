# Deployment

## Local

Call `./bin/deploy.sh` to test, build and deploy the application.

Wait for the deployment to finish (sometimes can take a few minutes).

## Azure

Go to [Azure portal](https://portal.azure.com)

### Start

Go to the application service slot called "temp (afakto/temp)".

Click on "Start".

Wait a short time, then open the URL: <https://afakto-temp.azurewebsites.net>

### Verifications

Check that the application is running correctly. Its version number can be found
when hovering the mouse over the top left logo...

If any issue, you can just "Stop" the deployment slot, fix the issue and redeploy.

### Swap slots

Click on "Swap" to swap the "temp" and "production" slots.

Wait for the swap to finish.

### Stop

Click on "Stop" to stop the "temp" slot.
