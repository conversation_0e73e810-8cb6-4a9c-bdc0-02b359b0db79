application {
  config {
    applicationType monolith
    authenticationType oauth2
    baseName Afakto
    blueprints []
    buildTool maven
    cacheProvider ehcache
    clientFramework vue
    clientPackageManager npm
    clientTheme yeti
    clientThemeVariant primary
    creationTimestamp 1654367469321
    databaseType sql
    devDatabaseType postgresql
    dtoSuffix DTO
    enableGradleEnterprise false
    enableHibernateCache true
    enableSwaggerCodegen false
    enableTranslation true
    jhiPrefix jhi
    jhipsterVersion "8.5.0"
    languages [en, fr]
    messageBroker false
    nativeLanguage en
    packageName com.afakto
    prodDatabaseType postgresql
    reactive false
    serverPort 8080
    serviceDiscoveryType no
    skipUserManagement true
    testFrameworks [cypress]
    websocket spring-websocket
    withAdminUi true
  }

  entities Address, BankTransaction, Buyer, BuyerFromFactor, BuyerFromInsurer, Cession, Company, Contact, Contract, CreditLimit, Datastream, ExternalCreditInsurance, FactorInstitution, Gap, Invoice, PaymentTerms
}

@ChangelogDate("**************")
entity Address {
  id UUID
  streetName String
  postalCode String
  city String
  stateProvince String
  country String
}
entity BankTransaction {
  id UUID required unique
  date LocalDate
  valueDate LocalDate
  transactionReferenceNumber String
  type BankTransactionType
  currency String
  amount BigDecimal
  balance BigDecimal
  category BankTransactionCategory
  ledgerAccount String
  narrative String
  identificationCode String
  referenceAccountServiceInstitution String
}

/**
 * Buyer is the debtor owing the invoice amount
 */
@ChangelogDate("**************")
entity Buyer {
  id UUID required unique
  code String required unique
  numberType NumberType required
  number String required
  name String
  excluded Boolean
  exclusionReason String
  currency String
  balance BigDecimal
  buyerFromFactorUnknown Boolean
}

@ChangelogDate("**************")
entity BuyerFromFactor {
  factorCode String
  number String
  name String
  currency String
  decimals Integer
  amountApproved BigDecimal
  amountOutstanding BigDecimal
  amountUnSecured BigDecimal
  amountUnFunded BigDecimal
  amountDraftReceived BigDecimal
}

/**
 * BuyerFromInsurer stores credit limit information from insurance providers
 */
@ChangelogDate("**************")
entity BuyerFromInsurer {
  insurerName String required
  raw Map
}

@ChangelogDate("**************")
entity Cession {
  id UUID required unique
  count Integer
  sum BigDecimal
  invoiceCount Integer
  invoiceSum BigDecimal
  creditNoteCount Integer
  creditNoteSum BigDecimal
  invoiceAddedCount Integer
  invoiceAddedSum BigDecimal
  creditNoteAddedCount Integer
  creditNoteAddedSum BigDecimal
  invoiceRemovedCount Integer
  invoiceRemovedSum BigDecimal
  creditNoteRemovedCount Integer
  creditNoteRemovedSum BigDecimal
  file String
  sentDate Instant
  error String
}

/**
 * Company is the creditor, sending invoices to related buyers
 */
@ChangelogDate("**************")
entity Company {
  id UUID required unique
  code String required unique
  numberType NumberType required
  number String required
  name String
}
@ChangelogDate("**************")
entity Contact {
  id UUID required unique
  name String
  phone String
  email String pattern(/^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$/)
}
/**
 * Contract is the agreement between the company and the afactor
 */
@ChangelogDate("**************")
entity Contract {
  id UUID required unique
  contractNumber String
  factorAccountNumber String
  status String
  syndicatedProgram Boolean
  confidentialProgram Boolean
  withRecourse Boolean
  unfunding Boolean
  monthlyMaxCession Integer
  signatureDate LocalDate
  activationDate LocalDate
  country String
  currency String
  guaranteeFund BigDecimal
  guaranteeLine BigDecimal
  nonGuaranteeLine BigDecimal
  factoringCommission BigDecimal
  minFactoringCommission BigDecimal
  indexRate Float
  indexRateFloor Float
  margin Float
}
@ChangelogDate("**************")
entity CreditLimit {
  id UUID required unique
  totalOutstanding BigDecimal
  outstandingUnderFactor BigDecimal
  currentCreditLimit BigDecimal
  requestedCreditLimit BigDecimal
  date ZonedDateTime
  status CreditLimitStatus
}

/**
 * A Datastream represents a data import/export flow.
 * It can be:
 * - incoming (outgoing = false): data imported from external sources (invoices, buyers, etc.)
 * - outgoing (outgoing = true): data exported to factors (BNP, SG balances, debtors, etc.)
 *
 * Each datastream is identified by its:
 * - orgId: the organization it belongs to
 * - type: the type of data (INVOICE, BUYER, BNP_BALANCE, etc.)
 * - path: the location of the file
 * - name: the name of the file
 *
 * It keeps track of:
 * - inserts: number of records inserted
 * - updates: number of records updated
 * - deletes: number of records deleted
 * - log: processing log
 * - error: error message if any
 * - failures: detailed list of processing failures
 */
entity Datastream {
    orgId String required
    type DatastreamType required
    outgoing Boolean required
    path String required
    name String required
    log String
    inserts Integer
    updates Integer
    deletes Integer
    error String
}

/**
 * Tracks individual failures during datastream processing
 */
entity DatastreamFailure {
    line Integer
    message String
    raw String
}

entity ExternalCreditInsurance {
  id UUID required unique
  name String
}
entity FactorInstitution {
  id UUID required unique
  type FactorInstitutionType
  code String
  name String
}

/**
 * Sometimes, some accounting data will be imported and give us information to
 * fill "gaps" between cession.
 */
@ChangelogDate("**************")
entity Gap {
  credit Boolean required
  type GapType required
}

/**
 * An invoice is a request for payment sent by the company to the buyer. It can be "sold" to an afactor
 */
@ChangelogDate("**************")
entity Invoice {
  id UUID required unique
  type InvoiceType
  invoiceNumber String
  date LocalDate
  dueDate LocalDate
  currency String
  amount BigDecimal
  balance BigDecimal
  paymentDate LocalDate
  excluded Boolean
  exclusionReason String
  isUnderFactor Boolean
  paymentMethod PaymentMethod
  reconciliationJournal String
}
@ChangelogDate("**************")
entity PaymentTerms {
  id UUID required unique
  numberOfDays Integer min(0)
}

enum BankTransactionCategory {
  BANK_TRANSFER,
  CESSION,
  CLIENT_COLLECTION,
  CURRENT_BALANCE,
  CURRENT_GUARANTEE_FUND,
  CURRENT_UNAVAILABLE,
  DIRECT_PAYMENT_NOT_FINANCED,
  FACTORING_COMMISSION,
  FEES_NOT_GTIE_INVOICES,
  FUNDING_COMMISSION,
  INITIAL_BALANCE,
  INITIAL_GUARANTEE_FUND,
  INITIAL_UNAVAILABLE,
  LOSS,
  MISCELLANEOUS,
  NOT_FINANCED_PAYMENTS,
  NOT_FINANCING,
  OTHER_FEES,
  PAYMENT_OF_INVOICE_NOT_BOUGHT,
  RESERVE_ACCOUNT,
  UNFUNDING,
  VAT_ON_COMMISSION
}
enum BankTransactionType {
  DEBIT,
  CREDIT
}
enum CreditLimitStatus {
  REJECTED,
  ACCEPTED,
  PARTIALLY_ACCEPTED,
  REQUESTED
}

/**
 * The DatastreamType enumeration.
 * Each type has an outgoing flag indicating if it's an export (true) or import (false).
 */
enum DatastreamType {
    BNP_ACCOUNT_STATEMENT(false),
    BNP_BALANCE(true),
    BNP_DEBTOR(true),
    BNP_OUTSTANDING(false),
    BNP_REMITTANCE(true),
    BUYER(false),
    CREDIT_LIMIT(false),
    INVOICE(false),
    MT94X(false),
    SG_BALANCE(true),
    SG_DEBTOR(true)
}

enum FactorInstitutionType {
  BANKING (BANKING),
  NON_BANKING (NON_BANKING)
}
enum GapType {
  EXCLUSION,
  OVERDUE,
  RECONCILIATION
}
enum InvoiceType {
  CREDIT_NOTE
  INVOICE
  OTHER
  UNALLOCATED_PAYMENT
}
enum NumberType {
  SIREN (SIREN),
  SIRET (SIRET),
  VAT (VAT),
  NATIONAL_REGISTRATION_NUMBER (NATIONAL_REGISTRATION_NUMBER),
  DUNS_BRADSTREET (DUNS_BRADSTREET),
  COMMERCIAL_REGISTER (COMMERCIAL_REGISTER)
}
enum PaymentMethod {
  BANK_CHECK ("BANK CHECK"),
  DRAFT (DRAFT),
  BANK_TRANSFER ("BANK TRANSFER"),
  POSTAL_TRANSFER ("POSTAL TRANSFER"),
  OTHERS (OTHERS),
  BAO (BAO),
  STANDING_ORDER ("STANDING ORDER"),
  DRAFT_NOT_ACCEPTED ("DRAFT NOT ACCEPTED")
}

relationship OneToOne {
  Buyer{address} to Address{buyer required}
  Buyer{buyerFromFactor} to BuyerFromFactor{buyer required}
  Buyer{buyerFromInsurer} to BuyerFromInsurer{buyer required}
  Buyer{contact} to Contact{buyer required}
  Buyer{paymentTerms} to PaymentTerms
  Company{address} to Address
  Gap{invoice required} to Invoice{gap}
}
relationship ManyToOne {
  BankTransaction{company required} to Company
  Buyer{company required} to Company
  Contract{company required} to Company
  Contract{factorInstitution required} to FactorInstitution
  Cession{contract required} to Contract
  Datastream{failures} to DatastreamFailure
  Gap{cession} to Cession{gaps}
  Invoice{buyer required} to Buyer
}
relationship ManyToMany {
  Cession{datastreams} to Datastream{cessions}
  Cession{invoices} to Invoice{cessions}
}

dto Address, BankTransaction, Buyer, BuyerFromFactor, BuyerFromInsurer, Cession, Company, Contact, Contract, CreditLimit, Datastream, DatastreamFailure, ExternalCreditInsurance, FactorInstitution, Invoice, PaymentTerms with mapstruct
service BankTransaction, Buyer, Cession, Company, Contract, Datastream , ExternalCreditInsurance, FactorInstitution, Invoice with serviceClass
paginate BankTransaction, Buyer, Cession, Company, Contract, Datastream , ExternalCreditInsurance, FactorInstitution, Invoice with pagination
filter BankTransaction, Buyer, Cession, Company, Contract, Datastream , ExternalCreditInsurance, FactorInstitution, Invoice
