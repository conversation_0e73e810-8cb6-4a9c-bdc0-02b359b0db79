# 🎨 AFAKTO Design System

> **An elegant and modern SaaS factoring solution**  
> *Complete design system for consistent user experience*

---

## 🎯 **Design Principles**

- **🔷 Clarity** : Clear interface for important financial decisions
- **⚡ Performance** : Fast loading and smooth interactions  
- **📱 Responsive** : Optimized for desktop, tablet and mobile
- **🌓 Accessibility** : Dark mode support and high contrast

---

## 🌈 **Color Palette**

### **Primary Colors**
```scss
// Backgrounds
$surface: #ffffff              // ⬜ Main white
$container: #f6f8fa            // 🔲 Very light gray
$backgroundSecondary: #f1f1f1  // 🔳 Light gray
$backgroundTertiary: #e6ebe9   // 🔲 Light gray-green
```

### **Brand Colors**
```scss
$primary: #232348              // 🟦 Dark blue primary
$secondary: #a1a0bd            // 🟣 Grayish purple
$accent: #625afa               // 🟪 Bright purple
$brandBlue: #232348            // 🔷 Brand blue
$brandYellow: #fcd983          // 🟨 Brand yellow
$brandMedium: #33a68d          // 🟢 Brand green
$brandLower: #a9e6d3           // 🟢 Light green
```

### **Functional Colors**
```scss
// States
$positive: #28cc42             // ✅ Success green
$negative: #ff3b2f             // ❌ Error red
$warning: #ff9500              // ⚠️ Warning orange
$disabled: #a3acba             // 🚫 Disabled gray

// Information
$infoMedium: #047aff           // ℹ️ Information blue
$infoLowest: #D7EFFF           // 💧 Very light blue
```

### **Neutral Colors**
```scss
// Text & Icons
$neutralLowest: #fcfcfc        // ⬜ Off-white
$neutralHigh: #737876          // 🔘 Medium gray
$neutralHigher: #434645        // ⚫ Dark gray
$neutralHighest: #0a0909       // ⬛ Black
```

### **Borders**
```scss
$neutral: #d5dbe1              // 🔲 Gray border
$borderPrimary: #f1f1f1        // 🔳 Primary border
$borderSecondary: #dde4e2      // 🔲 Secondary border
```

### **Dark Mode** 🌙
```scss
$dark-page: #1d1d1d                    // 🌑 Dark background
$backgroundSecondaryDark: #161716      // ⬛ Dark secondary background
$borderSecondaryDark: #242626          // 🔲 Dark border
$neutralHigherDark: #c1c8c6           // ⬜ Light text
$neutralHighestDark: #fcfcfc          // ⬜ Very light text
$infoLowestDark: #112d5a              // 🔷 Dark info
```

---

## 🔤 **Typography**

### **Font Family**
```scss
$typography-font-family: 'Noto Sans', sans-serif;
```

### **Typographic Hierarchy**

#### **H1 - Main Title**
```scss
font-size: 1.8rem;      // 28.8px
line-height: 2rem;      // 32px
font-weight: bold;
```

#### **H2 - Secondary Title**
```scss
font-size: 1.5em;       // 24px
font-weight: bold;
margin: 0;
```

#### **H3 - Subtitle**
```scss
font-size: 1.25em;      // 20px
margin: 0;
```

#### **Body Text**
```scss
font-size: 14px;        // Default
line-height: 1.4;
```

#### **Small Text**
```scss
font-size: 12px;        // Filters, metadata
font-weight: 500;
```

---

## 🧩 **Components**

### **🔘 Buttons**

#### **Primary Button (.btn--brand)**
```vue
<q-btn class="btn--brand">
  Primary Action
</q-btn>
```
- **Color** : `$brandMedium` (#33a68d)
- **Text** : White
- **Padding** : 12px 24px
- **Border-radius** : 8px
- **Hover effect** : -1px translation + shadow

#### **Secondary Button (.btn--secondary)**
```vue
<q-btn class="btn--secondary">
  Secondary Action
</q-btn>
```
- **Color** : Transparent
- **Border** : `$borderSecondary`
- **Text** : `$neutralHigher`

#### **Icon Button (.btn--icon)**
```vue
<q-btn class="btn--icon">
  <q-icon name="edit" />
</q-btn>
```
- **Shape** : Circular (40x40px)
- **Hover** : `$backgroundSecondary`

### **📋 Cards**

#### **Standard Card (.card)**
```vue
<q-card class="card">
  <q-card-section>
    Content
  </q-card-section>
</q-card>
```
- **Border** : 1px solid `$borderSecondary`
- **Border-radius** : 8px
- **Shadow** : 0 2px 4px rgba(0,0,0,0.1)

#### **Dashboard Card (.card--dashboard)**
```vue
<q-card class="card--dashboard">
  Dashboard widgets
</q-card>
```
- **Height** : 100%
- **Inherits** : .card styles

#### **Situation Card (.card--situation)**
```vue
<q-card class="card--situation">
  <div class="card--situation__header">
    Title
  </div>
  <p>Financial data</p>
</q-card>
```
- **Min-width** : 18em
- **Header** : Font-weight bold

### **🔍 Filters**

#### **Standard Filter (.filter)**
```vue
<q-select 
  class="filter"
  :class="{ 'filter--active': isActive }"
  dense
  borderless
/>
```
- **Shape** : Pill (border-radius: 9999px)
- **Size** : 12-16em width
- **Height** : 36px

#### **Active Filter (.filter--active)**
- **Color** : `$infoLowest` background
- **Border** : `$infoMedium`
- **Text** : `$infoMedium`

### **📊 Tables**

#### **Standard Table**
```vue
<q-table
  class="q-table"
  :data="data"
  :columns="columns"
/>
```
- **Borders** : `$borderSecondary`
- **Text color** : `$neutralHigher`
- **Fixed footer** : Position fixed at bottom

### **📱 Layout & Grid**

#### **Flex Utilities (.flex)**
```vue
<!-- Centered -->
<div class="flex flex--center">
  Centered content
</div>

<!-- Space between -->
<div class="flex flex--between">
  <div>Left</div>
  <div>Right</div>
</div>

<!-- Column -->
<div class="flex flex--column spacing--md">
  Column items
</div>
```

#### **Grid Utilities (.grid)**
```vue
<!-- 3 column grid -->
<div class="grid grid--3 grid--gap-md">
  <div>Item 1</div>
  <div>Item 2</div>
  <div>Item 3</div>
</div>
```

#### **Spacing (.spacing)**
- **--xs** : 4px
- **--sm** : 8px  
- **--md** : 16px
- **--lg** : 24px
- **--xl** : 32px

---

## 📐 **Breakpoints**

```scss
// Mobile
$breakpoint-xs-max: 599px

// Tablet
$breakpoint-sm-min: 600px
$breakpoint-sm-max: 1023px

// Desktop
$breakpoint-md-min: 1024px
$breakpoint-md-max: 1439px

// Large Desktop
$breakpoint-lg-min: 1440px
```

---

## 🎭 **States & Interactions**

### **Button States**
- **Normal** : Base color
- **Hover** : Darkening + translation
- **Active** : Return to normal position
- **Disabled** : `$disabled` + cursor: not-allowed
- **Loading** : Opacity 0.65 + spinner

### **Input States**
- **Normal** : Neutral border
- **Focus** : `$infoMedium` border
- **Error** : `$negative` border
- **Success** : `$positive` border

### **Transitions**
```scss
transition: all 0.3s ease;
```

---

## 🌓 **Dark Mode**

### **Activation**
```scss
.body--dark {
  // All dark styles
}
```

### **Main Adaptations**
- **Background** : `$dark-page` (#1d1d1d)
- **Cards** : `$borderSecondaryDark` borders
- **Text** : `$neutralHigherDark`
- **Charts** : Colors adjusted for contrast

---

## 🎨 **Utility Classes**

### **Text Colors**
```scss
.text-brandMedium     // Brand green
.text-positive        // Success green
.text-negative        // Error red
.text-neutralHigher   // Dark gray
```

### **Background Colors**
```scss
.bg-brandMedium       // Brand green background
.bg-backgroundSecondary // Light gray background
.bg-neutralLowest     // Off-white background
```

### **Responsive**
```scss
.responsive--hide-mobile    // Hidden on mobile
.responsive--hide-desktop   // Hidden on desktop
.responsive--full-width-mobile // Full width on mobile
```

---

## 📋 **Usage Examples**

### **Complete Dashboard Card**
```vue
<q-card class="card--dashboard">
  <q-card-section>
    <div class="flex flex--between flex--align-center">
      <h2>Widget Title</h2>
      <q-btn class="btn--icon">
        <q-icon name="more_vert" />
      </q-btn>
    </div>
    
    <div class="spacing--lg">
      <p class="text-neutralHigher">
        Important financial data
      </p>
      
      <div class="flex flex--between">
        <span class="text-positive">+12.5%</span>
        <span>€125,000</span>
      </div>
    </div>
  </q-card-section>
</q-card>
```

### **Filter Bar**
```vue
<q-toolbar class="flex flex--start spacing--md">
  <q-select 
    class="filter"
    :class="{ 'filter--active': selectedCompanies.length > 0 }"
    label="Companies"
    multiple
    v-model="selectedCompanies"
  />
  
  <q-select 
    class="filter"
    :class="{ 'filter--active': selectedCurrency }"
    label="Currency"
    v-model="selectedCurrency"
  />
  
  <q-space />
  
  <q-btn class="btn--brand">
    <q-icon name="add" />
    New Assignment
  </q-btn>
</q-toolbar>
```

### **Dashboard Section**
```vue
<section class="section--dashboard">
  <div class="grid grid--2 grid--gap-lg">
    <div class="card--situation">
      <div class="card--situation__header">
        Contract Situation
      </div>
      <p>Data...</p>
    </div>
    
    <div class="card--situation">
      <div class="card--situation__header">
        Factor Debt
      </div>
      <p>Data...</p>
    </div>
  </div>
</section>
```

---

## 🔧 **Maintenance & Evolution**

### **Adding New Colors**
1. Add to `quasar.variables.scss`
2. Create utility classes `.text-*` and `.bg-*`
3. Document in this design system

### **New Components**
1. Create in appropriate SCSS file
2. Use existing variables
3. Test in dark mode
4. Add examples here

### **Responsive**
- Always test mobile first
- Use defined breakpoints
- Plan dark mode adaptations

---

*📝 Last updated : Current AFAKTO project version*  
*🎨 Design system maintained by development team* 