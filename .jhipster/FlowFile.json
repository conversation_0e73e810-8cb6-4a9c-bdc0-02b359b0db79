{"annotations": {"changelogDate": "20240820162705"}, "dto": "mapstruct", "fields": [{"fieldName": "orgId", "fieldType": "String", "fieldValidateRules": ["required"]}, {"fieldName": "type", "fieldType": "FlowFileType", "fieldValidateRules": ["required"], "fieldValues": "BUYER,INVOIC<PERSON>,REMITTANCE,BALANCE,I22,I4C,MT94X"}, {"fieldName": "outgoing", "fieldType": "Boolean", "fieldValidateRules": ["required"]}, {"fieldName": "path", "fieldType": "String", "fieldValidateRules": ["required"]}, {"fieldName": "name", "fieldType": "String", "fieldValidateRules": ["required"]}, {"fieldName": "log", "fieldType": "String"}, {"fieldName": "error", "fieldType": "String"}], "jpaMetamodelFiltering": true, "name": "FlowFile", "pagination": "infinite-scroll", "readOnly": true, "relationships": [], "searchEngine": "no", "service": "serviceClass"}