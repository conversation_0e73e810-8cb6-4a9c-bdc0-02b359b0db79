{"annotations": {"changelogDate": "20241106172643"}, "dto": "mapstruct", "fields": [{"fieldName": "relatedEntityId", "fieldType": "UUID", "fieldValidateRules": ["required"]}, {"fieldName": "relatedEntityType", "fieldType": "String", "fieldValidateRules": ["required"]}, {"fieldName": "text", "fieldType": "String", "fieldValidateRules": ["required"]}], "jpaMetamodelFiltering": true, "name": "Comment", "pagination": "no", "readOnly": true, "searchEngine": "no", "service": "serviceClass"}