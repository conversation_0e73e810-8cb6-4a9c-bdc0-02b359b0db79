# afakto changelog

Changelog of afakto.

## 20250618 (2025-06-18)

### Features

-  **invoice**  add excluded flag on invoice ([800e4](https://github.com/Afakto/afakto/commit/800e4849922b78e) echarp)  
-  **admin**  script to copy some files to test ([afdb1](https://github.com/Afakto/afakto/commit/afdb190a55644b2) echarp)  
-  **companies**  init company drawer ([d180d](https://github.com/Afakto/afakto/commit/d180d514878209c) SKALSKI Alan)  
-  **drawer**  styling, edit button and expand on datastream show ([7a729](https://github.com/Afakto/afakto/commit/7a7299a63c7b8a7) SKALSKI Alan)  

### Bug Fixes

-  **companies**  fiscal year formatting ([ff6db](https://github.com/Afakto/afakto/commit/ff6db6e6a508962) SKALSKI Alan)  
-  **dashboard**  ratios tooltip ([44bb0](https://github.com/Afakto/afakto/commit/44bb0cc64d4fe86) SKALSKI Alan)  
-  **dashboard**  fix guarantee fund navigation ([debc6](https://github.com/Afakto/afakto/commit/debc6b0da092aaa) echarp)  

### Other changes

**touch up on drawers**


[66ed0](https://github.com/Afakto/afakto/commit/66ed0eae56be44c) SKALSKI Alan *2025-06-18 19:00:10*

**fix bugs**


[47664](https://github.com/Afakto/afakto/commit/4766490b0c9d14d) SKALSKI Alan *2025-06-18 19:00:10*

**i18n labels for show**


[03850](https://github.com/Afakto/afakto/commit/038504d70bc6963) SKALSKI Alan *2025-06-18 19:00:10*


## 20250617 (2025-06-17)

### Features

-  **buyers**  buyer creation same screen for enrichment ([fbb4c](https://github.com/Afakto/afakto/commit/fbb4cad0f45ebc2) Paul Comte)  

### Bug Fixes

-  **dashboard**  modify covered balance calculation ([91623](https://github.com/Afakto/afakto/commit/916236d0d0a2023) echarp)  

### Other changes

**Merge pull request #154 from paulcomte/improvements**

* Mainly buyers fixes 

[d9a70](https://github.com/Afakto/afakto/commit/d9a70b97065e61e) echarp *2025-06-17 09:13:48*

**Merge branch 'main' into improvements**


[71dea](https://github.com/Afakto/afakto/commit/71dead8c1bf8d90) Paul COMTE *2025-06-17 03:01:20*

**Merge remote-tracking branch 'origin/main' into improvements**


[5e260](https://github.com/Afakto/afakto/commit/5e260922f88bd74) Paul Comte *2025-06-13 02:33:56*


## ******** (2025-06-16)

### Features

-  **admin**  deployment script and documentation ([900cb](https://github.com/Afakto/afakto/commit/900cbde7e8b6573) echarp)  

### Bug Fixes

-  **datastream**  bnp account statement import correction ([2e755](https://github.com/Afakto/afakto/commit/2e75599286ea947) echarp)  

### Other changes


## ******** (2025-06-14)

### Features

-  **dashboard**  proper cover rate ([c033a](https://github.com/Afakto/afakto/commit/c033a636d7e30cf) echarp)  

### Bug Fixes

-  **datastream**  bnp account statement correction ([760aa](https://github.com/Afakto/afakto/commit/760aacc807bc865) echarp)  
-  **buyers**  change icons on buyer insurer table ([e115e](https://github.com/Afakto/afakto/commit/e115e5aa03c4cc8) SKALSKI Alan)  
-  **datastream**  fix close button on upload page ([d440f](https://github.com/Afakto/afakto/commit/d440fb9c9190d91) SKALSKI Alan)  
-  **datastream**  use i18n labels ([94b0b](https://github.com/Afakto/afakto/commit/94b0bdc45ed2c65) SKALSKI Alan)  

## ******** (2025-06-12)

### Features

-  **dashboard**  cover rate ([ab1d3](https://github.com/Afakto/afakto/commit/ab1d382651c5e81) echarp)  
-  **dashboard**  fine tuning on dashboard ([9e352](https://github.com/Afakto/afakto/commit/9e3524fbb8fd05d) SKALSKI Alan)  
-  **buyers**  mapping SIRET to SIREN ([c26a6](https://github.com/Afakto/afakto/commit/c26a6ec61cdc6eb) Paul Comte)  
-  **buyers**  buyer from insurer logo ([983ef](https://github.com/Afakto/afakto/commit/983eff8d22ffcc5) Paul Comte)  
-  **buyers**  refactor of services + resource + moved the buyer insurance id to the contract table ([05de3](https://github.com/Afakto/afakto/commit/05de3043a138d20) Paul Comte)  
-  **buyers**  buyer from insurer table with decision reason ([3bcd5](https://github.com/Afakto/afakto/commit/3bcd5dd0ede6291) Paul Comte)  
-  **credit-limit**  now using global contract ([f6af7](https://github.com/Afakto/afakto/commit/f6af795b11248bc) Paul Comte)  
-  **buyers**  contract selector in insurer panel ([fd8b4](https://github.com/Afakto/afakto/commit/fd8b431d7512464) Paul Comte)  
-  **buyers**  improved buyer enrich dialog code quality ([6614a](https://github.com/Afakto/afakto/commit/6614a0202cc91f0) Paul Comte)  
-  **datastream**  add failures tab to the drawer ([fc8c2](https://github.com/Afakto/afakto/commit/fc8c2eed8ca2101) SKALSKI Alan)  
-  **datastream**  dark mode on drawer ([4414a](https://github.com/Afakto/afakto/commit/4414a5b66445c00) SKALSKI Alan)  
-  **datastream**  init drawer behavior and style ([074c3](https://github.com/Afakto/afakto/commit/074c3626628e1b1) SKALSKI Alan)  
-  **app**  init consultation drawer ([3d512](https://github.com/Afakto/afakto/commit/3d5122853f66aee) SKALSKI Alan)  

### Bug Fixes

-  **user**  normalize user component dropdowns ([73b6a](https://github.com/Afakto/afakto/commit/73b6ab202dd5f71) SKALSKI Alan)  
-  **datastream**  fix failures bug ([f322a](https://github.com/Afakto/afakto/commit/f322a34a729ce01) SKALSKI Alan)  
-  **buyer**  dark mode for enrichment details ([50704](https://github.com/Afakto/afakto/commit/50704b24ba0e619) SKALSKI Alan)  
-  **datastream**  remove showpanel unused file ([3a912](https://github.com/Afakto/afakto/commit/3a912c938d91a64) SKALSKI Alan)  
-  **dashboard**  remove console log on top unavailable ([fad12](https://github.com/Afakto/afakto/commit/fad121cadfd2452) SKALSKI Alan)  
-  **datastream**  n format for inserts ([b6611](https://github.com/Afakto/afakto/commit/b661148b8001596) SKALSKI Alan)  

### Other changes

**Merge pull request #147 from alanskal/consultation-drawer**

* feat(datastream): Consultation drawer 

[d2905](https://github.com/Afakto/afakto/commit/d2905151de8c8ba) echarp *2025-06-11 15:29:02*

**Merge branch 'main' into consultation-drawer**


[1d770](https://github.com/Afakto/afakto/commit/1d770fe863e13a4) alanskal *2025-06-11 14:15:54*

**Merge pull request #148 from paulcomte/improvements**

* Buyer contract selection dialog + BuyerFromInsurer decision reason in contract 

[2f818](https://github.com/Afakto/afakto/commit/2f8182b941f852c) echarp *2025-06-11 13:17:52*

**merge changes**


[5bb97](https://github.com/Afakto/afakto/commit/5bb97edb20d7682) SKALSKI Alan *2025-06-11 11:12:57*

**Merge remote-tracking branch 'origin/main' into improvements**

* # Conflicts: 
* #	src/main/java/com/afakto/service/BuyerService.java 
* #	src/main/java/com/afakto/web/rest/BuyerResource.java 
* #	src/main/webapp/src/pages/entities/buyer/BuyerDetailsFromInsurerCoface.vue 

[c243e](https://github.com/Afakto/afakto/commit/c243ebceb0adedf) Paul Comte *2025-06-11 06:04:31*

**merge from main**


[fcfa6](https://github.com/Afakto/afakto/commit/fcfa6aa1baf31a8) Paul Comte *2025-06-10 03:34:35*


## 20250610 (2025-06-10)

### Bug Fixes

-  **buyer**  unknown buyers reset upon i4b imports ([2cc42](https://github.com/Afakto/afakto/commit/2cc424ae2bae062) echarp)  

### Other changes


## 20250608 (2025-06-08)

### Features

-  **invoice**  unavailable amount from factor ([44a51](https://github.com/Afakto/afakto/commit/44a516c44c2178b) echarp)  
-  **datastream**  proper buyer/invoice upload buttons ([a1aca](https://github.com/Afakto/afakto/commit/a1acaaaa8ece2f6) echarp)  
-  **datastream**  unknown buyers and under factor from i4b ([ce930](https://github.com/Afakto/afakto/commit/ce930cc48239b5b) echarp)  
-  **credit-limit**  top liner product description ([e4f41](https://github.com/Afakto/afakto/commit/e4f4153418159a8) Paul Comte)  
-  **buyers**  removed legal id 9pro + SIRET ([d23da](https://github.com/Afakto/afakto/commit/d23da65266a4551) Paul Comte)  
-  **buyers**  buyer creation + buyer enrichment error handle ([cdd54](https://github.com/Afakto/afakto/commit/cdd543e62069f29) Paul Comte)  
-  **ui**  custom error merge to notifyError ([b80cd](https://github.com/Afakto/afakto/commit/b80cd9c54349cb7) Paul Comte)  
-  **ui**  b-toggle component showing false on null value ([94770](https://github.com/Afakto/afakto/commit/9477078da77faea) Paul Comte)  
-  **buyers**  buyer enrichment by name on existing country ([5aa5a](https://github.com/Afakto/afakto/commit/5aa5aa83bf26f10) Paul Comte)  
-  **dashboard**  excluded card more inclusive ([8609c](https://github.com/Afakto/afakto/commit/8609c350d6662e0) echarp)  
-  **invoice**  invoice validation for import and API calls ([cce12](https://github.com/Afakto/afakto/commit/cce12a6203ba456) echarp)  
-  Add comprehensive design system and centralized CSS architecture - Add complete DESIGN_SYSTEM.md with color palette, typography, and components - Create modular CSS files: filters.scss, cards.scss, buttons.scss, layout.scss - Improve INSTALL.md with Windows-specific instructions - Fix package.json escaping issue for Windows compatibility - Centralize repeated styles for better maintainability ([d9ff3](https://github.com/Afakto/afakto/commit/d9ff345b3a25d83) SDaller)  
-  **security**  authorize swagger access ([798f4](https://github.com/Afakto/afakto/commit/798f442e1c3939a) echarp)  

### Bug Fixes

-  **buyer**  some enrichment field was required ([81fc8](https://github.com/Afakto/afakto/commit/81fc8f75790b913) echarp)  
-  **buyer**  quick fix to buyer list when missing address ([51971](https://github.com/Afakto/afakto/commit/519712bd40225bf) echarp)  
-  **app**  fixed package.json for unix system ([f72fa](https://github.com/Afakto/afakto/commit/f72fac6256fe094) Paul Comte)  
-  **buyers**  rollback buyer creation country selection to working state ([09e1e](https://github.com/Afakto/afakto/commit/09e1ea23345c87d) Paul Comte)  
-  **UI**  remove emit-value from BSelector ([b8cff](https://github.com/Afakto/afakto/commit/b8cff85e8830eb5) echarp)  

### Dependency updates

- update dependencies ([cd3ff](https://github.com/Afakto/afakto/commit/cd3ffe8b9f6e1a3) echarp)  
- update dependencies ([f441a](https://github.com/Afakto/afakto/commit/f441ab758f81eeb) echarp)  
### Other changes

**Merge branch 'main' of https://github.com/Afakto/afakto**


[b8a00](https://github.com/Afakto/afakto/commit/b8a008dc1105ca5) SDaller *2025-06-05 06:22:22*

**Merge branch 'main' of https://github.com/Afakto/afakto**


[530ca](https://github.com/Afakto/afakto/commit/530ca5541df9766) SDaller *2025-06-05 06:22:00*


## 20250604 (2025-06-04)

### Features

-  **buyer**  import more lenient on absent number id ([b58c1](https://github.com/Afakto/afakto/commit/b58c12bd0456ff9) echarp)  
-  **credit-limit**  coface node model to avoid further json parsing ([a564d](https://github.com/Afakto/afakto/commit/a564dd6345d88f8) Paul Comte)  
-  **credit-limit**  coface request model to avoid further json parsing on credit limit request ([90a3e](https://github.com/Afakto/afakto/commit/90a3e2b27a53c9e) Paul Comte)  
-  **buyers**  coface ESP search now works without ES prefix ([25ae2](https://github.com/Afakto/afakto/commit/25ae24a05f5b415) Paul Comte)  

### Bug Fixes

-  **datastream**  file download based on org name ([3923b](https://github.com/Afakto/afakto/commit/3923ba1d243259f) echarp)  

### Other changes


## 20250602 (2025-06-02)

### Features

-  **invoice**  invoice import in chunks of 25 ([f39b5](https://github.com/Afakto/afakto/commit/f39b51a04f81286) echarp)  
-  **datastream**  better encodings and line ending mgmt ([cd849](https://github.com/Afakto/afakto/commit/cd849a3a77be244) echarp)  
-  **buyers**  buyer creation better country filter ([803d1](https://github.com/Afakto/afakto/commit/803d1abd99552e3) Paul Comte)  

### Bug Fixes

-  **buyers**  fixed excluded buyer false by default ([e66c1](https://github.com/Afakto/afakto/commit/e66c10863ec2804) Paul Comte)  
-  **buyers**  removed NRN US regex rule ([b4924](https://github.com/Afakto/afakto/commit/b4924b62fd1e3d3) Paul Comte)  

### Other changes


## ******** (2025-05-31)

### Features

-  **security**  redirect to official auth0 config ([f1fbf](https://github.com/Afakto/afakto/commit/f1fbf5a0444b796) echarp)  
-  **bank-transaction**  migrate to CURRENT and INITIAL GUARANTEE_FUND ([9da5a](https://github.com/Afakto/afakto/commit/9da5a6ab382af69) echarp)  

## ******** (2025-05-28)

### Features

-  **buyer**  simplified enrichment ([803eb](https://github.com/Afakto/afakto/commit/803eb6a961ecc16) echarp)  
-  **buyer**  import ISO3 country codes ([f9590](https://github.com/Afakto/afakto/commit/f9590af2431a466) echarp)  
-  **buyer**  default number type is NRN ([17477](https://github.com/Afakto/afakto/commit/1747738a7a1fe64) echarp)  
-  **buyers**  gauge tooltip ([6220f](https://github.com/Afakto/afakto/commit/6220f037c3d41ad) SKALSKI Alan)  
-  **buyers**  extended the regex support for more countries ([022ef](https://github.com/Afakto/afakto/commit/022efe7b6497045) Paul Comte)  
-  **invoide**  check that due date is after invoice date ([e1c2e](https://github.com/Afakto/afakto/commit/e1c2ea652703624) echarp)  
-  **buyer**  NRN and National accepted as number type ([184ae](https://github.com/Afakto/afakto/commit/184aed156ce3cf2) echarp)  
-  **datastream**  extra line feed removed from BNP exports ([730ec](https://github.com/Afakto/afakto/commit/730ec16e75fe3be) echarp)  
-  **dashboard**  ratios on situation bar ([b18e8](https://github.com/Afakto/afakto/commit/b18e841acbdf5b4) SKALSKI Alan)  
-  **dashboard**  ratios in %, refine rules for amount displayed on different dashboard components ([2437e](https://github.com/Afakto/afakto/commit/2437eaa65b45b47) SKALSKI Alan)  
-  **buyers**  search can now use coface policy if none found in org ([72ca9](https://github.com/Afakto/afakto/commit/72ca9f05e29b9a2) Paul Comte)  
-  **buyers**  fixed tooltip on number badge + merge ([69513](https://github.com/Afakto/afakto/commit/69513060b57ba69) Paul Comte)  
-  **UI**  rounded borders on all flags ([6d3ce](https://github.com/Afakto/afakto/commit/6d3cea1cf8202c4) echarp)  
-  **buyers**  better enrichment using same behavior as the BuyerCreation ([0e4b9](https://github.com/Afakto/afakto/commit/0e4b9b823c361ff) Paul Comte)  
-  **user**  invitation expiration increased to 30 days ([14bea](https://github.com/Afakto/afakto/commit/14bea8a9801d673) echarp)  
-  **buyers**  search buyer by number on creation ([faa7b](https://github.com/Afakto/afakto/commit/faa7bd4549e3475) Paul Comte)  
-  **buyers**  removed buyer list store + fixed double call on search identity ([98146](https://github.com/Afakto/afakto/commit/98146f5ef97a85d) Paul Comte)  
-  **buyers**  buyer list added error tooltip if regex does not match ([6ed49](https://github.com/Afakto/afakto/commit/6ed49c9857841ce) Paul Comte)  
-  **buyers**  fixed buyer search to properly set the number type ([3fc56](https://github.com/Afakto/afakto/commit/3fc56f0572d0972) Paul Comte)  
-  **buyers**  auto identifier based on name or number + fill country ([35888](https://github.com/Afakto/afakto/commit/358888bf15687a7) Paul Comte)  
-  **buyers**  buyer creation online data -> data enrichment ([304ab](https://github.com/Afakto/afakto/commit/304abb502d567ec) Paul Comte)  
-  **buyers**  BuyersList + BuyerCreation ([0ba56](https://github.com/Afakto/afakto/commit/0ba5686efdaa9a7) Paul Comte)  
-  **buyer**  better date format in BuyerList ([1f996](https://github.com/Afakto/afakto/commit/1f9964df411a741) echarp)  
-  **buyers**  added tooltip on buyer number, to get localized number type ([847bf](https://github.com/Afakto/afakto/commit/847bf60e6eba047) Paul Comte)  
-  **buyers**  buyer creation national number mapping ([46c6c](https://github.com/Afakto/afakto/commit/46c6ce8c9624de4) Paul Comte)  

### Bug Fixes

-  **buyers**  fixed changed invoice type ([744ab](https://github.com/Afakto/afakto/commit/744abc4aa924592) Paul Comte)  
-  **datastream**  corrected invoice header split ([1a25c](https://github.com/Afakto/afakto/commit/1a25cfdb4348dac) echarp)  
-  **dashboard**  correct formulas for dashboard ratios ([a6674](https://github.com/Afakto/afakto/commit/a6674ed7d2a0002) SKALSKI Alan)  
-  **dashboard**  amount outstanding redirection arrow and tooltip ([86281](https://github.com/Afakto/afakto/commit/86281bca865f83b) SKALSKI Alan)  
-  **app**  add color variable ([3f07d](https://github.com/Afakto/afakto/commit/3f07d44d9c89a2d) SKALSKI Alan)  
-  **buyers**  disable buyer details enrichment ([2e602](https://github.com/Afakto/afakto/commit/2e6027c89e01e5c) Paul Comte)  
-  **buyers**  fixed buyer iso3 country to iso2 conversion ([2ba0e](https://github.com/Afakto/afakto/commit/2ba0e0eab5ccaf9) Paul Comte)  
-  **buyers**  fixed enrich select country dialog ([9cfc8](https://github.com/Afakto/afakto/commit/9cfc80827da30ef) Paul Comte)  
-  **buyers**  fixed buyer creation broken by new enrichment button ([a9f56](https://github.com/Afakto/afakto/commit/a9f56db521a49dc) Paul Comte)  
-  **buyers**  fixed french version ([f2548](https://github.com/Afakto/afakto/commit/f25485ff6e72e30) Paul Comte)  
-  **buyers**  removed buyer creation console.log ([22310](https://github.com/Afakto/afakto/commit/22310b1dad874cf) Paul Comte)  

### Other changes

**revert changelog modifications**


[d854b](https://github.com/Afakto/afakto/commit/d854b8fce6f4244) SKALSKI Alan *2025-05-28 12:53:52*

**Merge pull request #139 from paulcomte/buyer-enrichment**

* Buyer enrichment button 

[760db](https://github.com/Afakto/afakto/commit/760db83924a4d99) echarp *2025-05-23 12:05:57*

**Merge branch 'main' into buyer-enrichment**


[da59a](https://github.com/Afakto/afakto/commit/da59ae0a43e972a) echarp *2025-05-23 12:05:40*

**merge from main + fixed buyer tooltip**


[1e8e4](https://github.com/Afakto/afakto/commit/1e8e427d1deac57) Paul Comte *2025-05-22 23:43:10*


## 20250519 (2025-05-19)

### Features

-  **filters**  rounded flags in selector ([1b63f](https://github.com/Afakto/afakto/commit/1b63f503fe75459) echarp)  
-  **invoice**  validate amount/balance ([2ba53](https://github.com/Afakto/afakto/commit/2ba53a8cb2d020b) echarp)  

### Bug Fixes

-  **datastream**  reset buyer from factor data also by currency ([53182](https://github.com/Afakto/afakto/commit/531828ff2c7e624) echarp)  

### Other changes


## 20250515 (2025-05-15)

### Features

-  **contract**  improve form fields ([9c6ec](https://github.com/Afakto/afakto/commit/9c6ec9539a73bd8) echarp)  
-  **buyer**  fix validation + admin only button ([7b095](https://github.com/Afakto/afakto/commit/7b0957354b938a7) Paul Comte)  
-  **buyer**  buyer creation auto fill ([8f2a6](https://github.com/Afakto/afakto/commit/8f2a6d85bf5a99a) Paul Comte)  
-  **app**  reset button to delete all filters ([5f399](https://github.com/Afakto/afakto/commit/5f399d45936d136) SKALSKI Alan)  
-  **app**  filters phase 2 design and behavior for all filtering options, with better alignement, dark mode and responsive ([4a2b4](https://github.com/Afakto/afakto/commit/4a2b4e4e3bd4f19) SKALSKI Alan)  
-  **filters**  active class when value is selected, new-filter class on the majority of filters, display value behavior for enum and select, boolean display value ([12047](https://github.com/Afakto/afakto/commit/120474eddb43397) SKALSKI Alan)  
-  **contract**  non guarantee line used for covers ([a95ab](https://github.com/Afakto/afakto/commit/a95ab154db8e2fa) echarp)  
-  **buyer**  buyer creation search online ([1a9a7](https://github.com/Afakto/afakto/commit/1a9a7b22eda0a9b) Paul Comte)  
-  **swagger**  PublicEndpoint annotation ([ead19](https://github.com/Afakto/afakto/commit/ead1988d569c770) Paul Comte)  
-  **swagger**  removed swagger hidden annotation, migrating to new PublicEndpoint ([af3f1](https://github.com/Afakto/afakto/commit/af3f1283822abdd) Paul Comte)  
-  **swagger**  fixed swagger-ui permissions ([39783](https://github.com/Afakto/afakto/commit/397836b24603a0f) Paul Comte)  
-  **buyer**  buyer creation redesign ([0b8e9](https://github.com/Afakto/afakto/commit/0b8e9c3490777f1) Paul Comte)  
-  **buyer**  by default only listing buyers with outstanding invoices ([70b0b](https://github.com/Afakto/afakto/commit/70b0b29214591b4) echarp)  
-  **buyer**  added enrichment capabilities on more vat numbers ([a6299](https://github.com/Afakto/afakto/commit/a62998e5d851390) Paul Comte)  

### Bug Fixes

-  **buyer**  fix style, dark mode, icons, enrich tooltip ([0ca79](https://github.com/Afakto/afakto/commit/0ca796dc5a0b83a) SKALSKI Alan)  

### Dependency updates

- update dependencies ([3db5c](https://github.com/Afakto/afakto/commit/3db5c42a9c54fc5) echarp)  
### Other changes

**Merge pull request #134 from paulcomte/buyer-creation**

* TODO DESIGN - Buyer creation 

[bb82d](https://github.com/Afakto/afakto/commit/bb82d21ae523f65) echarp *2025-05-15 09:02:22*

**Merge branch 'main' into buyer-creation**


[d1672](https://github.com/Afakto/afakto/commit/d1672ff87d1d78b) Paul COMTE *2025-05-15 02:38:47*


## 20250512 (2025-05-12)

### Features

-  **datastream**  another date format ([c0b63](https://github.com/Afakto/afakto/commit/c0b63017407aae7) echarp)  
-  **datastream**  import more buyer columns ([f102f](https://github.com/Afakto/afakto/commit/f102f61426bc787) echarp)  
-  **buyer**  currency and amount cache columns ([c2fe4](https://github.com/Afakto/afakto/commit/c2fe4e7f69b1eaf) echarp)  
-  **buyer**  added enrichment capabilities on more vat numbers ([e54dd](https://github.com/Afakto/afakto/commit/e54dd31d4086d43) Paul Comte)  

### Bug Fixes

-  **dashboard**  guarantee fund better calculated ([89c5c](https://github.com/Afakto/afakto/commit/89c5c317047d5c2) echarp)  
-  **buyers**  fix disabled enrich button alignement ([3e23b](https://github.com/Afakto/afakto/commit/3e23b5eaa5efc58) SKALSKI Alan)  

### Dependency updates

- update dependencies ([4c169](https://github.com/Afakto/afakto/commit/4c169043099f90c) echarp)  
### Other changes


## 20250507 (2025-05-07)

### Features

-  **buyer**  improved overall page design ([35203](https://github.com/Afakto/afakto/commit/3520330d8ebc815) Paul Comte)  
-  **address**  rounded borders for countries ([ee6cf](https://github.com/Afakto/afakto/commit/ee6cf05b2365802) Paul Comte)  
-  **swagger**  added whitelist system on doc ([89f22](https://github.com/Afakto/afakto/commit/89f22fb1ebc631d) Paul Comte)  
-  **swagger**  removed dead route request matchers ([a2206](https://github.com/Afakto/afakto/commit/a220674a1abd7b0) Paul Comte)  
-  **swagger**  admin resources hidden in swagger ([e356c](https://github.com/Afakto/afakto/commit/e356c003bdb8f48) Paul Comte)  
-  **swagger**  added swagger resources ([8df38](https://github.com/Afakto/afakto/commit/8df387f51019f40) Paul Comte)  

### Bug Fixes

-  **datastream**  finds organization by name ([a94ca](https://github.com/Afakto/afakto/commit/a94caa690216852) echarp)  
-  **buyer**  fix enrich button style ([52fe7](https://github.com/Afakto/afakto/commit/52fe7cc5040be1f) SKALSKI Alan)  
-  **filters**  increase clickable zone for fliter closing, add back clearable for now ([7f420](https://github.com/Afakto/afakto/commit/7f420dccd496065) SKALSKI Alan)  
-  **buyers**  remove 'warnings' filter' ([3b807](https://github.com/Afakto/afakto/commit/3b807cac15f5909) SKALSKI Alan)  

### Other changes

**Merge pull request #124 from paulcomte/swagger-hidden**

* feat(swagger): admin resources hidden in swagger 

[8f4b8](https://github.com/Afakto/afakto/commit/8f4b8f6d4590f19) echarp *2025-05-07 09:00:53*

**Merge branch 'main' into swagger-hidden**


[4cd55](https://github.com/Afakto/afakto/commit/4cd55b310cd5c92) echarp *2025-05-07 09:00:38*


## 20250506 (2025-05-06)

### Features

-  **cession**  manage creditor gaps ([a2668](https://github.com/Afakto/afakto/commit/a2668f219558a98) echarp)  
-  **dashboard**  add back tooltip with full amount showing ([c9480](https://github.com/Afakto/afakto/commit/c9480765ebfa19b) SKALSKI Alan)  
-  **buyers**  centralize all warnings ([20c0e](https://github.com/Afakto/afakto/commit/20c0e2fcd19e6f5) SKALSKI Alan)  
-  **buyer**  custom error messages ([ed235](https://github.com/Afakto/afakto/commit/ed235922d488bde) Paul Comte)  
-  **buyer**  cleaned imports + removed redundant buyers component in company tab ([c257f](https://github.com/Afakto/afakto/commit/c257f047da1bd47) Paul Comte)  
-  **credit-limit**  insurer comment ([3c3c5](https://github.com/Afakto/afakto/commit/3c3c5a339f88f3f) Paul Comte)  
-  **credit-limit**  policy id shown on credit limit table ([f6f5d](https://github.com/Afakto/afakto/commit/f6f5d537bac46ba) Paul Comte)  
-  **buyer**  cleaned imports + removed redundant buyers component in company tab ([07934](https://github.com/Afakto/afakto/commit/0793488ca87d2ca) Paul Comte)  
-  **buyer**  custom error messages ([cc45d](https://github.com/Afakto/afakto/commit/cc45dd8c6292a44) Paul Comte)  

### Bug Fixes

-  **cession**  dense checkbox ([61669](https://github.com/Afakto/afakto/commit/61669bb261e0c54) echarp)  
-  **i18n**  update excluded buyers translation ([dc83d](https://github.com/Afakto/afakto/commit/dc83d86750db171) SKALSKI Alan)  
-  **buyers**  add more clarity on excluded warning ([4d4d0](https://github.com/Afakto/afakto/commit/4d4d0674cd8d7ca) SKALSKI Alan)  
-  **buyers**  add back column visibility line ([3cef5](https://github.com/Afakto/afakto/commit/3cef5d123b7cfed) SKALSKI Alan)  
-  **buyers**  correct conditional exclusion reason rendering ([2df33](https://github.com/Afakto/afakto/commit/2df33ede6dba07a) SKALSKI Alan)  
-  **i18n**  added back by translation ([2b510](https://github.com/Afakto/afakto/commit/2b5108ae13e734a) SKALSKI Alan)  
-  **orgas**  deal with empty logo in auth0 ([973af](https://github.com/Afakto/afakto/commit/973af6047ce68ed) echarp)  

### Other changes

**Merge pull request #117 from paulcomte/coface-update**

* Credit limit buyer view 

[ae03f](https://github.com/Afakto/afakto/commit/ae03f47c6233798) echarp *2025-05-05 11:25:14*

**Merge branch 'main' into coface-update**


[03a9e](https://github.com/Afakto/afakto/commit/03a9e2d2a69bd45) echarp *2025-05-05 11:24:50*


## 20250430 (2025-04-30)

### Bug Fixes

-  **contract**  change blind cover translation ([c73fe](https://github.com/Afakto/afakto/commit/c73fe88a6e7db08) echarp)  
-  **comment**  comments hidden behind pagination ([e3dd3](https://github.com/Afakto/afakto/commit/e3dd345b7c4eda9) echarp)  
-  **comment**  no empty comment ([1d416](https://github.com/Afakto/afakto/commit/1d4167f8f435624) echarp)  
-  **security**  let all roles download files ([9e02a](https://github.com/Afakto/afakto/commit/9e02ac12a7d927e) echarp)  
-  **cession**  properly associate datastreams to cession ([07bf6](https://github.com/Afakto/afakto/commit/07bf61f638d7889) echarp)  
-  **UI**  better table footer height ([6b923](https://github.com/Afakto/afakto/commit/6b9237864109d8c) echarp)  

### Other changes

**Update src/main/webapp/i18n/fr/contract.json**

* Co-authored-by: echarp &lt;<EMAIL>&gt; 

[42a85](https://github.com/Afakto/afakto/commit/42a85608d43ab90) alanskal *2025-04-30 13:49:40*

**feat(buyer) improved code visibility on credit limit table**


[956bd](https://github.com/Afakto/afakto/commit/956bdbd4b97671c) Paul Comte *2025-04-30 13:48:53*

**feat(buyer) improved buyer list icon design, insurer credit limit design**


[89233](https://github.com/Afakto/afakto/commit/892330c9d6b74fc) Paul Comte *2025-04-30 13:48:53*

**refactor(buyer) improved code quality + disabled duns enrichment**


[79d5d](https://github.com/Afakto/afakto/commit/79d5d56710db415) Paul Comte *2025-04-30 13:48:53*

**bug(buyer) removed extra console.log**


[6eb9a](https://github.com/Afakto/afakto/commit/6eb9a43dbbe499b) Paul Comte *2025-04-30 13:48:53*

**bug(buyer) fixed button style**


[5fe46](https://github.com/Afakto/afakto/commit/5fe46e2244094da) Paul Comte *2025-04-30 13:48:53*


## 20250429 (2025-04-29)

### Features

-  **UI**  bottom pagination always fixed ([ef79d](https://github.com/Afakto/afakto/commit/ef79d97f833906d) echarp)  
-  **datastream**  add upload/download direction icon ([693f6](https://github.com/Afakto/afakto/commit/693f697b722c43b) echarp)  

### Bug Fixes

-  **invoice**  translation mechanism breaking display ([2e57b](https://github.com/Afakto/afakto/commit/2e57b5e06f34369) echarp)  
-  **UI**  forgotten pagination background color ([065c1](https://github.com/Afakto/afakto/commit/065c19d8f4db13f) echarp)  
-  **cession**  correctly hide cashIn column ([944ab](https://github.com/Afakto/afakto/commit/944abd6b3b9d640) echarp)  
-  proper quasar distDir configuration ([60970](https://github.com/Afakto/afakto/commit/60970bd29fa94e8) echarp)  
-  production build of the i18n files ([24133](https://github.com/Afakto/afakto/commit/241330edbedbdd3) echarp)  

### Other changes


## 20250428 (2025-04-28)

### Features

-  **cession**  better header card display ([f28db](https://github.com/Afakto/afakto/commit/f28dbc8caa3818d) echarp)  
-  **datastream**  import SG invoice types ([fb6c8](https://github.com/Afakto/afakto/commit/fb6c8e504f53207) echarp)  
-  **credit-limit**  updated current credit limit visuals ([3c8be](https://github.com/Afakto/afakto/commit/3c8be1b03a6e251) Paul Comte)  

### Bug Fixes

-  **buyer**  display enrichment buyer - only to admin ([6764b](https://github.com/Afakto/afakto/commit/6764bc835e21a86) echarp)  
-  **buyer**  hide enrichment button ([dc0d6](https://github.com/Afakto/afakto/commit/dc0d67967e2799c) echarp)  
-  **UI**  use newer version of noto font ([e2fec](https://github.com/Afakto/afakto/commit/e2feca3b5490b06) echarp)  
-  **cession**  correct totalAmount for paid elements ([fd928](https://github.com/Afakto/afakto/commit/fd928dcb8f7741b) echarp)  

### Other changes


## 20250425 (2025-04-25)

### Features

-  **cession**  creation and consultation overhauled ([eb9ac](https://github.com/Afakto/afakto/commit/eb9acf930744a4e) echarp)  
-  **credit-limit**  merge ([5ed91](https://github.com/Afakto/afakto/commit/5ed91bbb51ec1e4) Paul Comte)  
-  **app**  switch back to noto font ([60e75](https://github.com/Afakto/afakto/commit/60e75d92c2a5379) SKALSKI Alan)  
-  **factor**  screen cleanup ([1ddc2](https://github.com/Afakto/afakto/commit/1ddc28687fae3ce) echarp)  
-  **dashboard**  Excluded buyers KPI ([0a459](https://github.com/Afakto/afakto/commit/0a459cd86aa9887) SKALSKI Alan)  
-  **buyer**  insurer decision reason request button ([681cb](https://github.com/Afakto/afakto/commit/681cb4d4cc4ebaf) Paul Comte)  
-  **UI**  cards less rounded corners ([2d832](https://github.com/Afakto/afakto/commit/2d832fe372ad942) echarp)  
-  **buyer**  insurer decision reason request button ([74ebb](https://github.com/Afakto/afakto/commit/74ebb2960cd7d28) Paul Comte)  
-  **buyer**  enrich buyer button on missing country ([9f112](https://github.com/Afakto/afakto/commit/9f112622549a564) Paul Comte)  
-  **UI**  sharper import/export icons ([d1b9c](https://github.com/Afakto/afakto/commit/d1b9c7a043f3fd6) echarp)  
-  **UI**  sticky pagination larger ([358c9](https://github.com/Afakto/afakto/commit/358c93b1f6bd745) echarp)  

### Bug Fixes

-  **app.scss**  remove unused line ([9f69a](https://github.com/Afakto/afakto/commit/9f69afd7701275a) SKALSKI Alan)  
-  **app**  add missing button on transactions(configure) and cession validation ([60345](https://github.com/Afakto/afakto/commit/603452f6d3db9ae) SKALSKI Alan)  
-  **app**  delete blank line ([3778b](https://github.com/Afakto/afakto/commit/3778b339709e3cb) SKALSKI Alan)  
-  **app**  delete unused lines ([05ab6](https://github.com/Afakto/afakto/commit/05ab638a25c8ed6) SKALSKI Alan)  
-  **cession**  async datastream generation ([581ce](https://github.com/Afakto/afakto/commit/581cecbdff2ebf4) echarp)  

### Other changes

**feat(buyer) fixed icon design**


[ecf03](https://github.com/Afakto/afakto/commit/ecf03c44cf02669) Paul Comte *2025-04-25 12:52:14*

**feat(buyer) fixed icon design**


[21a99](https://github.com/Afakto/afakto/commit/21a9957bc22b03a) Paul Comte *2025-04-25 12:52:14*

**Merge pull request #111 from paulcomte/coface-service**

* Coface service 

[b5d02](https://github.com/Afakto/afakto/commit/b5d0265e28035b2) echarp *2025-04-25 12:51:50*

**refactor(insurer) made a FeignInsurerCofaceHelper**


[3d51f](https://github.com/Afakto/afakto/commit/3d51ff94630bfbf) Paul Comte *2025-04-25 05:14:07*

**refactor(buyer) improved code quality**


[39796](https://github.com/Afakto/afakto/commit/3979608ab22595d) Paul Comte *2025-04-25 03:40:50*

**Merge branch 'main' into insurer-decision-reason**


[1620e](https://github.com/Afakto/afakto/commit/1620ed09d912d0a) Paul COMTE *2025-04-24 06:55:13*


## 20250422 (2025-04-22)

### Features

-  **cession**  optimise cession creation ([d2515](https://github.com/Afakto/afakto/commit/d25157a3a91bc6a) echarp)  
-  **user**  loading gears when consulting "all" comments ([10a4c](https://github.com/Afakto/afakto/commit/10a4cde870cd36f) echarp)  

### Bug Fixes

-  **security**  reinstate proper "getMapperImpl" ([fc69d](https://github.com/Afakto/afakto/commit/fc69d58aa282382) echarp)  
-  **UI**  some checkboxes where using readonly instead of disable ([a1a0b](https://github.com/Afakto/afakto/commit/a1a0b17cf5b0bb8) echarp)  
-  **UI**  revert from @use to @import quasar style files ([13bbc](https://github.com/Afakto/afakto/commit/13bbcf30a36f0cb) echarp)  
-  **UI**  use outline icons (in most places) ([c4f3f](https://github.com/Afakto/afakto/commit/c4f3fb974139ea5) echarp)  
-  **cession**  counters properly reset when changing contract ([9aa9f](https://github.com/Afakto/afakto/commit/9aa9f0367b30795) echarp)  
-  **UI**  debounce timing added to the text filter ([15498](https://github.com/Afakto/afakto/commit/154981b40250668) echarp)  

### Other changes


## 20250418 (2025-04-18)

### Features

-  **cession**  link to gaps in invoice screen ([f21f3](https://github.com/Afakto/afakto/commit/f21f39ea87c2d3d) echarp)  
-  **app**  new add filter & column buttons, style and label ([bc7f8](https://github.com/Afakto/afakto/commit/bc7f8a66e5dfae3) SKALSKI Alan)  
-  **afakto**  removed 4MB of downloaded fonts on new user connection ([c344c](https://github.com/Afakto/afakto/commit/c344cf670486f7c) Paul Comte)  
-  **credit-limit**  input error message on amount not divisible by 1,000 ([1eabd](https://github.com/Afakto/afakto/commit/1eabd9c728ddd14) Paul Comte)  

### Bug Fixes

-  **UI**  quick correction on display column button ([5abcd](https://github.com/Afakto/afakto/commit/5abcd1c62573eeb) echarp)  
-  **comments**  only load comments when an entity id is available ([7c086](https://github.com/Afakto/afakto/commit/7c0869db6653913) echarp)  

### Other changes


## 20250417 (2025-04-17)

### Features

-  **credit-limit**  more user messages ([2a97f](https://github.com/Afakto/afakto/commit/2a97f34a21e16d2) echarp)  
-  **credit-limit**  translations and/or other UI elements ([f974b](https://github.com/Afakto/afakto/commit/f974bd11ae0f752) echarp)  
-  **afakto**  better error message on notification ([ee3b8](https://github.com/Afakto/afakto/commit/ee3b832ec920b88) Paul Comte)  
-  **credit-limit**  emit signal to refresh credit limit request table when updating ([77c92](https://github.com/Afakto/afakto/commit/77c92223986699a) Paul Comte)  
-  **credit-limit**  moved input logic to global <b-input> ([5cf74](https://github.com/Afakto/afakto/commit/5cf74754e3f29a5) Paul Comte)  
-  **dashboard**  add redirection arrows ([d4589](https://github.com/Afakto/afakto/commit/d4589d1229d4f0b) SKALSKI Alan)  
-  **goodbye**  no more picsum ([3b322](https://github.com/Afakto/afakto/commit/3b3225830c9ed18) echarp)  
-  **credit-limit**  various UI improvments ([1d1af](https://github.com/Afakto/afakto/commit/1d1af39b7a91299) Paul Comte)  
-  **credit-limit**  removed extra coface call to retrieve history ([63754](https://github.com/Afakto/afakto/commit/63754bb1c2cf5bb) Paul Comte)  
-  **credit-limit**  visual fixes and many requests patched ([b9948](https://github.com/Afakto/afakto/commit/b994884c08802fc) Paul Comte)  
-  **credit-limit**  visual fixes and many requests patched ([3b3c6](https://github.com/Afakto/afakto/commit/3b3c6326b956da7) Paul Comte)  

### Bug Fixes

-  **app**  add missing buttons, change icon for submit on manual credit limit request ([ad646](https://github.com/Afakto/afakto/commit/ad646e66a6a8460) SKALSKI Alan)  

### Other changes

**feature(credit-limit):**

* - &#x27;Extras&#x27; icon in quasar.conf.js 
* - New icon for a partially accepted credit limit 
* - Button &quot;request credit limit&quot; greyed if the delivery is pending 

[4126a](https://github.com/Afakto/afakto/commit/4126a7a5522e068) Paul Comte *2025-04-17 10:19:38*

**Merge pull request #96 from paulcomte/insurer-credit-limit**

* Insurer credit limit 

[79265](https://github.com/Afakto/afakto/commit/79265293735a7ee) echarp *2025-04-16 19:56:49*

**Merge branch 'main' into insurer-credit-limit**


[5b81f](https://github.com/Afakto/afakto/commit/5b81f963b9a0111) echarp *2025-04-16 19:56:32*

**Merge branch 'main' into insurer-credit-limit**


[22d5b](https://github.com/Afakto/afakto/commit/22d5b70cffab98b) Paul COMTE *2025-04-16 01:00:51*


## ******** (2025-04-14)

### Features

-  **UI**  tabular numbers, for better alignment ([c2f33](https://github.com/Afakto/afakto/commit/c2f33f50f506886) echarp)  
-  **dashboard**  simplification and alignments ([cba15](https://github.com/Afakto/afakto/commit/cba15c7682dd25a) echarp)  

### Bug Fixes

-  **bankTransaction**  remove duplicate configure button ([b8466](https://github.com/Afakto/afakto/commit/b84668e8f2d0905) echarp)  
-  **datastream**  take only first BNP account statement balance ([59b31](https://github.com/Afakto/afakto/commit/59b315b833e8a55) echarp)  

### Dependency updates

- upgrade dependencies ([13036](https://github.com/Afakto/afakto/commit/130364b291c2707) echarp)  
## ******** (2025-04-11)

### Features

-  **security**  envers audit ([79c3a](https://github.com/Afakto/afakto/commit/79c3aaa184c27cf) echarp)  
-  **app**  create new buttons and replace old buttons on all pages ([5d273](https://github.com/Afakto/afakto/commit/5d273e60dace85a) SKALSKI Alan)  
-  **app**  add div with separators for buttons when brand and neutral buttons are together ([018b6](https://github.com/Afakto/afakto/commit/018b6c8fb21bcd5) SKALSKI Alan)  
-  **buyer**  countries identifier list in coface api ([a3577](https://github.com/Afakto/afakto/commit/a3577bdd6f11fff) Paul Comte)  

### Bug Fixes

-  **UI**  correct Inter font filename, with upper case I ([469e7](https://github.com/Afakto/afakto/commit/469e75b9962c59e) echarp)  
-  **buyer**  change button 'edit' to 'submit' in credit limit request ([ba34e](https://github.com/Afakto/afakto/commit/ba34e81b6033735) SKALSKI Alan)  
-  **dashboard**  do not list buyers with 0 unavailable amount ([cf513](https://github.com/Afakto/afakto/commit/cf513eeb49f2c7c) echarp)  

### Other changes


## 20250410 (2025-04-10)

### Features

-  **dashboard**  top unavailable sum and total unavailable spacing for when tab is empty, labels for tabs ([ef7a1](https://github.com/Afakto/afakto/commit/ef7a1661c49b808) SKALSKI Alan)  
-  **dashboard**  change tabs labels and spacing/size ([b335b](https://github.com/Afakto/afakto/commit/b335bd75f4a7d99) SKALSKI Alan)  
-  **dashboard**  init amountOutstanding total request ([9baf4](https://github.com/Afakto/afakto/commit/9baf461493d4e34) SKALSKI Alan)  
-  **dashboard**  add sum to outstanding & unavailable ([416d5](https://github.com/Afakto/afakto/commit/416d5d5653ebecb) SKALSKI Alan)  
-  **company**  created company added to current user perimeter ([f2c1b](https://github.com/Afakto/afakto/commit/f2c1b6165b88487) echarp)  
-  **buyers**  patched the customer reference when updating the credit limit ([b6b67](https://github.com/Afakto/afakto/commit/b6b6717079c23bc) Paul Comte)  
-  **buyers**  patched the customer reference when updating the credit limit ([d5a0e](https://github.com/Afakto/afakto/commit/d5a0e71a91c58ff) Paul Comte)  

### Bug Fixes

-  **cession**  creation for contract without overdue number ([f7aee](https://github.com/Afakto/afakto/commit/f7aeea58167e3bc) echarp)  
-  ensure stacktraces are logged in production ([aba0e](https://github.com/Afakto/afakto/commit/aba0e1761a9ca73) echarp)  
-  **dashboard**  fix outstanding/unavailable tab padding ([65496](https://github.com/Afakto/afakto/commit/65496b7b3de9781) SKALSKI Alan)  
-  **dashboard**  remove unused requests lines, fix donut display bug on reload ([406f6](https://github.com/Afakto/afakto/commit/406f66e4819d6ac) SKALSKI Alan)  

### Other changes

**Merge pull request #89 from paulcomte/buyer-features**

* Buyer features 

[209e3](https://github.com/Afakto/afakto/commit/209e346bd37bb14) echarp *2025-04-10 08:07:47*

**Merge branch 'main' into buyer-features**


[09612](https://github.com/Afakto/afakto/commit/096122c89d19622) Paul COMTE *2025-04-09 23:57:49*

**refactor(buyers) refactored buyer enrichment**


[b406d](https://github.com/Afakto/afakto/commit/b406d5c251aa787) Paul Comte *2025-04-09 10:38:53*

**[buyer-features 560e50d6] refactor(buyer): improved code visibility**

* - Removed LegalEntity 
* - Removed siren.service 
* - Merged enrichment features into same buyer resource 

[5c195](https://github.com/Afakto/afakto/commit/5c19519fffb906e) Paul Comte *2025-04-09 10:38:53*

**[buyer-features 560e50d6] refactor(buyer): improved code visibility**

* - Removed LegalEntity 
* - Removed siren.service 
* - Merged enrichment features into same buyer resource 

[01f75](https://github.com/Afakto/afakto/commit/01f75350a0dc0c7) Paul Comte *2025-04-09 10:26:56*

**refactor(buyers) refactored buyer enrichment**


[d275b](https://github.com/Afakto/afakto/commit/d275b1634981b4f) Paul Comte *2025-04-09 08:19:51*


## ******** (2025-04-08)

### Features

-  **bankTransaction**  remove value date column ([63d58](https://github.com/Afakto/afakto/commit/63d589a3007817e) echarp)  
-  **credit-limit**  requested amount field shown ([2eac1](https://github.com/Afakto/afakto/commit/2eac16566946529) Paul Comte)  
-  **buyers**  buyer information retrieved by VAT number ([a88b7](https://github.com/Afakto/afakto/commit/a88b78f826dc4f5) Paul Comte)  

### Bug Fixes

-  **credit-limit**  quick correction on coface and requested amount ([af6de](https://github.com/Afakto/afakto/commit/af6de1204c1cee3) echarp)  
-  **font-src**  add google fonts to authorized sources - 3 ([60701](https://github.com/Afakto/afakto/commit/60701d58e675910) echarp)  
-  **font-src**  add google fonts to authorized sources - 2 ([9463d](https://github.com/Afakto/afakto/commit/9463d687fa8a2f2) echarp)  
-  **font-src**  add google fonts to authorized sources ([fb257](https://github.com/Afakto/afakto/commit/fb257e31dbb7c58) echarp)  

### Other changes

**Update src/main/webapp/src/pages/entities/buyer/BuyerDetails.vue**


[f6bed](https://github.com/Afakto/afakto/commit/f6bed4349bd0d4a) echarp *2025-04-08 17:29:45*


## ******** (2025-04-06)

### Features

-  **credit-limit**  simplify credit limit screen ([45d3c](https://github.com/Afakto/afakto/commit/45d3cd5e7e53464) echarp)  
-  **bankTransaction**  rename accounting fields ([3f573](https://github.com/Afakto/afakto/commit/3f5731928c16c24) echarp)  
-  **credit-limit**  CreditLimitRequest.orderCode is replaced by the deliveryId after state update ([9a637](https://github.com/Afakto/afakto/commit/9a637ee51e32907) Paul Comte)  
-  **credit-limit**  Added requestCode and requestedAmount to CreditLimitRequest ([c0aef](https://github.com/Afakto/afakto/commit/c0aef377b966057) Paul Comte)  
-  **credit-limit**  Database update and fixed various behaviors ([4146d](https://github.com/Afakto/afakto/commit/4146d16a1b1f2e8) Paul Comte)  
-  **credit-limit**  fixed contract targeting all companies ([a8b2d](https://github.com/Afakto/afakto/commit/a8b2ddec5c95cca) Paul Comte)  
-  **ux**  new card design for all except situation bar ([d2e4f](https://github.com/Afakto/afakto/commit/d2e4f3fa1a17e72) SKALSKI Alan)  
-  **dashboard**  design situation bar cards with correct items placement, optimize horizontal space on dashboard ([4f071](https://github.com/Afakto/afakto/commit/4f0711453f7bdaa) SKALSKI Alan)  
-  **dashboard**  new cards design on situation bar ([0a024](https://github.com/Afakto/afakto/commit/0a0242ab2377c55) SKALSKI Alan)  
-  **jdl**  update entities and relationships ([32a99](https://github.com/Afakto/afakto/commit/32a99c5f3fe9d11) echarp)  
-  **credit-limit**  fixed current credit limit information display ([dafdb](https://github.com/Afakto/afakto/commit/dafdb67ad971ceb) Paul Comte)  
-  **buyers**  updated credit limit buttons and display ([8dfca](https://github.com/Afakto/afakto/commit/8dfca9798216afe) Paul Comte)  
-  **credit-limit**  refactoring and title ([651f8](https://github.com/Afakto/afakto/commit/651f8cc8916c4d6) Paul Comte)  
-  **credit-limit**  various fixes and refactoring: ([4876c](https://github.com/Afakto/afakto/commit/4876c758a8f53c3) Paul Comte)  
-  **credit-limit**  fixed update credit limit workflow ([1dafc](https://github.com/Afakto/afakto/commit/1dafc34d4067609) Paul Comte)  
-  **credit-limit**  implemented auto apply for credit limit using coface ([1cd5b](https://github.com/Afakto/afakto/commit/1cd5b18be6cd3bb) Paul Comte)  
-  **credit-limit**  Renamed "Credit insurance" to "Insurer" ([15ab8](https://github.com/Afakto/afakto/commit/15ab89280368b99) Paul Comte)  
-  **credit-limit**  retrieve buyer easyNumber if known by coface ([10a18](https://github.com/Afakto/afakto/commit/10a18868fcabeca) Paul Comte)  
-  **app**  use inter font instead of roboto ([df39a](https://github.com/Afakto/afakto/commit/df39a6454268da8) SKALSKI Alan)  

### Bug Fixes

-  **cession**  corrected BNP debtor file ([3eba2](https://github.com/Afakto/afakto/commit/3eba21e2423730b) echarp)  

### Other changes

**Merge pull request #84 from paulcomte/credit-limit**

* feat(credit-limit) 

[d7e3e](https://github.com/Afakto/afakto/commit/d7e3e68fe48f6d1) echarp *2025-04-04 13:10:05*

**Merge branch 'main' into credit-limit**


[e0f57](https://github.com/Afakto/afakto/commit/e0f57bbe8617fe5) echarp *2025-04-04 13:08:44*

**Update src/main/java/com/afakto/service/insurer/InsurerCofaceService.java**

* Co-authored-by: echarp &lt;<EMAIL>&gt; 

[84e31](https://github.com/Afakto/afakto/commit/84e3146b9d6cd02) Paul COMTE *2025-04-03 09:58:42*


## 20250401 (2025-04-01)

### Features

-  **cession**  gaps better displayed ([bb90d](https://github.com/Afakto/afakto/commit/bb90d3f71a4c6f4) echarp)  

### Dependency updates

- upgrade dependencies, remove jacoco ([4af1c](https://github.com/Afakto/afakto/commit/4af1c3f435c3220) echarp)  
### Other changes

**upgrade merge of jhipster_upgrade branch into main**


[3391c](https://github.com/Afakto/afakto/commit/3391ca8b3dd89e9) echarp *2025-04-01 10:04:05*

**generated jhipster_upgrade using JHipster 8.10.0**


[d0dab](https://github.com/Afakto/afakto/commit/d0dab659c34d8d3) echarp *2025-04-01 09:53:36*

**reference merge of jhipster_upgrade branch into main**


[2d391](https://github.com/Afakto/afakto/commit/2d39174057a50b2) echarp *2025-04-01 09:53:25*

**generated jhipster_upgrade using JHipster 8.9.0**


[84178](https://github.com/Afakto/afakto/commit/8417864f81e5c50) echarp *2025-04-01 09:53:24*

**generated jhipster_upgrade using JHipster 8.9.0**


[e5078](https://github.com/Afakto/afakto/commit/e5078b5c3722b9b) echarp *2025-02-10 09:53:18*

**generated jhipster_upgrade using JHipster 8.8.0**


[1d038](https://github.com/Afakto/afakto/commit/1d038c2b359fde6) echarp *2025-02-10 09:53:07*

**generated jhipster_upgrade using JHipster 8.8.0**


[8d237](https://github.com/Afakto/afakto/commit/8d237dc23564700) echarp *2025-01-07 09:29:51*

**generated jhipster_upgrade using JHipster ^8.7.3**


[1e6b5](https://github.com/Afakto/afakto/commit/1e6b54efdf88a96) echarp *2025-01-07 09:29:37*


## 20250331 (2025-03-31)

### Features

-  **cession**  add activation date validation for submission ([02fad](https://github.com/Afakto/afakto/commit/02fada35038327d) echarp)  
-  **UI**  goodbye page image on the right ([ac178](https://github.com/Afakto/afakto/commit/ac178dbb5730c51) echarp)  
-  **contract**  hide credit insurance columns in ContractsList ([e1cec](https://github.com/Afakto/afakto/commit/e1cec4fc1a5641e) echarp)  
-  **contract**  relocate cashIn section in ContractDetails ([d3a20](https://github.com/Afakto/afakto/commit/d3a203ba6e718a9) echarp)  
-  **cession**  add eventual gap label ([225bd](https://github.com/Afakto/afakto/commit/225bd4805860ae2) echarp)  
-  **UI**  enhance branding in layouts and goodbye page ([007b1](https://github.com/Afakto/afakto/commit/007b1f71f887c32) echarp)  

## 20250329 (2025-03-29)

### Features

-  **logout**  enhance logout flow with goodbye page ([81b46](https://github.com/Afakto/afakto/commit/81b4660fc94500d) echarp)  
-  **user**  goodbye page ([10802](https://github.com/Afakto/afakto/commit/10802f46dfdbfc8) SKALSKI Alan)  
-  **UI**  enhance user settings and top right icons ([479d4](https://github.com/Afakto/afakto/commit/479d47de621020b) echarp)  

### Bug Fixes

-  **config**  use OAUTH_URI_MGMT in another place ([e58be](https://github.com/Afakto/afakto/commit/e58be13a1525c95) echarp)  
-  **config**  update issuer-uri to use OAUTH_URI_MGMT ([90c86](https://github.com/Afakto/afakto/commit/90c86f441b5155a) echarp)  
-  **dashboard**  handle blind covered buyers in availableToSell ([1beb5](https://github.com/Afakto/afakto/commit/1beb50151bec551) echarp)  

### Other changes

**Merge branch 'main' into env-prod**


[b9261](https://github.com/Afakto/afakto/commit/b9261107e5b427f) echarp *2025-03-27 21:34:15*


## 20250327 (2025-03-27)

### Features

-  **datastream**  improve import buyer address mapping logic ([ed31d](https://github.com/Afakto/afakto/commit/ed31d60ca094841) echarp)  
-  **buyer**  add dynamic fill-mask for number input ([77cbc](https://github.com/Afakto/afakto/commit/77cbc9c6ebbf568) echarp)  
-  **app**  update dark mode with new colors, refactor dark variables ([4a328](https://github.com/Afakto/afakto/commit/4a328a07c901a2f) SKALSKI Alan)  
-  **app**  add new colors variables and classes in quasar variables so we don't have to write css in each files ([2fcd2](https://github.com/Afakto/afakto/commit/2fcd2cdafb9aa98) SKALSKI Alan)  
-  **datastream**  add missing translations for headers ([1134c](https://github.com/Afakto/afakto/commit/1134ccc406e25ad) echarp)  
-  **contract**  update blind cover terminology ([28398](https://github.com/Afakto/afakto/commit/283989c588d51f4) echarp)  

### Bug Fixes

-  **datastream**  add null-safe checks for orgId rendering ([e0018](https://github.com/Afakto/afakto/commit/e0018bc641e4c8d) echarp)  
-  **dashboard**  fix contract situation cards height on small screen ([91820](https://github.com/Afakto/afakto/commit/9182012b152562e) SKALSKI Alan)  
-  **dashboard**  replace color old variables ([31bc0](https://github.com/Afakto/afakto/commit/31bc0f70504444d) SKALSKI Alan)  
-  **app**  change lists color and update variables ([1e563](https://github.com/Afakto/afakto/commit/1e563b6c7566c20) SKALSKI Alan)  
-  **datastream**  replace remaining 'flow file' with 'datastream' in codebase ([4d514](https://github.com/Afakto/afakto/commit/4d514b70da5f937) echarp)  
-  **config**  use OAUTH_URI_MGMT in another place ([d557e](https://github.com/Afakto/afakto/commit/d557ee5ef04bac3) echarp)  
-  **config**  update issuer-uri to use OAUTH_URI_MGMT ([907d5](https://github.com/Afakto/afakto/commit/907d515a5fda9a9) echarp)  
-  **dashboard**  increase delay to ensure chart options are set correctly ([fdbce](https://github.com/Afakto/afakto/commit/fdbceaca979e3cc) echarp)  

### Dependency updates

- update dependencies ([726c1](https://github.com/Afakto/afakto/commit/726c127e9d2c3d2) echarp)  
### Other changes

**remove blank line**


[d23a7](https://github.com/Afakto/afakto/commit/d23a7b8d35fdebf) SKALSKI Alan *2025-03-27 09:32:21*


## 20250323 (2025-03-24)

### Other changes

**refactor(defaultCreditLimitAmount) renamed into blindCoverAmount**


[6f1e8](https://github.com/Afakto/afakto/commit/6f1e889e5702914) Paul Comte *2025-03-24 08:55:45*

**Merge pull request #73 from paulcomte/datastream**

* Refactor FlowFile to Datastream 

[d6cd9](https://github.com/Afakto/afakto/commit/d6cd997d2670121) echarp *2025-03-24 08:55:10*

**Merge branch 'main' into datastream**


[202b7](https://github.com/Afakto/afakto/commit/202b70cd2e1786a) echarp *2025-03-24 08:54:51*

**refactor(FlowFile) renamed mixed up name Dataservice to Datastream**


[e4c92](https://github.com/Afakto/afakto/commit/e4c928e13615b17) Paul Comte *2025-03-21 04:24:49*

**refactor(FlowFile) renamed FlowFile tests and main to datastream**


[769da](https://github.com/Afakto/afakto/commit/769da3c0a4668bc) Paul Comte *2025-03-19 07:58:17*

**refactor(FlowFile) renamed FlowFileFailure to DataStreamFailure**


[d85d1](https://github.com/Afakto/afakto/commit/d85d14a6f2fc028) Paul Comte *2025-03-19 07:36:38*

**refactor(FlowFile) renamed variables to datastream**


[a20c3](https://github.com/Afakto/afakto/commit/a20c39a6d8bec50) Paul Comte *2025-03-19 07:30:01*

**refactor(FlowFile) updated CessionDTO field flowFiles to dataStreams**


[18961](https://github.com/Afakto/afakto/commit/18961718aabe32e) Paul Comte *2025-03-19 07:10:58*

**refactor(FlowFile) FlowFile repository to Datastream repository**


[81449](https://github.com/Afakto/afakto/commit/81449357da9c825) Paul Comte *2025-03-19 06:50:19*

**refactor(FlowFile) updated frontend variables name**


[fe5f7](https://github.com/Afakto/afakto/commit/fe5f7cc2450a83f) Paul Comte *2025-03-19 05:43:43*

**refactor(FlowFile) updated translations**


[dc68f](https://github.com/Afakto/afakto/commit/dc68f8861b229b9) Paul Comte *2025-03-19 05:36:17*

**refactor(FlowFile) renamed routes to Datastream**


[18f99](https://github.com/Afakto/afakto/commit/18f99e6fcfb0b81) Paul Comte *2025-03-19 05:23:24*


## 20250321 (2025-03-21)

### Features

-  **dashboard**  move contract situation and fuse with factor debt ([dc4dd](https://github.com/Afakto/afakto/commit/dc4ddb72772147a) SKALSKI Alan)  
-  **dashboard**  add generate new cession button and replace positive color variable ([08bb4](https://github.com/Afakto/afakto/commit/08bb469e5af7be6) SKALSKI Alan)  
-  **cession**  gap management ([6c923](https://github.com/Afakto/afakto/commit/6c923f21e1aeb22) echarp)  
-  **cession**  optimize SG debtor file address handling ([295b4](https://github.com/Afakto/afakto/commit/295b40c190f14d0) echarp)  
-  **companies**  fixed deleteFromAllPerimeters request ([9155e](https://github.com/Afakto/afakto/commit/9155e665e202062) Paul Comte)  
-  **companies**  improved code performance and readability on delete action ([6c36d](https://github.com/Afakto/afakto/commit/6c36d4411ee7ec2) Paul Comte)  
-  **companies**  delete button now deleting even with user associated or comment ([6777e](https://github.com/Afakto/afakto/commit/6777e1cc6f477ec) Paul Comte)  
-  **companies**  company address from VAT iso tag ([ed750](https://github.com/Afakto/afakto/commit/ed75035d4150623) Paul Comte)  
-  **buyers**  updated div tag for 'created date' ([8243a](https://github.com/Afakto/afakto/commit/8243a2be60cb1c2) Paul Comte)  
-  **buyers**  remove time for 'created date' + show as hover ([2b61f](https://github.com/Afakto/afakto/commit/2b61fc56b3d0ff7) Paul Comte)  

### Bug Fixes

-  **dashboard**  associate each color with the label related ([8fe23](https://github.com/Afakto/afakto/commit/8fe23a818052648) SKALSKI Alan)  
-  **dashboard**  historical withdran debt width bug fix ([51728](https://github.com/Afakto/afakto/commit/517284f831b2645) SKALSKI Alan)  
-  **dashboard**  fix dark mode for new contract situation cards ([2e4e6](https://github.com/Afakto/afakto/commit/2e4e6b6e2508ff5) SKALSKI Alan)  
-  **cession**  handle null CreditInsurancePolicy in SG debtor file ([5982e](https://github.com/Afakto/afakto/commit/5982e2e4a60be40) echarp)  
-  **buyer**  better country names tooltip ([e8ab4](https://github.com/Afakto/afakto/commit/e8ab42c0ee3c5b3) echarp)  

### Other changes

**remove blank line**


[ee021](https://github.com/Afakto/afakto/commit/ee0211cdc574247) SKALSKI Alan *2025-03-21 22:14:32*

**(fix(dashboard): fix contract situation height issue**


[59bde](https://github.com/Afakto/afakto/commit/59bdea064ccb824) SKALSKI Alan *2025-03-21 22:14:32*

**Merge branch 'main' into gap**


[cdefd](https://github.com/Afakto/afakto/commit/cdefdd7f3dfb34c) echarp *2025-03-21 22:02:42*

**Merge pull request #75 from paulcomte/buyer-create-date**

* feat(buyers): remove time for &#x27;created date&#x27; + show as hover 

[4d4f0](https://github.com/Afakto/afakto/commit/4d4f0d838f0f3bd) echarp *2025-03-21 08:59:21*

**Update src/main/webapp/src/pages/subcomponents/BuyersComp.vue**

* Co-authored-by: echarp &lt;<EMAIL>&gt; 

[eab5c](https://github.com/Afakto/afakto/commit/eab5cb687dd599c) Paul COMTE *2025-03-21 08:56:53*

**feat(npm script) updated npm run watch script with cypress**


[adede](https://github.com/Afakto/afakto/commit/adede8bb312d6f3) Paul Comte *2025-03-20 09:03:51*

**Merge branch 'main' into buyer-create-date**


[4a6c1](https://github.com/Afakto/afakto/commit/4a6c11bcaf6e816) Paul COMTE *2025-03-20 06:39:15*


## ******** (2025-03-19)

### Features

-  **buyer**  add country flag styling to BuyersList ([20f74](https://github.com/Afakto/afakto/commit/20f740c90fb4de3) echarp)  
-  **buyer**  improve BuyersComp.vue and LegalEntityInfoService ([bf5b6](https://github.com/Afakto/afakto/commit/bf5b62845a8692b) echarp)  
-  **bankTransaction**  add new code category for initial unavailable ([6a0ae](https://github.com/Afakto/afakto/commit/6a0aec61ea10cf3) echarp)  
-  **buyer**  improve buyer address display ([1e083](https://github.com/Afakto/afakto/commit/1e083570d21fa88) echarp)  

### Bug Fixes

-  **bankTransaction**  toggle sign for credit/debit in generateOffsetEntry ([abc70](https://github.com/Afakto/afakto/commit/abc70b92bd9f0ae) echarp)  

## ******** (2025-03-14)

### Features

-  **bankTransaction**  handle complex ledger account and offset generation ([dff4b](https://github.com/Afakto/afakto/commit/dff4b587119d0a1) echarp)  
-  **companies**  greyed-out delete button if company has associated data ([1784d](https://github.com/Afakto/afakto/commit/1784d58a4cb6a6f) Paul Comte)  

### Bug Fixes

-  **user**  implements user component pagination management on remaining lists ([8f486](https://github.com/Afakto/afakto/commit/8f4866480c56e84) SKALSKI Alan)  

## ******** (2025-03-13)

### Features

-  **companies**  buyers category can now select the address of a buyer and update it ([2dba1](https://github.com/Afakto/afakto/commit/2dba1b6e6bbbf99) Paul Comte)  
-  **list**  sticky footer and create new table scss file for upcoming changes ([071af](https://github.com/Afakto/afakto/commit/071af2020158eb7) SKALSKI Alan)  
-  **user**  centralize all lists pagination in top right user component ([e5ab6](https://github.com/Afakto/afakto/commit/e5ab6f33779d410) SKALSKI Alan)  
-  **user**  add lists rows management in user component ([1f07f](https://github.com/Afakto/afakto/commit/1f07f0e40e3317b) SKALSKI Alan)  
-  **datastream**  transform Windows files to Unix format ([3059c](https://github.com/Afakto/afakto/commit/3059c950111d651) echarp)  
-  **companies**  buyers category with list of buyers and dialog to set address ([c2632](https://github.com/Afakto/afakto/commit/c2632a7f4df47f3) Paul Comte)  
-  **buyer**  manage buyer's currency in BuyerQueryService ([5651c](https://github.com/Afakto/afakto/commit/5651c062335239f) echarp)  

### Other changes

**test(cypress) added new package.json script npm run cypress**


[b3b5a](https://github.com/Afakto/afakto/commit/b3b5aba1933d30d) Paul Comte *2025-03-13 16:09:54*

**bug(address) removed clearable from country**


[f7a07](https://github.com/Afakto/afakto/commit/f7a077c31593761) Paul Comte *2025-03-13 16:09:33*

**remove individual pagination management on lists**


[d4734](https://github.com/Afakto/afakto/commit/d473406c0748778) SKALSKI Alan *2025-03-12 16:16:12*


## 20250311 (2025-03-11)

### Features

-  **authentication**  remove unnecessary iframes from Error401.vue ([9e664](https://github.com/Afakto/afakto/commit/9e6640ef02608f8) echarp)  
-  **buyer**  add buyerFromFactorUnknown field ([9a14c](https://github.com/Afakto/afakto/commit/9a14c2a2a170349) echarp)  
-  **auth0**  added bypass auth0 capability ([5ab22](https://github.com/Afakto/afakto/commit/5ab227d9c375011) Paul Comte)  
-  **func test**  cypress cession test WIP company creation done ([a96ef](https://github.com/Afakto/afakto/commit/a96ef4bcfd60375) Paul Comte)  
-  **func test**  cypress setup with simple dashboard access ([02706](https://github.com/Afakto/afakto/commit/027062b5f336243) Paul Comte)  

### Bug Fixes

-  **tests**  add dbms attribute to changeSets for PostgreSQL ([b9e72](https://github.com/Afakto/afakto/commit/b9e72492f985b31) echarp)  
-  **bankTransaction**  correct category for code 1605 ([394aa](https://github.com/Afakto/afakto/commit/394aa2bacddb984) echarp)  

### Dependency updates

- update dependencies ([b4963](https://github.com/Afakto/afakto/commit/b4963efb05abc71) echarp)  
- update dependencies ([73140](https://github.com/Afakto/afakto/commit/73140f6c6974584) echarp)  
- bump vue-i18n in the npm_and_yarn group across 1 directory ([37c83](https://github.com/Afakto/afakto/commit/37c83765fcc83d5) dependabot[bot])  
### Other changes

**feat(has cover) invoices and buyers list hasCover filter following hasCreditLimit mechanisms**


[e79e3](https://github.com/Afakto/afakto/commit/e79e3f04d2b8062) Paul Comte *2025-03-11 09:16:19*

**Merge pull request #57 from paulcomte/testing**

* CI/CD on cypress 

[1a3a0](https://github.com/Afakto/afakto/commit/1a3a06856fdd191) echarp *2025-03-07 08:23:17*

**Merge branch 'main' into testing**


[7dcd0](https://github.com/Afakto/afakto/commit/7dcd0d5c5cb5f77) Paul COMTE *2025-03-06 08:49:02*

**test(cypress) CI/CD workflow file WIP**


[81fe5](https://github.com/Afakto/afakto/commit/81fe5bc186eaa8a) Paul Comte *2025-03-06 08:46:23*

**test(cypress) simple cession scenario + auto delete after tasks**


[ce7f0](https://github.com/Afakto/afakto/commit/ce7f0cf287b27cc) Paul Comte *2025-03-05 07:17:40*

**feat(cypress docs) added cypress documentation to run a test**


[2fe6c](https://github.com/Afakto/afakto/commit/2fe6cea0806bd8d) Paul Comte *2025-03-04 17:50:08*


## 20250306 (2025-03-06)

### Features

-  **userComp**  move logout logic to user top right component ([f9f60](https://github.com/Afakto/afakto/commit/f9f602a9771697e) SKALSKI Alan)  
-  **dashboard**  change dropdown icon and elements order ([27bbf](https://github.com/Afakto/afakto/commit/27bbfd34d12df32) SKALSKI Alan)  

### Bug Fixes

-  **authentication**  handle internal notify call in OrgIdAspect ([50638](https://github.com/Afakto/afakto/commit/50638d19df98861) echarp)  
-  **userComp**  fix style and refactor code ([c1496](https://github.com/Afakto/afakto/commit/c149600f533a46b) SKALSKI Alan)  
-  **dashboard**  potential funding: earliest created_date for latest transaction ([5bff3](https://github.com/Afakto/afakto/commit/5bff3db84c98524) echarp)  

### Other changes

**refactor(bins) deleted duplicate maven config**


[e1be0](https://github.com/Afakto/afakto/commit/e1be0de3363cc37) Paul Comte *2025-03-06 08:36:42*

**refactor(bin) renamed bins/ to bin/**


[6250b](https://github.com/Afakto/afakto/commit/6250bb4f0251ae9) Paul Comte *2025-03-06 08:36:42*

**refactor(bins) created a binary folder and moved binaries there**


[50191](https://github.com/Afakto/afakto/commit/5019139df12d38e) Paul Comte *2025-03-06 08:36:42*


## ******** (2025-03-05)

### Features

-  **flowFile**  add company notification on no invoice import ([da9c2](https://github.com/Afakto/afakto/commit/da9c2cc7fe895e8) echarp)  
-  **docs**  updated documentation + INSTALL.md and concept into README ([cc5b2](https://github.com/Afakto/afakto/commit/cc5b27c3078df02) Paul Comte)  

### Bug Fixes

-  **bankTransaction**  properly calculate balances ([f2c16](https://github.com/Afakto/afakto/commit/f2c1658b5fe0017) echarp)  
-  **comments**  refine comments perimeter ([aa071](https://github.com/Afakto/afakto/commit/aa07191faf2171f) SKALSKI Alan)  

### Dependency updates

- update dependencies ([3f2b1](https://github.com/Afakto/afakto/commit/3f2b192c8ed4e29) echarp)  
### Other changes

**test(cypress) simple cession scenario + auto delete after tasks**


[84cee](https://github.com/Afakto/afakto/commit/84ceec755959a3e) Paul Comte *2025-03-05 10:28:01*


## ******** (2025-03-03)

### Features

-  **contract**  daily credit limit update ([b654b](https://github.com/Afakto/afakto/commit/b654b5b2bc42d40) echarp)  

### Bug Fixes

-  **authentication**  make login params optional to prevent 400 error ([6e04d](https://github.com/Afakto/afakto/commit/6e04d07deaf054a) echarp)  
-  **store**  restore logout action in authentication store ([dd991](https://github.com/Afakto/afakto/commit/dd991f5e75da675) echarp)  

### Other changes

**fix(favicon) fixed favicon layout**


[95203](https://github.com/Afakto/afakto/commit/95203d7e5fb748f) Paul Comte *2025-03-03 12:03:49*


## 20250228 (2025-02-28)

### Bug Fixes

-  **flowFile**  correct SG file names and country code extraction ([e30ca](https://github.com/Afakto/afakto/commit/e30cac08aefbb75) echarp)  
-  **authentication**  simplify auto logout management and update session timeout to avoid error when logging back ([7c531](https://github.com/Afakto/afakto/commit/7c53188f0bd5e3a) SKALSKI Alan)  
-  **flowFile**  add missing fields in SG debtor export ([841fc](https://github.com/Afakto/afakto/commit/841fcfae94a5d61) echarp)  
-  **dashboard**  adjust historical withdrawn debt to six months ([1d760](https://github.com/Afakto/afakto/commit/1d76018483d1c0e) echarp)  

### Other changes

**Update README.md**

* - add introduction to the project 
* - add mac os commands 

[c42c9](https://github.com/Afakto/afakto/commit/c42c96a1ec35684) alanskal *2025-02-28 12:17:31*


## 20250227 (2025-02-27)

### Features

-  **flowFile**  add header to BNP debtor export and refactor field extraction ([d9d4d](https://github.com/Afakto/afakto/commit/d9d4de6cb960262) echarp)  

### Bug Fixes

-  **dashboard**  fix select currency refreshing bug ([deaef](https://github.com/Afakto/afakto/commit/deaefa4452276e9) SKALSKI Alan)  
-  **i18n**  replace 'compagnie' with 'entreprise' in others lines ([32a76](https://github.com/Afakto/afakto/commit/32a7683cff34edf) SKALSKI Alan)  
-  **authentication**  refactor code for auto-logout ([b11ac](https://github.com/Afakto/afakto/commit/b11ac8fefe6e7b5) SKALSKI Alan)  

### Other changes

**Merge pull request #46 from alanskal/auto-logout-2**

* Auto logout 2 

[479a7](https://github.com/Afakto/afakto/commit/479a7363ec02313) echarp *2025-02-27 13:45:48*

**Merge branch 'main' of https://github.com/Afakto/afakto**


[eac81](https://github.com/Afakto/afakto/commit/eac81c590eb9994) SKALSKI Alan *2025-02-27 09:18:24*

**Merge branch 'main' of https://github.com/Afakto/afakto into auto-logout**


[3c7bc](https://github.com/Afakto/afakto/commit/3c7bccba366d532) SKALSKI Alan *2025-02-26 17:21:38*


## 20250226 (2025-02-26)

### Features

-  **flowFile**  reset buyers' factor data upon outstanding import ([288b5](https://github.com/Afakto/afakto/commit/288b518f3e9dea4) echarp)  
-  **auto-logout**  implements auto-logout after 15 min ([1581e](https://github.com/Afakto/afakto/commit/1581eba1b85a53e) SKALSKI Alan)  
-  **buyer**  country name in tooltip ([e67b1](https://github.com/Afakto/afakto/commit/e67b122e9a49e93) echarp)  

### Bug Fixes

-  **dashboard**  remove factor debt multiple currency display ([deeed](https://github.com/Afakto/afakto/commit/deeed95cffd2fd3) echarp)  
-  **buyer**  country column before name ([b5b32](https://github.com/Afakto/afakto/commit/b5b32f3800abf4b) echarp)  
-  remove white spaces in auto-logout code ([4fcae](https://github.com/Afakto/afakto/commit/4fcae3c565d6b1b) SKALSKI Alan)  
-  **i18n**  replace in french "compagnies" by "entreprises" ([763be](https://github.com/Afakto/afakto/commit/763be3627918443) SKALSKI Alan)  
-  **auto-logout**  set base timer to 30 min ([2d0bd](https://github.com/Afakto/afakto/commit/2d0bdafd42177c3) SKALSKI Alan)  
-  **dashboard**  update context parameters in router links ([b12dd](https://github.com/Afakto/afakto/commit/b12dd900db21da5) echarp)  

### Other changes

**Merge pull request #45 from alanskal/auto-logout**

* Auto logout 

[25c87](https://github.com/Afakto/afakto/commit/25c872fa787fc01) echarp *2025-02-26 19:05:22*

**Merge branch 'main' into auto-logout**


[acca9](https://github.com/Afakto/afakto/commit/acca9df4bccc2d8) alanskal *2025-02-26 17:34:57*

**remove war and lighthouse README.md**


[b94ef](https://github.com/Afakto/afakto/commit/b94ef6d5431ebe5) alanskal *2025-02-26 16:40:43*


## 20250225 (2025-02-25)

### Features

-  **buyer**  add country filter and display in buyer list ([e99c7](https://github.com/Afakto/afakto/commit/e99c7a098071877) echarp)  
-  **cession**  add BNP debtor export job and optimize utils ([b13db](https://github.com/Afakto/afakto/commit/b13db1ea39f3498) echarp)  

### Bug Fixes

-  **dashboard**  make cards clickable ([0947f](https://github.com/Afakto/afakto/commit/0947fc9c7a94702) echarp)  
-  **Comment**  prevent navigation on close button click ([41a6f](https://github.com/Afakto/afakto/commit/41a6f9d88b055b4) echarp)  

## 20250221 (2025-02-21)

### Features

-  add SG balance export job ([3f9ce](https://github.com/Afakto/afakto/commit/3f9ce7b65382b43) echarp)  
-  **dashboard**  init selected currency ([ccffb](https://github.com/Afakto/afakto/commit/ccffbdd62ab9a23) SKALSKI Alan)  
-  **dashboard**  select currency selection synchronized with related components ([53448](https://github.com/Afakto/afakto/commit/534484992f065a1) SKALSKI Alan)  
-  **dashboard**  init selectedCurrency ([3cc01](https://github.com/Afakto/afakto/commit/3cc01f4ccf96fd8) SKALSKI Alan)  
-  **dashboard**  remove unused lines ([0cc38](https://github.com/Afakto/afakto/commit/0cc386872aa02ad) SKALSKI Alan)  
-  **docs**  correct CRUD permissions for cession and flowFile ([bc296](https://github.com/Afakto/afakto/commit/bc29642c1e34fc5) echarp)  

### Bug Fixes

-  **user**  correctly delete all user's notifications on deletion ([31f38](https://github.com/Afakto/afakto/commit/31f38cedddaf44a) echarp)  

### Dependency updates

- upgrade dependencies and optimize notifyError ([f1f78](https://github.com/Afakto/afakto/commit/f1f78e80685c5be) echarp)  
### Other changes

**Update README.md**


[93c02](https://github.com/Afakto/afakto/commit/93c0265ae66dd38) alanskal *2025-02-20 19:32:43*

**Update README.md**

* update readme without navigation workflow for now 

[4fa66](https://github.com/Afakto/afakto/commit/4fa66a156f4f79c) alanskal *2025-02-20 19:32:43*

**Update README.md**


[1189e](https://github.com/Afakto/afakto/commit/1189e4847218908) alanskal *2025-02-20 19:32:43*


## ******** (2025-02-19)

### Features

-  **notification**  change color to red when unarchived notification, fixed dark mode ([3f904](https://github.com/Afakto/afakto/commit/3f90496b5b4d1cd) SKALSKI Alan)  
-  **dashboard**  update dashboard translations ([5ba32](https://github.com/Afakto/afakto/commit/5ba32634c00cfec) echarp)  

### Bug Fixes

-  **user**  purge comment notifications on user deletion ([2ee79](https://github.com/Afakto/afakto/commit/2ee79a24358a8f9) echarp)  
-  **bankTransaction**  change sort order to ascending by createdDate ([e88ad](https://github.com/Afakto/afakto/commit/e88adededc2f825) echarp)  
-  **bankTransaction**  change sort order to ascending by createdDate ([82870](https://github.com/Afakto/afakto/commit/8287087d216f1e9) echarp)  

### Other changes


## ******** (2025-02-18)

### Features

-  **user**  handle unique login and field-specific errors ([92fd4](https://github.com/Afakto/afakto/commit/92fd4cfbbbcd914) echarp)  

### Bug Fixes

-  **notifications**  handle 400 BAD_REQUEST caption properly ([532ee](https://github.com/Afakto/afakto/commit/532ee0b7194be53) echarp)  
-  **user**  initialize authorities array ([85bcb](https://github.com/Afakto/afakto/commit/85bcbcb84dec170) echarp)  
-  **buyer**  update policyId retrieval with currency ([88a91](https://github.com/Afakto/afakto/commit/88a91b593b9a03d) echarp)  
-  **buyer**  enhance null checks for product fields ([73d44](https://github.com/Afakto/afakto/commit/73d442719ae90e4) echarp)  
-  **dashboard**  add condition for unsecured amount ([f8a3d](https://github.com/Afakto/afakto/commit/f8a3d9beefced39) echarp)  

### Other changes


## 20250217 (2025-02-17)

### Features

-  **contract**  add credit insurance filters ([31c0a](https://github.com/Afakto/afakto/commit/31c0a33bb6acb55) echarp)  
-  **user**  enhance authorities selection UI ([6e22f](https://github.com/Afakto/afakto/commit/6e22f2ea8a28f2d) echarp)  
-  **docs**  update concepts and roles in documentation ([01be1](https://github.com/Afakto/afakto/commit/01be108f08f827a) echarp)  

### Bug Fixes

-  **pom**  improve readability by reformatting comments ([95816](https://github.com/Afakto/afakto/commit/95816dfec66e8d6) echarp)  
-  **dashboard**  correct condition in amountUnsecured query ([5d6f0](https://github.com/Afakto/afakto/commit/5d6f0b46d3f80ba) echarp)  

## 20250214 (2025-02-14)

### Features

-  **user**  add ROLE_READER and update role descriptions ([22a9d](https://github.com/Afakto/afakto/commit/22a9d05baf4427b) echarp)  
-  **docs**  update concepts with roles and access levels ([38dc3](https://github.com/Afakto/afakto/commit/38dc33fca82d0db) echarp)  

### Bug Fixes

-  **dashboard**  correct and optimize top unavailables query ([cbb7b](https://github.com/Afakto/afakto/commit/cbb7b3bf4283e14) echarp)  

### Dependency updates

- update dependencies ([4f8f8](https://github.com/Afakto/afakto/commit/4f8f87dd9ab2565) echarp)  
## 20250213 (2025-02-13)

### Features

-  **notification**  all notification tab ([1f217](https://github.com/Afakto/afakto/commit/1f2170296ba7f0a) SKALSKI Alan)  
-  **comment**  improve notification messages for BNP and Coface ([da3c7](https://github.com/Afakto/afakto/commit/da3c77a4131ff81) echarp)  
-  **cession**  add SG Debtor Export Job and related functionality ([56189](https://github.com/Afakto/afakto/commit/56189652c48fcb3) echarp)  
-  **notification**  init new notification dialog ([fe663](https://github.com/Afakto/afakto/commit/fe663718097ab2c) SKALSKI Alan)  
-  **buyer**  handle obsolete credit limits in InsurerCofaceService ([a577e](https://github.com/Afakto/afakto/commit/a577e6afafda07a) echarp)  
-  **buyer**  add Coface status translations and refactor Vue components ([74d0c](https://github.com/Afakto/afakto/commit/74d0c2134a14cba) echarp)  
-  **contract**  add ECB and IMF exchange rate info ([74764](https://github.com/Afakto/afakto/commit/74764ba7d8f52c7) echarp)  
-  **doc**  add Azure Vault and update icons ([668cd](https://github.com/Afakto/afakto/commit/668cd383be8086e) echarp)  
-  **doc**  update architecture diagram with icons ([07e6c](https://github.com/Afakto/afakto/commit/07e6c6fc4b1711b) echarp)  

### Bug Fixes

-  **flowFile**  move invoice balance reset to after import ([1433b](https://github.com/Afakto/afakto/commit/1433bc964d3a7a3) echarp)  
-  **notification**  fix notification display ([36a5c](https://github.com/Afakto/afakto/commit/36a5c287c5e599c) SKALSKI Alan)  
-  **notification**  fix broken navigation issue ([40859](https://github.com/Afakto/afakto/commit/408593bf34362cb) SKALSKI Alan)  
-  **buyer**  move buyer save operation after insurer data setup ([99a98](https://github.com/Afakto/afakto/commit/99a98f4fdd44649) echarp)  
-  **icons**  outlined icons ([4ea34](https://github.com/Afakto/afakto/commit/4ea347ee3e7d5a7) SKALSKI Alan)  
-  **invoice**  update buyer label in invoice.json ([b5268](https://github.com/Afakto/afakto/commit/b526876ae37998f) echarp)  

### Other changes

**Merge pull request #40 from alanskal/notification-display**

* Notification display 

[f4454](https://github.com/Afakto/afakto/commit/f4454842d3ac45b) echarp *2025-02-13 17:15:49*

**Merge branch 'main' into notification-display**


[394a1](https://github.com/Afakto/afakto/commit/394a1cc3d8b0189) echarp *2025-02-13 17:15:19*

**Merge branch 'main' into notification-display**


[4c470](https://github.com/Afakto/afakto/commit/4c470ca304aca32) alanskal *2025-02-13 17:03:54*

**format notification center**


[912d5](https://github.com/Afakto/afakto/commit/912d55d9e6dffde) SKALSKI Alan *2025-02-06 18:00:47*


## 20250211 (2025-02-11)

### Features

-  **contract**  manage sellable invoices using credit limits ([58685](https://github.com/Afakto/afakto/commit/586859dcfb5de97) echarp)  
-  **cession**  simplify due_date condition in queries ([956ea](https://github.com/Afakto/afakto/commit/956ea69e3b622e7) echarp)  
-  **notification**  init new notification dialog ([776c9](https://github.com/Afakto/afakto/commit/776c97c6d1fb6fd) SKALSKI Alan)  
-  **cession**  add support for Societe Generale factoring ([f307d](https://github.com/Afakto/afakto/commit/f307d34e0592eaf) echarp)  
-  **contract**  add "non specified coverage" default amount ([02feb](https://github.com/Afakto/afakto/commit/02feb3bad6eb9b4) echarp)  

### Bug Fixes

-  **tests**  update factor institution name in test ([2f4b7](https://github.com/Afakto/afakto/commit/2f4b73b64a1cf9b) echarp)  
-  **contract**  handle null endDate in InsurerCofaceService ([0a6a7](https://github.com/Afakto/afakto/commit/0a6a7d0ec51d4e7) echarp)  

### Dependency updates

- update dependencies ([0af89](https://github.com/Afakto/afakto/commit/0af891e283644e0) echarp)  
### Other changes

**upgrade merge of jhipster_upgrade branch into main**


[3f9c7](https://github.com/Afakto/afakto/commit/3f9c7185c17a68b) echarp *2025-02-10 09:59:42*

**reference merge of jhipster_upgrade branch into main**


[8feff](https://github.com/Afakto/afakto/commit/8feff670a463a07) echarp *2025-02-10 09:53:08*

**format notification center**


[821b9](https://github.com/Afakto/afakto/commit/821b95a83b1e77d) SKALSKI Alan *2025-02-07 16:44:20*


## ******** (2025-01-31)

### Bug Fixes

-  **contract**  remove unique constraint from policyId ([46914](https://github.com/Afakto/afakto/commit/4691421d8d33cf6) echarp)  
-  **bankTransaction**  remove null check in format ([e35a7](https://github.com/Afakto/afakto/commit/e35a7992d8b3a50) echarp)  

## ******** (2025-01-30)

### Features

-  **contract**  rename buyerId to customerId, auto-update credit limits upon setup ([05e53](https://github.com/Afakto/afakto/commit/05e539cc42c01e4) echarp)  

### Bug Fixes

-  **chart**  remove x axis line ([ebc25](https://github.com/Afakto/afakto/commit/ebc250dbea1b08c) SKALSKI Alan)  
-  **dashboard**  update unsecured amount query conditions ([4555d](https://github.com/Afakto/afakto/commit/4555d08a6914ac8) echarp)  

## ********_2 (2025-01-29)

### Bug Fixes

-  **flowFile**  ensure proper approved amount calculation ([c7008](https://github.com/Afakto/afakto/commit/c70085fc562c464) echarp)  
-  **UI**  handle null values in column formatting ([64e2b](https://github.com/Afakto/afakto/commit/64e2bd30cc0caea) echarp)  

## ******** (2025-01-29)

### Features

-  **flowFile**  improve notification message formatting ([df725](https://github.com/Afakto/afakto/commit/df72558a0dcd80e) echarp)  
-  **UI**  update import/export to outlined symbols ([aa904](https://github.com/Afakto/afakto/commit/aa9041ce4c8e212) echarp)  
-  **buyer**  amountFunded visible by default ([db5ff](https://github.com/Afakto/afakto/commit/db5ffc87cc13c02) echarp)  
-  **UI**  add material outlined icons ([35314](https://github.com/Afakto/afakto/commit/35314c04ceb3ae7) echarp)  
-  **flowFile**  manage BNP unknown buyers, with notification ([06640](https://github.com/Afakto/afakto/commit/066400920cfa38c) echarp)  
-  **flowFile**  add amount sign check in InvoiceRepository ([b0700](https://github.com/Afakto/afakto/commit/b0700febc287696) echarp)  
-  **invoice**  add new transpositions to InvoiceType enum ([cb0c5](https://github.com/Afakto/afakto/commit/cb0c521847c4f9d) echarp)  
-  **buyer**  add validation for buyer number and type ([7e8ed](https://github.com/Afakto/afakto/commit/7e8ed5f6b6b3f1c) echarp)  
-  **buyer**  handle multiple SIREN updates in BnpOutstandingImport ([750bc](https://github.com/Afakto/afakto/commit/750bc348130ad21) echarp)  

### Bug Fixes

-  **UI**  handle null and 0 amounts in BuyersList.vue ([3cd18](https://github.com/Afakto/afakto/commit/3cd18083af5c132) echarp)  
-  **UI**  correctly display 0 amounts in tables ([943b1](https://github.com/Afakto/afakto/commit/943b190fabbe4d2) echarp)  
-  **dashboard**  limit to 10 unavailable in DashboardRepository ([1c517](https://github.com/Afakto/afakto/commit/1c517871571bc44) echarp)  
-  **comment**  manage navigation to related entity ([4528f](https://github.com/Afakto/afakto/commit/4528f3826f6fb3e) echarp)  
-  **buyer**  improve duplicate buyer handling in CofaceService ([52242](https://github.com/Afakto/afakto/commit/522423474018373) echarp)  
-  **buyer**  cleaner error messages InsurerCofaceService ([4897d](https://github.com/Afakto/afakto/commit/4897de777b66f4b) echarp)  
-  **buyer**  add null checks and logging in CofaceService ([f4b1e](https://github.com/Afakto/afakto/commit/f4b1e4e4306a656) echarp)  

### Dependency updates

- update maven plugin versions ([723f5](https://github.com/Afakto/afakto/commit/723f5e22828177b) echarp)  
### Other changes


## ******** (2025-01-24)

### Features

-  **buyer**  fetch Coface insurance data ([71873](https://github.com/Afakto/afakto/commit/7187346e7308d05) echarp)  
-  **bankTransaction**  add new code category for current balance ([3c5cc](https://github.com/Afakto/afakto/commit/3c5cce81bae1324) echarp)  
-  **chart**  add line on y-axis and color comportment ([0f0bc](https://github.com/Afakto/afakto/commit/0f0bc44e1e15def) SKALSKI Alan)  

### Bug Fixes

-  **dashboard**  useless parenthesis ([e72d5](https://github.com/Afakto/afakto/commit/e72d5df9069819f) SKALSKI Alan)  
-  **dashboard**  follow typographic rules for english version and reduce font size in Top cards ([3e3ad](https://github.com/Afakto/afakto/commit/3e3adb431ff9861) SKALSKI Alan)  
-  **dashboard**  update FactorDebt chart options and tooltip positioning ([6c2c3](https://github.com/Afakto/afakto/commit/6c2c37f414eb7b9) SKALSKI Alan)  
-  **dashboard**  update FactorDebt chart options and tooltip positioning ([1e559](https://github.com/Afakto/afakto/commit/1e559de3508b318) SKALSKI Alan)  

### Other changes

**Merge pull request #35 from alanskal/chart**

* Chart 

[b51f0](https://github.com/Afakto/afakto/commit/b51f0e7aede2da5) echarp *2025-01-24 15:53:20*

**Merge branch 'main' into chart**


[1af0a](https://github.com/Afakto/afakto/commit/1af0a4419e1e4ad) alanskal *2025-01-24 14:06:35*


## 20250122 (2025-01-22)

### Features

-  **dashboard**  add Top Unavailables component and modify chart legend positioning ([664b4](https://github.com/Afakto/afakto/commit/664b4f02db838ad) SKALSKI Alan)  

### Bug Fixes

-  **contract**  improve exchange rates table rendering ([fe91a](https://github.com/Afakto/afakto/commit/fe91a3d7a920aa2) echarp)  
-  **dashboard**  add maximumFractionDigits to currency format ([e2c84](https://github.com/Afakto/afakto/commit/e2c84320f5e886d) echarp)  
-  **dashboard**  correct query for top unavailable buyers ([8e60e](https://github.com/Afakto/afakto/commit/8e60e823876710a) echarp)  

## 20250121 (2025-01-21)

### Features

-  **contract**  add exchange rates display to health page ([9ccd3](https://github.com/Afakto/afakto/commit/9ccd3dec7e44f1d) echarp)  
-  **dashboard**  add hyperlink with filtering criterias for YTD fees ([9092d](https://github.com/Afakto/afakto/commit/9092d4d9a512d1e) SKALSKI Alan)  

### Bug Fixes

-  **dashboard**  adjust fiscal year date calculation ([55f55](https://github.com/Afakto/afakto/commit/55f558dababaf29) echarp)  
-  **dashboard**  correct fiscal year fees card click handler ([a6a8d](https://github.com/Afakto/afakto/commit/a6a8dd85e8deb57) echarp)  

## 20250120 (2025-01-20)

### Features

-  **buyer**  handle enter key, chain delete, and remove payment terms ([cef34](https://github.com/Afakto/afakto/commit/cef348490639911) echarp)  
-  **flowFile**  allow updating buyers without buyer id and type ([d3190](https://github.com/Afakto/afakto/commit/d31906e28ba5f85) echarp)  
-  **dashboard**  add endpoint to fetch top unavailable invoices ([aa7f8](https://github.com/Afakto/afakto/commit/aa7f87e3c3881d4) echarp)  
-  **UI**  add empty value option to number filters ([e4e68](https://github.com/Afakto/afakto/commit/e4e689d9355a2c8) echarp)  
-  **flowFile**  add validation for buyer ID number ([16043](https://github.com/Afakto/afakto/commit/160432404a3468c) echarp)  
-  **security**  add initial security policy document ([5f2ce](https://github.com/Afakto/afakto/commit/5f2cefc5779f8a8) echarp)  

### Bug Fixes

-  **flowFile**  replace deprecated CSVFormat build method ([f06e5](https://github.com/Afakto/afakto/commit/f06e5d68af94356) echarp)  
-  **UI**  add empty value option to date filters ([8e157](https://github.com/Afakto/afakto/commit/8e1570d3a6777b1) echarp)  
-  **i18n**  update translations for flow files and addresses ([6b110](https://github.com/Afakto/afakto/commit/6b1106120bec72a) echarp)  

### Other changes


## 20250117 (2025-01-17)

### Features

-  **security**  add orgId to contract repository methods ([d2ff7](https://github.com/Afakto/afakto/commit/d2ff79a50303652) echarp)  
-  **app**  trying new font ([3ae33](https://github.com/Afakto/afakto/commit/3ae33960dc6d762) SKALSKI Alan)  
-  **insurer**  formating and conditional rendering ([b8a3c](https://github.com/Afakto/afakto/commit/b8a3c01ebb4f149) SKALSKI Alan)  
-  **doc**  update README for clarity and consistency ([532a3](https://github.com/Afakto/afakto/commit/532a39e0e953880) echarp)  

### Bug Fixes

-  **security**  allow access to /img/** ([0640b](https://github.com/Afakto/afakto/commit/0640b6ce8181064) echarp)  
-  **test**  correct orgId in basicBankTransaction test ([431c6](https://github.com/Afakto/afakto/commit/431c63c60e21690) echarp)  
-  **app**  logo alignement ([3b668](https://github.com/Afakto/afakto/commit/3b668c98ce3a8a2) SKALSKI Alan)  
-  **layout**  add padding to the logo title for improved spacing ([b954e](https://github.com/Afakto/afakto/commit/b954ee7e02f808b) SKALSKI Alan)  
-  **insurer**  removed time from dates rows ([2feb8](https://github.com/Afakto/afakto/commit/2feb8110f22d162) SKALSKI Alan)  
-  **app**  re-added roboto font ([80e0a](https://github.com/Afakto/afakto/commit/80e0ac10ca973f9) SKALSKI Alan)  
-  correct SQL syntax for altering PostgreSQL user password ([07e48](https://github.com/Afakto/afakto/commit/07e4853a832546b) echarp)  
-  **bankTransaction**  correct category for code 9667 in CSV file ([3f9b6](https://github.com/Afakto/afakto/commit/3f9b6a0950b8570) echarp)  

### Other changes

**désolé**


[21f96](https://github.com/Afakto/afakto/commit/21f96f4f2b56440) SKALSKI Alan *2025-01-16 13:43:48*


## ******** (2025-01-10)

### Features

-  **bankTransaction**  add entity meta dates to CategoryToAccountDetails ([27b74](https://github.com/Afakto/afakto/commit/27b74c524e36d5e) echarp)  
-  **insurer**  added insurance essential infos with conditional rendering ([dfa49](https://github.com/Afakto/afakto/commit/dfa496901efb010) SKALSKI Alan)  
-  **i18n**  update translation strings ([027d6](https://github.com/Afakto/afakto/commit/027d67427a58804) echarp)  
-  add fiscal year fees to dashboard ([d3298](https://github.com/Afakto/afakto/commit/d329893c7d3c89d) echarp)  
-  **company**  add fiscal year start month field ([e22a2](https://github.com/Afakto/afakto/commit/e22a2a961f3c350) echarp)  

### Bug Fixes

-  **bankTransaction**  Company.categoryToAccounts wrongfully deleted ([de6db](https://github.com/Afakto/afakto/commit/de6db997bcf9a93) echarp)  
-  btn look ([d880f](https://github.com/Afakto/afakto/commit/d880f7bb690b4e9) SKALSKI Alan)  
-  add null checks for currency and financialInformation ([7d31b](https://github.com/Afakto/afakto/commit/7d31b976a724d97) echarp)  
-  correct calculation for historical factor debt ([0a612](https://github.com/Afakto/afakto/commit/0a612afdd262709) echarp)  
-  **i18n**  update fiscal year fees tooltip text ([c04e8](https://github.com/Afakto/afakto/commit/c04e8f2a004a451) echarp)  

### Other changes

**Merge pull request #25 from alanskal/formating-credit**

* feat(insurer): added insurance essential infos with conditional rende… 

[68664](https://github.com/Afakto/afakto/commit/68664ee68e04daa) echarp *2025-01-10 09:38:07*

**Merge branch 'main' into formating-credit**


[286b7](https://github.com/Afakto/afakto/commit/286b79926ddc07b) alanskal *2025-01-10 09:30:54*

**bCurrency bug, button box shadow and companirs select styling**


[bd880](https://github.com/Afakto/afakto/commit/bd8800cd35047da) SKALSKI Alan *2025-01-08 17:36:36*

**upgrade merge of jhipster_upgrade branch into main**


[71f02](https://github.com/Afakto/afakto/commit/71f023fe6bd3610) echarp *2025-01-07 10:25:34*

**reference merge of jhipster_upgrade branch into main**


[2d582](https://github.com/Afakto/afakto/commit/2d5821596979e2f) echarp *2025-01-07 09:29:39*


## 20250109 (2025-01-09)

### Features

-  **dashboard**  rename fiscal year start month to closing month ([b60f7](https://github.com/Afakto/afakto/commit/b60f704d66d3a58) echarp)  
-  **insurer**  update BuyerDetailsFromInsurerAtradius.vue ([4622e](https://github.com/Afakto/afakto/commit/4622e2dc7f90a50) echarp)  
-  update text styles and remove bold class ([b5407](https://github.com/Afakto/afakto/commit/b54074cd7344d83) echarp)  

### Bug Fixes

-  **comment**  remove admin notification logic ([60180](https://github.com/Afakto/afakto/commit/60180589fecdecb) echarp)  

### Other changes

**Merge pull request #24 from alanskal/fix-btn**

* fix: btn look 

[a675c](https://github.com/Afakto/afakto/commit/a675c2dc1ba1369) echarp *2025-01-09 13:52:10*

**Merge branch 'main' into fix-btn**


[9568f](https://github.com/Afakto/afakto/commit/9568f611a9bca0d) echarp *2025-01-09 13:51:42*


## 20250108 (2025-01-08)

## 20250106 (2025-01-06)

### Features

-  update BuyerDetailsFromInsurerAtradius formatting ([ade85](https://github.com/Afakto/afakto/commit/ade85cfbdb904c3) echarp)  
-  add mechanism to fetch and display insurer data ([480c7](https://github.com/Afakto/afakto/commit/480c7e8bd14f64d) echarp)  
-  add default filter credit limit excluded and due date for available to sell ([cf7a0](https://github.com/Afakto/afakto/commit/cf7a097f56d71a1) SKALSKI Alan)  
-  update dependencies and refactor path usage ([c18e2](https://github.com/Afakto/afakto/commit/c18e2eb7f72e877) echarp)  
-  refactor insurer services and update method visibility ([2015a](https://github.com/Afakto/afakto/commit/2015abe30506129) echarp)  
-  add InsurerAtradiusService for credit limits update ([aaf01](https://github.com/Afakto/afakto/commit/aaf01a1cf593ea1) echarp)  
-  **contract**  manage cash-in contracts in repository ([cc961](https://github.com/Afakto/afakto/commit/cc9618b0b46fa44) echarp)  
-  added the borderless property to filtering components and changed avatars sizes in dense mode ([c0f33](https://github.com/Afakto/afakto/commit/c0f336999b2ed43) SKALSKI Alan)  
-  dense mode change persistance and modes buttons styling ([92d3f](https://github.com/Afakto/afakto/commit/92d3fd34f09d78a) SKALSKI Alan)  
-  **contract**  reorder fields in Contract entity ([1a6eb](https://github.com/Afakto/afakto/commit/1a6eb0ab0683f6f) echarp)  
-  add dense mode styling to filtering and visibility components ([274a7](https://github.com/Afakto/afakto/commit/274a7bb299bbaae) SKALSKI Alan)  
-  add dense mode preference and corresponding UI toggle ([a45ba](https://github.com/Afakto/afakto/commit/a45baf41f70dc55) SKALSKI Alan)  
-  add insurer service and update contract details ([e00dd](https://github.com/Afakto/afakto/commit/e00ddc123ebf0d9) echarp)  
-  remove roboto-font from quasar config ([771e7](https://github.com/Afakto/afakto/commit/771e78d3bbc07fe) echarp)  
-  add dense mode preference and corresponding UI toggle ([afa0c](https://github.com/Afakto/afakto/commit/afa0c2b889b385d) SKALSKI Alan)  
-  add Organization entity and update related components ([8dcd2](https://github.com/Afakto/afakto/commit/8dcd2a2d72f5842) echarp)  
-  set project version to timestamp during packaging ([f8c9e](https://github.com/Afakto/afakto/commit/f8c9ecf13bd9c71) echarp)  
-  re-added prop filter to rows export ([b54eb](https://github.com/Afakto/afakto/commit/b54eb89b63ded58) SKALSKI Alan)  
-  add user notifications and comment notification deletion ([4b8d4](https://github.com/Afakto/afakto/commit/4b8d4de97edd245) echarp)  
-  return accepted credit limit request data ([e0a57](https://github.com/Afakto/afakto/commit/e0a57f2f9616b45) echarp)  
-  **repository**  add method to find buyer by orgId, companyCode, and code ([cce34](https://github.com/Afakto/afakto/commit/cce34f8a399b54c) echarp)  
-  **batch**  handle duplicate files in import job ([8d15b](https://github.com/Afakto/afakto/commit/8d15b353580551c) echarp)  

### Bug Fixes

-  **build**  change build timestamp phase to package ([05853](https://github.com/Afakto/afakto/commit/058531fe9e17f37) echarp)  
-  correct option labels and values in UserDetails ([1b827](https://github.com/Afakto/afakto/commit/1b827a85666b491) echarp)  
-  **build**  change build timestamp phase to package ([77c8d](https://github.com/Afakto/afakto/commit/77c8d3bbd0c54a2) echarp)  
-  **ColumnsVisibility.vue**  indentation ([03645](https://github.com/Afakto/afakto/commit/03645a6050ee06f) SKALSKI Alan)  
-  **BCurrency.vue**  no currency label ([72f1f](https://github.com/Afakto/afakto/commit/72f1ff9dc3ed6bf) SKALSKI Alan)  
-  typo ([e9c5d](https://github.com/Afakto/afakto/commit/e9c5db2c00c1aad) SKALSKI Alan)  
-  typo ([75d30](https://github.com/Afakto/afakto/commit/75d30bf4d998445) SKALSKI Alan)  

### Other changes

**Merge pull request #22 from alanskal/avalaible-to-sell**

* feat: add default filter credit limit excluded and due date for available to sell 

[2a949](https://github.com/Afakto/afakto/commit/2a94904270e2a92) echarp *2025-01-06 11:28:28*

**Merge branch 'main' of https://github.com/Afakto/afakto into name-after**


[bfa36](https://github.com/Afakto/afakto/commit/bfa367698c720e7) SKALSKI Alan *2024-12-27 11:50:54*

**Merge pull request #19 from alanskal/dense-view**

* Dense view 

[05693](https://github.com/Afakto/afakto/commit/056939ef822c19e) echarp *2024-12-24 14:31:28*

**Merge branch 'main' into dense-view**


[b7905](https://github.com/Afakto/afakto/commit/b790580d53e748a) echarp *2024-12-24 14:30:51*

**Merge branch 'main' of https://github.com/Afakto/afakto into dense-view**


[a0167](https://github.com/Afakto/afakto/commit/a0167caccaf0a04) SKALSKI Alan *2024-12-24 10:43:38*

**Merge pull request #12 from alanskal/columns-values**

* Columns values 

[eda78](https://github.com/Afakto/afakto/commit/eda7826601eed99) echarp *2024-12-17 18:01:59*

**Missing repository method**


[5b40c](https://github.com/Afakto/afakto/commit/5b40c4534ab8f1c) echarp *2024-12-13 13:26:50*


## 0.52.3 (2024-12-12)

### Features

-  **SituationBar**  update card click action to navigate to invoices with specific query parameters ([6cb68](https://github.com/Afakto/afakto/commit/6cb6890af696a60) SKALSKI Alan)  
-  **BCurrency**  add option for 'none' in currency selection and update translations ([b490c](https://github.com/Afakto/afakto/commit/b490c3386769109) SKALSKI Alan)  

### Bug Fixes

-  **invoice**  correct balance comparison logic ([66761](https://github.com/Afakto/afakto/commit/66761b2f9aabbbf) echarp)  
-  **situationBar**  balance not equals 0 instead of amount ([c7da5](https://github.com/Afakto/afakto/commit/c7da50943220735) SKALSKI Alan)  
-  **SituationBar**  update invoice navigation query parameters to include context ([8cdcd](https://github.com/Afakto/afakto/commit/8cdcd60e955f5ca) SKALSKI Alan)  

### Other changes

**Merge pull request #8 from alanskal/dashboard-click**

* Dashboard click 

[ee341](https://github.com/Afakto/afakto/commit/ee34142673ffdd4) echarp *2024-12-12 14:26:30*

**Merge branch 'main' into dashboard-click**


[03041](https://github.com/Afakto/afakto/commit/030410536e564d6) alanskal *2024-12-12 14:24:52*


## 0.52.2 (2024-12-11)

### Bug Fixes

-  update invoice repository method signature ([be19c](https://github.com/Afakto/afakto/commit/be19c65e0e944b6) echarp)  
-  await loading allowed currencies on mount ([3c850](https://github.com/Afakto/afakto/commit/3c8507e1f88c088) echarp)  

## 0.52.1 (2024-12-11)

### Features

-  **filters**  trigger watcher and update filters handling ([84456](https://github.com/Afakto/afakto/commit/844567dcd64bd97) echarp)  

## 0.52.0 (2024-12-11)

### Features

-  **flowFileType**  update BNP labels and paths handling ([b286b](https://github.com/Afakto/afakto/commit/b286bab1e2d3cda) echarp)  
-  **buyer**  add initial filter for balance in invoices ([869e3](https://github.com/Afakto/afakto/commit/869e3a592cfcea4) echarp)  
-  **BCurrency**  add option for 'none' in currency selection and update translations ([fab3d](https://github.com/Afakto/afakto/commit/fab3d97a552a3e6) SKALSKI Alan)  
-  **BCurrency**  update currency selection to use 'null' instead of empty string and adjust translations ([f9ff9](https://github.com/Afakto/afakto/commit/f9ff9024858ac65) SKALSKI Alan)  
-  **BCurrency**  add 'None' option to reference currency translations ([37b2c](https://github.com/Afakto/afakto/commit/37b2c90b1e8220f) SKALSKI Alan)  
-  **CessionResource**  remove unused getCessionFile endpoint documentation ([99ca9](https://github.com/Afakto/afakto/commit/99ca9be68361894) SKALSKI Alan)  
-  **CessionDetails.vue**  **FlowFileDetails.vue**  refactor file download logic to use the same API route ([4fe6a](https://github.com/Afakto/afakto/commit/4fe6a3d2fbfa941) SKALSKI Alan)  
-  **CessionResource**  remove unused getCessionFile endpoint ([4d8a0](https://github.com/Afakto/afakto/commit/4d8a07e230653b0) SKALSKI Alan)  
-  **menu**  default invoice menu targets outstanding invoices ([360b7](https://github.com/Afakto/afakto/commit/360b7b702980e8c) echarp)  
-  **i18n**  update French translations for dashboard ([b541a](https://github.com/Afakto/afakto/commit/b541a535a64f5e5) echarp)  

### Bug Fixes

-  **webapp**  improve component reactivity and error handling ([77895](https://github.com/Afakto/afakto/commit/77895fb0996df95) echarp)  

### Other changes


## 0.51.3 (2024-12-10)

### Features

-  **errors**  enhance BadRequestAlertException handling ([62e8f](https://github.com/Afakto/afakto/commit/62e8f8da7027e76) echarp)  
-  **index**  add watch for preference changes ([ac37b](https://github.com/Afakto/afakto/commit/ac37b67db9eeef5) echarp)  
-  **user**  reload page on preferences update ([bbe21](https://github.com/Afakto/afakto/commit/bbe215eec40a8cd) echarp)  
-  **css**  separate dashboard styles into own file ([35d67](https://github.com/Afakto/afakto/commit/35d6781dabd2b2e) echarp)  

### Bug Fixes

-  **authentication**  improve locale handling and cleanup ([c9bc5](https://github.com/Afakto/afakto/commit/c9bc5c5a3cc26cb) echarp)  

### Other changes


## 0.51.2 (2024-12-10)

### Bug Fixes

-  **dashboard**  update balance calculation logic ([02a45](https://github.com/Afakto/afakto/commit/02a45cc7db7a2c6) echarp)  

## 0.51.1 (2024-12-09)

### Features

-  add detailed validation error messages ([cda6e](https://github.com/Afakto/afakto/commit/cda6e0f4d9a4efc) echarp)  

## 0.51.0 (2024-12-09)

### Features

-  **UserComp.vue**  update styles and improve dropdown ([b6a0b](https://github.com/Afakto/afakto/commit/b6a0b84859ca441) echarp)  
-  **user**  refactor dark mode toggle and update preferences handling ([8d2f8](https://github.com/Afakto/afakto/commit/8d2f8f357e85054) SKALSKI Alan)  
-  add amountUnsecured endpoint and update i18n ([54868](https://github.com/Afakto/afakto/commit/548687f6cfcb08e) echarp)  
-  **docs**  add entities diagram to concepts.md ([d8907](https://github.com/Afakto/afakto/commit/d8907222ec7c94f) echarp)  
-  add amountUnsecured field to Invoice entities ([addfe](https://github.com/Afakto/afakto/commit/addfeb24efbb4a9) echarp)  
-  copy generated cession files to archive ([dcb5b](https://github.com/Afakto/afakto/commit/dcb5b85a062f986) echarp)  
-  update project dependencies and plugins ([91fa6](https://github.com/Afakto/afakto/commit/91fa6c1ef97e7f2) echarp)  

### Bug Fixes

-  **docs**  standardize arrow direction in entities diagram ([e41af](https://github.com/Afakto/afakto/commit/e41afcda38a3378) echarp)  
-  set visibleColumns to empty array by default ([ba77e](https://github.com/Afakto/afakto/commit/ba77e6ab14e85e1) echarp)  

### Dependency updates

- update checkstyle and javadoc plugin versions ([0a1ec](https://github.com/Afakto/afakto/commit/0a1ecb0f924bb78) echarp)  
### Other changes

**Merge branch 'main' into unavailable**


[143d4](https://github.com/Afakto/afakto/commit/143d4f5ba3303be) echarp *2024-12-09 09:53:29*

**Update concepts.md**

* Add an entities&#x27; graph 

[07e7d](https://github.com/Afakto/afakto/commit/07e7d5b0a8af348) echarp *2024-11-28 14:49:47*

**Merge pull request #1 from alanskal/main**

* First commit 

[774c1](https://github.com/Afakto/afakto/commit/774c1edb0844298) echarp *2024-11-27 15:38:53*

**Merge branch 'main' of https://github.com/Afakto/afakto**


[651b7](https://github.com/Afakto/afakto/commit/651b7c48cb13d20) SKALSKI Alan *2024-11-27 15:20:18*


## 0.50.1 (2024-11-22)

### Features

-  add exclusionReason field to Buyer entity ([4961c](https://github.com/Afakto/afakto/commit/4961cefc10da95c) echarp)  

## 0.50.0 (2024-11-22)

### Features

-  update UsersList.vue and staged changes ([74484](https://github.com/Afakto/afakto/commit/74484fd57987e72) echarp)  
-  add excluded field to Buyer entity ([c6eaa](https://github.com/Afakto/afakto/commit/c6eaa533a9e67d5) echarp)  

## 0.49.5 (2024-11-21)

### Features

-  add new fields to InvoiceFromFactor ([e216a](https://github.com/Afakto/afakto/commit/e216a8ab635d8d5) echarp)  

## 0.49.4 (2024-11-21)

### Features

-  add criteria filters to FlowFileQueryService ([b5cef](https://github.com/Afakto/afakto/commit/b5cef0b6d66c06f) echarp)  
-  remove buyer code exclusions feature ([6c170](https://github.com/Afakto/afakto/commit/6c17053ac77e395) echarp)  
-  **flow-file**  add dynamic company selection ([d4737](https://github.com/Afakto/afakto/commit/d47372756d6151a) echarp)  

### Bug Fixes

-  update ContractDetails and BuyerDetails components ([a90e9](https://github.com/Afakto/afakto/commit/a90e970ca8137bd) echarp)  
-  update BuyersList field for amountOutstanding ([b0bae](https://github.com/Afakto/afakto/commit/b0bae8a878797c8) echarp)  
-  remove redundant directory creation ([0d3a8](https://github.com/Afakto/afakto/commit/0d3a89e41e33c32) echarp)  

## 0.49.3 (2024-11-19)

### Features

-  add height styling to q-card rows ([f9a46](https://github.com/Afakto/afakto/commit/f9a46ac7443fb53) echarp)  
-  add buyer code exclusions management ([3cf0a](https://github.com/Afakto/afakto/commit/3cf0a7d62604c19) echarp)  
-  update dependencies and add new changelog ([63d97](https://github.com/Afakto/afakto/commit/63d97a2032809e1) echarp)  
-  **company**  add tabs for main and address details ([bbb39](https://github.com/Afakto/afakto/commit/bbb39a65b14628f) echarp)  

### Other changes


## 0.49.2 (2024-11-16)

### Bug Fixes

-  remove redundant class from q-toolbar elements ([9fd44](https://github.com/Afakto/afakto/commit/9fd44a60fe5a3bb) echarp)  

### Other changes


## 0.49.1 (2024-11-15)

### Features

-  add invoices tab to buyer details ([4115e](https://github.com/Afakto/afakto/commit/4115e5fef8406b2) echarp)  
-  add topBuyers specific styles ([cd527](https://github.com/Afakto/afakto/commit/cd5272b217814b2) echarp)  

### Bug Fixes

-  **ui**  correct template and add policy field ([b2fa0](https://github.com/Afakto/afakto/commit/b2fa00e9d113885) echarp)  

## 0.49.0 (2024-11-14)

### Features

-  **ui**  add insurer and factor details to buyer ([09705](https://github.com/Afakto/afakto/commit/09705079dde7bb0) echarp)  

## 0.48.1 (2024-11-12)

### Features

-  add cession and factor tabs to InvoiceDetails ([097f3](https://github.com/Afakto/afakto/commit/097f3072c2b2dc1) echarp)  

## 0.48.0 (2024-11-12)

### Features

-  adjust sticky elements for mobile view ([93134](https://github.com/Afakto/afakto/commit/93134b49e3ba773) echarp)  
-  refactor comments section in EntityMeta component ([37be0](https://github.com/Afakto/afakto/commit/37be0a46f54e42b) echarp)  
-  **comment**  add Comment entity with CRUD operations ([6ce82](https://github.com/Afakto/afakto/commit/6ce82ec4664b7d7) echarp)  

## 0.47.0 (2024-11-07)

### Features

-  add new code category to BNP account statements ([e7454](https://github.com/Afakto/afakto/commit/e74542578a3a1c7) echarp)  
-  **details**  add entity meta component to detail pages ([bc68f](https://github.com/Afakto/afakto/commit/bc68fef823ed2a4) echarp)  
-  **contract**  enhance contract details layout ([988d5](https://github.com/Afakto/afakto/commit/988d5d7acd69f59) echarp)  

### Other changes

**Merge branch 'jhipster_upgrade'**


[b92fe](https://github.com/Afakto/afakto/commit/b92feb99b02ab0c) echarp *2024-11-06 16:31:40*

**generated jhipster_upgrade using JHipster 8.7.3**


[24346](https://github.com/Afakto/afakto/commit/243461564fa944e) echarp *2024-11-06 09:53:45*

**reference merge of jhipster_upgrade branch into main**


[4a067](https://github.com/Afakto/afakto/commit/4a0679cc291040d) echarp *2024-11-06 09:53:30*

**generated jhipster_upgrade using JHipster ^8.7.3**


[ae107](https://github.com/Afakto/afakto/commit/ae107fc447a0942) echarp *2024-11-06 09:53:29*


## 0.46.2 (2024-11-04)

### Features

-  add fallback for invoice lookup by buyer company ([71f2b](https://github.com/Afakto/afakto/commit/71f2b27ed560f4d) echarp)  

## 0.46.1 (2024-11-04)

### Features

-  update invoice processing to use buyer code ([5ca0d](https://github.com/Afakto/afakto/commit/5ca0da5bf5ed3ac) echarp)  

### Bug Fixes

-  **security**  update CSP for img-src to include https ([8db9b](https://github.com/Afakto/afakto/commit/8db9beaac3cbb80) echarp)  

## 0.46.0 (2024-11-01)

### Features

-  limit floating digits in currency display ([db27a](https://github.com/Afakto/afakto/commit/db27a63a76f07e6) echarp)  
-  optimize currency handling and fetching ([dcae8](https://github.com/Afakto/afakto/commit/dcae8b9008ccc46) echarp)  
-  enhance TopBuyers component and repository ([62a2c](https://github.com/Afakto/afakto/commit/62a2c647f7acb3a) echarp)  
-  easy filter removal ([f6f13](https://github.com/Afakto/afakto/commit/f6f1356dcac1600) echarp)  
-  detect delimiter in setupHeader method ([f9e5e](https://github.com/Afakto/afakto/commit/f9e5e00cc0abc7a) echarp)  
-  empty column filters not saved ([c8f8f](https://github.com/Afakto/afakto/commit/c8f8f6259539f99) echarp)  
-  sanitize buyer number by removing non-alphanumeric chars ([4f9a2](https://github.com/Afakto/afakto/commit/4f9a2b64791df10) echarp)  

### Bug Fixes

-  increase z-index of q-header for proper layering ([2e1be](https://github.com/Afakto/afakto/commit/2e1bee3ff04c673) echarp)  

### Dependency updates

- update dependencies in pom.xml ([48902](https://github.com/Afakto/afakto/commit/48902b8318cfdc4) echarp)  
### Other changes


## 0.45.0 (2024-10-25)

### Features

-  **repository**  align contract situation queries with factor debt logic ([27966](https://github.com/Afakto/afakto/commit/27966881cdff5fa) echarp)  

### Bug Fixes

-  **css**  align ContractSituation with FactorDebt styles ([91333](https://github.com/Afakto/afakto/commit/9133381e46a92e2) echarp)  

## 0.44.1 (2024-10-24)

### Features

-  **repository**  update SQL queries for total factor debt ([dcbb8](https://github.com/Afakto/afakto/commit/dcbb81db749821f) echarp)  

### Other changes


## 0.44.0 (2024-10-24)

### Features

-  **CreditLimitRequestEdit**  lock currency if possible ([ace5c](https://github.com/Afakto/afakto/commit/ace5cea1664d80e) echarp)  
-  **buyer**  add createdDate column to BuyersList ([c8b66](https://github.com/Afakto/afakto/commit/c8b66bc4561ee3a) echarp)  
-  **buyer**  add incoherent balance and amount checks ([6ff0b](https://github.com/Afakto/afakto/commit/6ff0b8999ebe4a3) echarp)  
-  **buyer**  add incoherent limit check and UI updates ([62177](https://github.com/Afakto/afakto/commit/621771c349a1e66) echarp)  
-  **layout**  comment out help menu item ([be6e1](https://github.com/Afakto/afakto/commit/be6e1286ca838de) echarp)  
-  **currency**  add conversion for historical data ([fa43a](https://github.com/Afakto/afakto/commit/fa43a04558f1842) echarp)  

### Bug Fixes

-  **batch**  update ImportJob delay to 30 minutes ([aa01c](https://github.com/Afakto/afakto/commit/aa01cfbe3d47044) echarp)  

## 0.43.1 (2024-10-21)

### Features

-  top buyers amount conversion ([1ab7a](https://github.com/Afakto/afakto/commit/1ab7af7d02e40e9) echarp)  
-  add contract situation conversion and UI updates ([3466e](https://github.com/Afakto/afakto/commit/3466e3e2d7d0b92) echarp)  

### Bug Fixes

-  update currency column name in CessionsList.vue ([a0234](https://github.com/Afakto/afakto/commit/a0234bc0009fa7a) echarp)  

## 0.43.0 (2024-10-19)

### Features

-  global DELETE counts and sums in cession table ([77a73](https://github.com/Afakto/afakto/commit/77a737b8716156c) echarp)  
-  add reference currency select to user details ([b2c8b](https://github.com/Afakto/afakto/commit/b2c8bd1961002cf) echarp)  
-  update styles for q-header and table headers ([568d7](https://github.com/Afakto/afakto/commit/568d7d91edb3ea9) echarp)  
-  **banking**  add new code category 1087 ([54d3d](https://github.com/Afakto/afakto/commit/54d3df1576de534) echarp)  

### Bug Fixes

-  correct invoice handling and loading state logic ([98dbc](https://github.com/Afakto/afakto/commit/98dbc9e166f68fb) echarp)  
-  correct invoice funding logic in BnpOutstandingImport ([af268](https://github.com/Afakto/afakto/commit/af268980609a679) echarp)  

### Other changes


## 0.42.0 (2024-10-17)

### Features

-  **currency**  implement reference currency conversion ([cc7f6](https://github.com/Afakto/afakto/commit/cc7f69138925149) echarp)  
-  **cession**  update column names for contract fields ([d1980](https://github.com/Afakto/afakto/commit/d19801da4b8ed6b) echarp)  
-  add alternative transposition for VAT number type ([978b2](https://github.com/Afakto/afakto/commit/978b2cee43d8bfc) echarp)  
-  add company filter to DashboardRepository queries ([226e1](https://github.com/Afakto/afakto/commit/226e192c1e52b56) echarp)  
-  update ApexCharts colors and styles ([b3fab](https://github.com/Afakto/afakto/commit/b3fab98658c26fa) echarp)  
-  **user**  add preferences management and update API ([fa1d7](https://github.com/Afakto/afakto/commit/fa1d7c86263f7f2) echarp)  
-  **user**  add user self-edit page and dark mode toggle ([e3248](https://github.com/Afakto/afakto/commit/e3248494dcac54f) echarp)  
-  add log status handling for FlowFile ([78555](https://github.com/Afakto/afakto/commit/78555c596cee69a) echarp)  

### Bug Fixes

-  ensure StopWatch is stopped only if started ([edf32](https://github.com/Afakto/afakto/commit/edf32863d3442e5) echarp)  
-  **batch**  update method to find company by code ([4f6c3](https://github.com/Afakto/afakto/commit/4f6c357c871372f) echarp)  

### Other changes


## 0.41.0 (2024-10-12)

### Features

-  extend session timeout to 1 hour ([fba6d](https://github.com/Afakto/afakto/commit/fba6dd00bf59919) echarp)  
-  handle V2.0 files by moving to error folder ([a2db0](https://github.com/Afakto/afakto/commit/a2db03f1cadf193) echarp)  
-  **ui**  add moving shadow effect for version indicators ([1bfd4](https://github.com/Afakto/afakto/commit/1bfd41206380bd5) echarp)  
-  **ui**  add moving shadow effect for DEV version indicator ([dffab](https://github.com/Afakto/afakto/commit/dffab5bc387736b) echarp)  
-  add stopwatch logging for importPath method ([8ba42](https://github.com/Afakto/afakto/commit/8ba421a05e81cad) echarp)  

### Bug Fixes

-  **batch**  set path to "all" for BNP export jobs ([6f4d2](https://github.com/Afakto/afakto/commit/6f4d27011e064ee) echarp)  
-  correct invoice sum calculation in Cession ([56b63](https://github.com/Afakto/afakto/commit/56b6376dcdfb94e) echarp)  
-  reset remittance identifier in correct methods ([e5504](https://github.com/Afakto/afakto/commit/e55041a5056c7ed) echarp)  
-  handle missing company and buyer in import ([3b99d](https://github.com/Afakto/afakto/commit/3b99d854baa4ba6) echarp)  
-  correct file download to prevent corruption ([bff9c](https://github.com/Afakto/afakto/commit/bff9cd429c08de5) echarp)  

### Other changes


## 0.40.0 (2024-10-08)

### Features

-  **flow-file**  add download functionality ([ffc35](https://github.com/Afakto/afakto/commit/ffc35cb2bea1007) echarp)  
-  **cession**  add confirmation dialog before submission ([10b09](https://github.com/Afakto/afakto/commit/10b0988995d421a) echarp)  
-  improve dark mode handling and UI adjustments ([3235c](https://github.com/Afakto/afakto/commit/3235c360486961e) echarp)  

### Bug Fixes

-  **bankTransaction**  correct ledger account assignment ([7b440](https://github.com/Afakto/afakto/commit/7b440d4e570abf9) echarp)  
-  **layouts**  handle undefined store account ([8a045](https://github.com/Afakto/afakto/commit/8a045bcde0a8036) echarp)  

### Other changes


## 0.39.0 (2024-10-07)

### Features

-  add offset transactions endpoint and UI ([cbee2](https://github.com/Afakto/afakto/commit/cbee291b0cfaf42) echarp)  
-  **auth**  handle 401 errors with OAuth2 redirection ([a8311](https://github.com/Afakto/afakto/commit/a83112ecbbef5ca) echarp)  

## 0.38.0 (2024-10-04)

### Features

-  **category-to-account**  add currency field ([dc863](https://github.com/Afakto/afakto/commit/dc863574321b9f0) echarp)  

## 0.37.2 (2024-10-03)

### Features

-  **repository**  optimize top buyers query ([94574](https://github.com/Afakto/afakto/commit/94574bedd882194) echarp)  

### Bug Fixes

-  **i18n**  update top buyers label in dashboard ([54fb0](https://github.com/Afakto/afakto/commit/54fb043ebae4ef2) echarp)  
-  **template**  remove uppercase transformation in title ([fb759](https://github.com/Afakto/afakto/commit/fb75906e3a7f8b2) echarp)  

### Other changes


## 0.37.1 (2024-10-03)

### Features

-  buyer from factor lookup logic, by company and currency ([3d868](https://github.com/Afakto/afakto/commit/3d868103bb4db8c) echarp)  

### Bug Fixes

-  handle missing contract in BnpAccountStatementLine ([48cf1](https://github.com/Afakto/afakto/commit/48cf1b0964795b0) echarp)  

## 0.37.0 (2024-10-02)

### Bug Fixes

-  **contract**  return null if no contract is found ([a423c](https://github.com/Afakto/afakto/commit/a423cc56887dd23) echarp)  

### Other changes


## 0.36.1 (2024-10-02)

### Features

-  **fonts**  setup Noto Sans and SFProDisplay fonts ([f3eeb](https://github.com/Afakto/afakto/commit/f3eebd97f4430cf) echarp)  
-  **layout**  center section items in dashboard ([8c9bd](https://github.com/Afakto/afakto/commit/8c9bd09b4b3554e) echarp)  

## 0.36.0 (2024-10-01)

### Features

-  **flow-file**  add upload functionality ([e7f27](https://github.com/Afakto/afakto/commit/e7f27146d03ecf0) echarp)  
-  **batch**  refactor importPath method ([d4f6a](https://github.com/Afakto/afakto/commit/d4f6a6abe89fd87) echarp)  
-  integrate Droid Sans font in logo ([e7ed0](https://github.com/Afakto/afakto/commit/e7ed0dcce4b9f0c) echarp)  

### Bug Fixes

-  **cession**  ensure immediate database flush ([b1fcb](https://github.com/Afakto/afakto/commit/b1fcbd330603877) echarp)  
-  **layout**  adjust drawer logo size and margins ([2f546](https://github.com/Afakto/afakto/commit/2f54699ca3e9e1f) echarp)  

### Other changes


## 0.35.0 (2024-09-30)

### Features

-  **batch**  set encoding to ISO-8859-1 for file output ([07e7d](https://github.com/Afakto/afakto/commit/07e7d1c578b5840) echarp)  

### Bug Fixes

-  **ui**  render append slot content in BInput component ([d0691](https://github.com/Afakto/afakto/commit/d06913d0bf43bea) echarp)  
-  **contract**  add help text for activation date ([0a0d7](https://github.com/Afakto/afakto/commit/0a0d76510c2a36f) echarp)  
-  **layout**  update drawer logo and item margins ([400f3](https://github.com/Afakto/afakto/commit/400f3f6e4b7aa44) echarp)  
-  **favicon**  remove incorrect 16x16 icon reference ([c3eb8](https://github.com/Afakto/afakto/commit/c3eb83a4f41602d) echarp)  

### Other changes


## 0.34.4 (2024-09-28)

### Features

-  add multi-currency support to contract charts ([1d6ab](https://github.com/Afakto/afakto/commit/1d6ab5754f75923) echarp)  

### Bug Fixes

-  allow for null BuyerFromFactor in BnpRemittanceExportJob ([3e245](https://github.com/Afakto/afakto/commit/3e2454b3a2b1627) echarp)  

### Other changes


## 0.34.3 (2024-09-27)

### Features

-  add sorting to contracts API call ([1d5c9](https://github.com/Afakto/afakto/commit/1d5c9bb0c3456e0) echarp)  

### Bug Fixes

-  handle spaces in number parsing for French locale ([3d3e4](https://github.com/Afakto/afakto/commit/3d3e43c918dfe6e) echarp)  

## 0.34.2 (2024-09-27)

### Other changes


## 0.34.1 (2024-09-27)

### Other changes


## 0.34.0 (2024-09-27)

### Features

-  improve header handling and field mapping ([5e23b](https://github.com/Afakto/afakto/commit/5e23bb949937a52) echarp)  
-  add conditional rendering for dashboard sections ([78eaf](https://github.com/Afakto/afakto/commit/78eaf902f7fecd1) echarp)  

### Bug Fixes

-  handle undefined column labels gracefully ([7586b](https://github.com/Afakto/afakto/commit/7586b73c32fa14f) echarp)  

### Other changes


## 0.33.1 (2024-09-26)

### Features

-  improve error handling and header processing ([86e2e](https://github.com/Afakto/afakto/commit/86e2eb434ff09bd) echarp)  
-  add BigDecimal conversion for multiple locales ([3da82](https://github.com/Afakto/afakto/commit/3da8223d0aee27b) echarp)  
-  add support for multiple date formats ([c3a1d](https://github.com/Afakto/afakto/commit/c3a1df3363b52e2) echarp)  

## 0.33.0 (2024-09-25)

### Features

-  update contractSituation query to use Credit_limit table ([05f46](https://github.com/Afakto/afakto/commit/05f4618f43d4b8a) echarp)  
-  update credit limit criteria in InvoiceQueryService ([cda61](https://github.com/Afakto/afakto/commit/cda61fcad725a57) echarp)  
-  **batch**  add credit limit import functionality ([f630c](https://github.com/Afakto/afakto/commit/f630c822f740429) echarp)  
-  **creditLimit**  simplify credit limit management ([3ca97](https://github.com/Afakto/afakto/commit/3ca97e3b1a5519f) echarp)  
-  **ui**  add dense prop to q-table for better spacing ([29aea](https://github.com/Afakto/afakto/commit/29aeaddbb4e0ce4) echarp)  

### Bug Fixes

-  ensure amount remains a number during editing ([d873e](https://github.com/Afakto/afakto/commit/d873ea7957067b3) echarp)  

### Other changes


## 0.32.0 (2024-09-23)

### Features

-  add flowFile handling and metadata to Cession ([57e96](https://github.com/Afakto/afakto/commit/57e964b9c2e5d43) echarp)  

## 0.31.3 (2024-09-23)

### Bug Fixes

-  **bnp**  correct padding in end block formatting ([f257a](https://github.com/Afakto/afakto/commit/f257a5ba5634ae3) echarp)  

## 0.31.2 (2024-09-23)

### Other changes


## 0.31.1 (2024-09-23)

### Other changes


## 0.31.0 (2024-09-23)

### Features

-  **contract**  update country_id to not nullable ([18075](https://github.com/Afakto/afakto/commit/18075f9364deaf9) echarp)  
-  combine build and deploy jobs for Azure deployment ([bfa87](https://github.com/Afakto/afakto/commit/bfa87fdaff38d64) echarp)  
-  optimize date parsing and improve readability ([b0ea7](https://github.com/Afakto/afakto/commit/b0ea7f42adc3a0b) echarp)  

### Bug Fixes

-  **bnp**  correct balance calculation in export job ([339a7](https://github.com/Afakto/afakto/commit/339a7610380def3) echarp)  
-  move chart type to options in ContractSituation.vue ([7efc5](https://github.com/Afakto/afakto/commit/7efc562a6e8e1ab) echarp)  
-  import Sass color functions and adjust color usage ([702e8](https://github.com/Afakto/afakto/commit/702e81422f8b0ae) echarp)  

### Other changes

**Merge branch 'env-prod'**


[77de7](https://github.com/Afakto/afakto/commit/77de728e94559e5) echarp *2024-09-22 16:30:44*


## 0.30.3 (2024-09-20)

### Other changes


## 0.30.2 (2024-09-20)

### Features

-  log raw input data for negative invoice amounts ([7606e](https://github.com/Afakto/afakto/commit/7606e8748082b85) echarp)  

### Dependency updates

- bump spring-boot-starter-parent to 3.3.4 ([9b959](https://github.com/Afakto/afakto/commit/9b959a37d4f78c6) echarp)  
### Other changes


## 0.30.1 (2024-09-19)

### Features

-  optimize BNP export jobs and update repository queries ([e4748](https://github.com/Afakto/afakto/commit/e4748c4f03f5211) echarp)  

## 0.30.0 (2024-09-19)

### Features

-  ensure correct cell type handling in InvoiceFileUploadService ([39b17](https://github.com/Afakto/afakto/commit/39b17c3be5b5a07) echarp)  
-  **InvoiceType**  add new invoice types ([cc069](https://github.com/Afakto/afakto/commit/cc069c54087715b) echarp)  

## 0.29.1 (2024-09-18)

### Features

-  **InvoiceFileUploadService**  optimize invoice processing ([c3408](https://github.com/Afakto/afakto/commit/c34083fe895315d) echarp)  

### Bug Fixes

-  **BuyerService**  handle null paymentTerms in get method ([b22cc](https://github.com/Afakto/afakto/commit/b22ccde80c2233d) echarp)  

## 0.29.0 (2024-09-17)

### Breaking changes

-  **Buyer**  add balance field to Buyer entity ([34070](https://github.com/Afakto/afakto/commit/34070b03960fcfc) echarp)  

### Features

-  **Buyer**  add balance field to Buyer entity ([34070](https://github.com/Afakto/afakto/commit/34070b03960fcfc) echarp)  
-  **InvoiceImport**  enhance error message and update docs ([d4723](https://github.com/Afakto/afakto/commit/d4723a886ae1014) echarp)  
-  Reset invoice balance before reimport ([48b14](https://github.com/Afakto/afakto/commit/48b143f60a88102) echarp)  
-  Add PaymentMethod conversion to CustomConversionService ([2e7e6](https://github.com/Afakto/afakto/commit/2e7e69db21a6356) echarp)  

### Bug Fixes

-  **BInput**  replace date handling with native input ([533be](https://github.com/Afakto/afakto/commit/533be4d7f635b85) echarp)  

## 0.28.0 (2024-09-17)

### Features

-  **InvoiceImport**  add invoice import functionality ([8f561](https://github.com/Afakto/afakto/commit/8f56180c4f5916e) echarp)  
-  **CessionFile**  adjust file path and download name ([d5d76](https://github.com/Afakto/afakto/commit/d5d769134acc7dc) echarp)  
-  **BnpExportJobs**  optimize database queries and output ([e8f1e](https://github.com/Afakto/afakto/commit/e8f1edf48c201b3) echarp)  
-  **BnpExportJobs**  add newline to string formats ([59120](https://github.com/Afakto/afakto/commit/59120712116736b) echarp)  
-  **InvoiceImport**  change invoice type code ([e8591](https://github.com/Afakto/afakto/commit/e8591893fe93f29) echarp)  
-  Improve Spring Batch processing and error handling ([dce22](https://github.com/Afakto/afakto/commit/dce22fc0f4f13a1) echarp)  

### Bug Fixes

-  Handle null values in DashboardRepository ([be432](https://github.com/Afakto/afakto/commit/be4325411aa3746) echarp)  

## 0.27.0 (2024-09-16)

### Features

-  Add cession filter to InvoiceQueryService ([f622e](https://github.com/Afakto/afakto/commit/f622e1c26fb8941) echarp)  
-  Increase API request size limit ([01eb5](https://github.com/Afakto/afakto/commit/01eb5261735557a) echarp)  
-  Refactor file import and archiving process ([0c58b](https://github.com/Afakto/afakto/commit/0c58b36aeb2857e) echarp)  
-  change filesystem path to temp directory ([a417c](https://github.com/Afakto/afakto/commit/a417cea5b1cef70) echarp)  
-  simplify row expansion in upload tables ([2bac6](https://github.com/Afakto/afakto/commit/2bac6e13e6fdef6) echarp)  
-  update prod workflow and remove old runs workflow ([310dd](https://github.com/Afakto/afakto/commit/310dd43d5749e5f) echarp)  

### Bug Fixes

-  update deployment workflow and app version ([b9648](https://github.com/Afakto/afakto/commit/b9648cc56fb8d57) echarp)  
-  remove unnecessary build dependency in prod workflow ([bf8bd](https://github.com/Afakto/afakto/commit/bf8bd7ee2336a89) echarp)  

### Other changes

**Enums InvoiceType, NumberType, and PaymentMethod now support multiple**

* string values for each enum constant. This improves the flexibility of 
* the parsing process. 
* Date parsing in InvoiceFileUploadService now uses a DateTimeFormatter 
* with an optional time part, allowing it to parse both date and datetime 
* strings into LocalDate objects. 
* Error handling for missing CSV columns has been improved in both 
* BuyerFileUploadService and InvoiceFileUploadService. 
* The Buyer entity now links to the Company entity upon creation. 

[24e62](https://github.com/Afakto/afakto/commit/24e62661dfae086) echarp *2024-09-11 15:40:59*


## 0.26.0 (2024-09-08)

### Features

-  upgrade Java version to 21 ([6d93a](https://github.com/Afakto/afakto/commit/6d93a054681120f) echarp)  
-  **github-actions**  update workflows ([9cc30](https://github.com/Afakto/afakto/commit/9cc30676a39bae0) echarp)  
-  **github-actions**  update formatting in workflow files ([8f141](https://github.com/Afakto/afakto/commit/8f1411dbc832ac2) echarp)  

### Other changes

**Update env-prod_afakto.yml**


[5113f](https://github.com/Afakto/afakto/commit/5113f94c5a16208) echarp *2024-09-05 18:34:05*

**Add or update the Azure App Service build and deployment workflow config**


[5c30d](https://github.com/Afakto/afakto/commit/5c30d11d7f20555) echarp *2024-09-05 16:54:57*

**Update env-prod_app-test-ng.yml**


[14276](https://github.com/Afakto/afakto/commit/14276190127e413) echarp *2024-09-04 20:04:40*

**Update env-prod_app-test-ng.yml**


[bf357](https://github.com/Afakto/afakto/commit/bf357ac1c194706) echarp *2024-09-04 19:52:35*

**Update env-prod_app-test-ng.yml**


[4355a](https://github.com/Afakto/afakto/commit/4355aaeb339d0ad) echarp *2024-09-04 19:38:33*

**Update env-prod_app-test-ng.yml**


[fcfb6](https://github.com/Afakto/afakto/commit/fcfb699589e4dfe) echarp *2024-09-04 19:31:42*

**Update env-prod_app-test-ng.yml**


[6dbaf](https://github.com/Afakto/afakto/commit/6dbafd0a0b39819) echarp *2024-09-04 18:33:07*

**Update env-prod_app-test-ng.yml**


[76b6a](https://github.com/Afakto/afakto/commit/76b6ab24719a92b) echarp *2024-09-04 18:22:08*

**Add or update the Azure App Service build and deployment workflow config**


[559a4](https://github.com/Afakto/afakto/commit/559a46e745d1ec6) echarp *2024-09-04 17:39:50*

**Create an auto-deploy file**


[6bbdc](https://github.com/Afakto/afakto/commit/6bbdc2a040ef8ed) echarp *2024-09-03 21:56:35*

**Create an auto-deploy file**


[97634](https://github.com/Afakto/afakto/commit/976347649ca219f) echarp *2024-09-03 17:39:49*


## 0.25.0 (2024-09-03)

### Features

-  Enhance FlowFile handling and UI ([543cd](https://github.com/Afakto/afakto/commit/543cd8ef4ae5f9f) echarp)  

## 0.24.2 (2024-09-03)

### Features

-  Increase max pageable size in application config ([45659](https://github.com/Afakto/afakto/commit/45659bab076638a) echarp)  

## 0.24.1 (2024-09-02)

### Features

-  **NumberType**  update enum and i18n files ([4ed42](https://github.com/Afakto/afakto/commit/4ed4255aba6b05d) echarp)  
-  **i18n**  add icon keys to translation files ([a82e8](https://github.com/Afakto/afakto/commit/a82e818f99ee2a3) echarp)  

### Bug Fixes

-  **BuyerFileUploadService**  handle missing payment terms ([77b56](https://github.com/Afakto/afakto/commit/77b56f8fa90e8f1) echarp)  

### Other changes


## 0.24.0 (2024-08-29)

### Features

-  **BnpI4cImportJob**  refactor error handling and logging ([0c08b](https://github.com/Afakto/afakto/commit/0c08b34a095e052) echarp)  
-  **Invoice**  add buyer code to invoice list ([7c143](https://github.com/Afakto/afakto/commit/7c14320ea41cc5f) echarp)  
-  **BnpI4cImportJob**  streamline buyer update logic ([fdcb1](https://github.com/Afakto/afakto/commit/fdcb17047672835) echarp)  
-  Improve MT94xImportJob and BankTransactionsList ([3d961](https://github.com/Afakto/afakto/commit/3d96155b4c64383) echarp)  
-  **FlowFile**  add FlowFile entity to UI ([bd208](https://github.com/Afakto/afakto/commit/bd208584eb196e3) echarp)  
-  **UploadService**  refactor upload service to use FlowFile ([a0a71](https://github.com/Afakto/afakto/commit/a0a717770ddd3d3) echarp)  
-  **FlowFile**  add FlowFile entity ([4854d](https://github.com/Afakto/afakto/commit/4854d052ca04611) echarp)  
-  **package-lock.json**  update package versions ([1b023](https://github.com/Afakto/afakto/commit/1b023c8c4b8ec5b) echarp)  

### Bug Fixes

-  **DashboardRepository**  handle null sums in queries ([98910](https://github.com/Afakto/afakto/commit/98910ab88398add) echarp)  

### Other changes

**Merge remote-tracking branch 'origin/env-prod'**


[e91d2](https://github.com/Afakto/afakto/commit/e91d2da1ff273b4) echarp *2024-08-28 18:48:23*

**Merge pull request #58 from khanhpham100398/feature/allow-Emmanuel-IP-to-access-nifi-vm**

* [terraform] - Correct IP whitelisting to Nifi Linux VM 

[0b485](https://github.com/Afakto/afakto/commit/0b485f9349a8047) Phạm Khanh *2024-08-28 10:38:54*

**[terraform] - Correct IP whitelisting to Nifi Linux VM**


[f2913](https://github.com/Afakto/afakto/commit/f291346b39f15a9) Khanh Pham *2024-08-28 10:34:09*

**Merge pull request #57 from khanhpham100398/feature/allow-Emmanuel-IP-to-access-nifi-vm**

* [terraform] - Update IP whitelisting to Nifi Linux VM 

[7d3bc](https://github.com/Afakto/afakto/commit/7d3bcc95326a410) Phạm Khanh *2024-08-28 10:14:18*

**[terraform] - Update IP whitelisting to Nifi Linux VM**


[13701](https://github.com/Afakto/afakto/commit/137017b622cc5b3) Khanh Pham *2024-08-28 08:50:21*

**Merge remote-tracking branch 'origin/env-prod'**


[aeb8a](https://github.com/Afakto/afakto/commit/aeb8af6e4d4e046) echarp *2024-08-27 08:52:43*

**Merge pull request #56 from khanhpham100398/feature/apache-nifi-vm-creation**

* [terraform] -  Server Migration From kpham Subscription To dnguyen15 Subscription 

[9aa50](https://github.com/Afakto/afakto/commit/9aa50d0243f8d4f) Phạm Khanh *2024-08-21 03:15:55*

**[terraform] - revert application_name back to afakto**


[4b19e](https://github.com/Afakto/afakto/commit/4b19e8f3dcbda3b) Khanh Pham *2024-08-20 07:31:49*

**Merge branch 'feature/apache-nifi-vm-creation-without-any-module-changes-v2' into env-prod**


[e281d](https://github.com/Afakto/afakto/commit/e281d8f65e96744) Khanh Pham *2024-08-15 05:16:00*

**[terraform] - Change application_name to afaktolab**


[447cb](https://github.com/Afakto/afakto/commit/447cb923cfb531f) Khanh Pham *2024-08-15 03:29:19*

**Merge pull request #3 from dnguyen15mantu/feature/nifi-vm-migration**

* NiFi VM migration 

[8fa59](https://github.com/Afakto/afakto/commit/8fa59c7163625bf) Phạm Khanh *2024-08-14 08:09:23*

**Attaching the NiFi VM to snet-afakto-2-prod and allowing it on the File Shares**


[da88b](https://github.com/Afakto/afakto/commit/da88b5665b3de49) NGUYEN Duc Tung *2024-08-12 09:04:01*

**Adding the NiFi VM**


[f1ee7](https://github.com/Afakto/afakto/commit/f1ee7111137ccbb) NGUYEN Duc Tung *2024-08-08 10:42:52*


## 0.23.0 (2024-08-19)

### Features

-  Add company column to InvoicesList view ([0eb8a](https://github.com/Afakto/afakto/commit/0eb8aef2067cb4f) echarp)  
-  Improve data handling and logging in file upload services ([a9f05](https://github.com/Afakto/afakto/commit/a9f05a67fe97fb7) echarp)  

### Bug Fixes

-  allow inline styles in content-security-policy ([3b347](https://github.com/Afakto/afakto/commit/3b3475cc1fb7d34) echarp)  
-  allow inline scripts in content-security-policy ([2b77c](https://github.com/Afakto/afakto/commit/2b77cd866a547d1) echarp)  

## 0.22.0 (2024-08-19)

### Features

-  sort authorities alphabetically ([f81ff](https://github.com/Afakto/afakto/commit/f81ff7b0966727b) echarp)  
-  **pom.xml**  upgrade Guava library version ([0703e](https://github.com/Afakto/afakto/commit/0703e1080af30fe) echarp)  
-  **AccountService**  limit authorities for non-admin users ([9da9e](https://github.com/Afakto/afakto/commit/9da9e66037c9d33) echarp)  
-  **security**  implement OrgIdOwned interface ([9c84d](https://github.com/Afakto/afakto/commit/9c84d2cbfd8da39) echarp)  
-  Attempt to setup api-docs in prod ([da17f](https://github.com/Afakto/afakto/commit/da17f13c53adfb3) echarp)  
-  Enable api-docs profile in prod environment ([ca43e](https://github.com/Afakto/afakto/commit/ca43ee851f2dd47) echarp)  

### Bug Fixes

-  remove ASC order in User authority ([fee40](https://github.com/Afakto/afakto/commit/fee407a438690d3) echarp)  

### Other changes


## 0.21.1 (2024-08-14)

### Features

-  Add company perimeter check for entities ([d5d92](https://github.com/Afakto/afakto/commit/d5d920331abf15a) echarp)  
-  Implement company perimeter check for entities ([aee4c](https://github.com/Afakto/afakto/commit/aee4c9387be4220) echarp)  

### Bug Fixes

-  update mapstruct version to 1.6.0 ([0d0d5](https://github.com/Afakto/afakto/commit/0d0d5250c6c786b) echarp)  

### Other changes


## 0.21.0 (2024-07-27)

### Features

-  Enhance CategoryToAccountDetails.vue and related files ([204df](https://github.com/Afakto/afakto/commit/204dff22379c8db) echarp)  
-  add new pages to manage CategoryToAccount ([70db0](https://github.com/Afakto/afakto/commit/70db06f6165a74c) echarp)  
-  update Maven plugin and dependency versions ([bdf95](https://github.com/Afakto/afakto/commit/bdf95df9a6576f7) echarp)  
-  add CategoryToAccount entity and its relationships ([8a8af](https://github.com/Afakto/afakto/commit/8a8afc971dccd99) echarp)  
-  **CessionNew**  filter contracts by activation date ([03c71](https://github.com/Afakto/afakto/commit/03c71b458264006) echarp)  
-  Add management OAuth credentials to app service ([bd36e](https://github.com/Afakto/afakto/commit/bd36ea42f1d5093) echarp)  

### Bug Fixes

-  **AccountService**  **UserService**  improve data consistency ([5aab9](https://github.com/Afakto/afakto/commit/5aab993237d61c8) echarp)  

### Other changes


## 0.20.2 (2024-07-24)

### Features

-  **forms**  add beforeUnload event to prevent data loss ([22318](https://github.com/Afakto/afakto/commit/223188708c6836c) echarp)  
-  **currencies**  add CAD currency ([04a8a](https://github.com/Afakto/afakto/commit/04a8ae2ab8e3eca) echarp)  
-  Enhance AuthInfoResource and update mapstruct version ([dd8c9](https://github.com/Afakto/afakto/commit/dd8c9ba4cbdb01d) echarp)  

### Bug Fixes

-  Make organization field readonly in UserDetails.vue ([9fe94](https://github.com/Afakto/afakto/commit/9fe949eac392a40) echarp)  

## 0.20.1 (2024-07-20)

### Features

-  **DashboardRepository**  adjust queries for better accuracy ([77496](https://github.com/Afakto/afakto/commit/774965b4f5b65b1) echarp)  
-  add separator in MainLayout.vue ([399de](https://github.com/Afakto/afakto/commit/399de593e385c8f) echarp)  
-  upgrade spring-boot-starter-parent to 3.3.2 ([dd53e](https://github.com/Afakto/afakto/commit/dd53ec2826272db) echarp)  

### Other changes


## 0.20.0 (2024-07-19)

### Breaking changes

-  remove parent from Company, add orgId ([e72f6](https://github.com/Afakto/afakto/commit/e72f61567353647) echarp)  
-  optimize Auth0 role management and role checks ([37791](https://github.com/Afakto/afakto/commit/377910ebdd9a475) echarp)  
-  **auth**  add Auth0 integration with invitation support ([ee92e](https://github.com/Afakto/afakto/commit/ee92ed6fec9e9bc) echarp)  

### Features

-  enhance form validation and UI improvements ([40adf](https://github.com/Afakto/afakto/commit/40adf9149306f50) echarp)  
-  remove webkit datetime edit styles ([8fa70](https://github.com/Afakto/afakto/commit/8fa709c70616e80) echarp)  
-  add orgId filter to CompanyCriteria ([04b48](https://github.com/Afakto/afakto/commit/04b48ccd4be7321) echarp)  
-  **UI**  enhance navigation and filtering ([dc856](https://github.com/Afakto/afakto/commit/dc8560ef7f60a70) echarp)  
-  **Company**  enhance organization display ([863c0](https://github.com/Afakto/afakto/commit/863c0f704dc2ffd) echarp)  
-  **Security**  enforce orgId-based access control ([46b4c](https://github.com/Afakto/afakto/commit/46b4c676724c770) echarp)  
-  **ErrorHandling**  add handler for DataIntegrityViolation ([ad978](https://github.com/Afakto/afakto/commit/ad978f060131b0a) echarp)  
-  **BnpI4cImportJob**  handle version for detached entities ([eb057](https://github.com/Afakto/afakto/commit/eb057c8511df893) echarp)  
-  **user**  restrict organization data fetch to admin users ([52e9c](https://github.com/Afakto/afakto/commit/52e9c79540f25b7) echarp)  
-  **UserDetails.vue**  handle delete user async ([a4369](https://github.com/Afakto/afakto/commit/a436978e06e5c41) echarp)  
-  **auth**  add Auth0 integration with invitation support ([ee92e](https://github.com/Afakto/afakto/commit/ee92ed6fec9e9bc) echarp)  
-  **SecurityConfiguration**  remove manual JwtDecoder setup ([23245](https://github.com/Afakto/afakto/commit/23245e8310c02dc) echarp)  

### Bug Fixes

-  **UI**  adjust menu item order in MainLayout ([4a2eb](https://github.com/Afakto/afakto/commit/4a2ebb5698a6b4a) echarp)  
-  correct active state for root route in MenuItem ([fd23d](https://github.com/Afakto/afakto/commit/fd23d706ab1172f) echarp)  

### Other changes


## 0.19.1 (2024-07-10)

### Other changes

**Merge remote-tracking branch 'origin/env-prod'**


[39c6c](https://github.com/Afakto/afakto/commit/39c6cbf2d170c7f) echarp *2024-07-10 08:37:02*

**Merge pull request #55 from khanhpham100398/feature/file-exchanger-server-removal**

* Feature/file exchanger server removal 

[59ff1](https://github.com/Afakto/afakto/commit/59ff1c7ef89bd34) Phạm Khanh *2024-07-02 03:02:16*

**[terraform] - revert application name back to afakto**


[7ec92](https://github.com/Afakto/afakto/commit/7ec929788aabe82) Khanh Pham *2024-07-01 08:44:39*

**[terraform] - remove the file-exchanger server.**


[c9e2a](https://github.com/Afakto/afakto/commit/c9e2aea93113b34) Khanh Pham *2024-07-01 08:00:59*

**[terraform] - Change application_name to afaktolab**


[e9c8f](https://github.com/Afakto/afakto/commit/e9c8f742bb88d0e) Khanh Pham *2024-06-27 07:42:53*


## 0.19.0 (2024-07-10)

### Breaking changes

-  **domain**  remove explicit EqualsAndHashCode ([9f129](https://github.com/Afakto/afakto/commit/9f12960ec2c6153) echarp)  
-  **domain**  remove unnecessary serialVersionUID ([8ab10](https://github.com/Afakto/afakto/commit/8ab10415fbbbf55) echarp)  
-  **auditing**  add optimistic locking for entities ([607c4](https://github.com/Afakto/afakto/commit/607c427a083c116) echarp)  

### Features

-  update dependencies ([7eb40](https://github.com/Afakto/afakto/commit/7eb4099b28dea72) echarp)  
-  **MainLayout**  improve drawer behavior for mobile view ([2a112](https://github.com/Afakto/afakto/commit/2a1121e1eb7b1da) echarp)  
-  Use companies from user account for filters ([a71e4](https://github.com/Afakto/afakto/commit/a71e4cb1b86f795) echarp)  
-  **dashboard**  add company filter to dashboard queries ([e5070](https://github.com/Afakto/afakto/commit/e5070a5d06dbb42) echarp)  
-  upgrade rimraf to version 6.0.0 ([50de2](https://github.com/Afakto/afakto/commit/50de29b48d96ff5) echarp)  
-  **UserService**  refactor user role check and cache clearing ([5ecca](https://github.com/Afakto/afakto/commit/5ecca966169339e) echarp)  
-  **security**  enforce role-based access control ([58c93](https://github.com/Afakto/afakto/commit/58c935d1b85150f) echarp)  
-  **dashboard**  add role check for sell invoices button ([c1b33](https://github.com/Afakto/afakto/commit/c1b33160ca92e51) echarp)  
-  **package.json**  update dependencies and reorganize properties ([56345](https://github.com/Afakto/afakto/commit/56345ac1361a3f8) echarp)  
-  **AccessControl**  implement role-based form controls ([f112b](https://github.com/Afakto/afakto/commit/f112b6214ad10e7) echarp)  
-  **ColumnsFiltering**  add use-chips prop to q-select ([6909d](https://github.com/Afakto/afakto/commit/6909d2818788088) echarp)  
-  **security**  add access control for credit limit requests ([44805](https://github.com/Afakto/afakto/commit/44805fd31aecc1f) echarp)  
-  **caching**  modify caching strategy for entities ([28582](https://github.com/Afakto/afakto/commit/285829f37692895) echarp)  
-  **authentication**  add role-based form access control ([8e21a](https://github.com/Afakto/afakto/commit/8e21a67b51cac9f) echarp)  
-  Add user creation in IT setup methods ([588e6](https://github.com/Afakto/afakto/commit/588e6dcfbb4bbaf) echarp)  
-  update dependencies ([6d59c](https://github.com/Afakto/afakto/commit/6d59ce42fc0629d) echarp)  
-  Implement UserCompaniesPerimeter in QueryServices ([a3b58](https://github.com/Afakto/afakto/commit/a3b582e7ef3b589) echarp)  
-  **user-company**  add many-to-many relationship ([6e425](https://github.com/Afakto/afakto/commit/6e42532f8266a1c) echarp)  
-  **user roles**  replace ROLE_USER with ROLE_WRITER ([fca80](https://github.com/Afakto/afakto/commit/fca807f084a4460) echarp)  
-  **User**  add avatar display in UsersList ([f314e](https://github.com/Afakto/afakto/commit/f314e56a47957f1) echarp)  
-  **User**  change date conversion in formula ([2a364](https://github.com/Afakto/afakto/commit/2a3640e984d1adc) echarp)  
-  **user**  user management, list and details ([9269f](https://github.com/Afakto/afakto/commit/9269f05cc051fea) echarp)  
-  **User**  add advanced search and CRUD operations ([6d25e](https://github.com/Afakto/afakto/commit/6d25ee828d27266) echarp)  
-  **user**  refactor User entity and related classes ([3561b](https://github.com/Afakto/afakto/commit/3561b52c017e2b1) echarp)  
-  **auditing**  add optimistic locking for entities ([607c4](https://github.com/Afakto/afakto/commit/607c427a083c116) echarp)  
-  Move productName in package.json ([be512](https://github.com/Afakto/afakto/commit/be512cea4f34d74) echarp)  
-  Move Afakto.jdl and Remove Model Diagrams ([543cc](https://github.com/Afakto/afakto/commit/543cc492c85ff79) echarp)  

### Bug Fixes

-  Update label for cession status in CessionNew.vue ([0894b](https://github.com/Afakto/afakto/commit/0894b85c3055756) echarp)  

### Other changes

**This commit removes the manual session handling in the InvoiceService**

* for loading the Buyer entity. The EntityManager unwrap and explicit 
* session.get() calls have been removed. This change simplifies the code 
* and relies on Hibernate&#x27;s default lazy loading behavior for associated 
* entities. The CustomInvoiceRepository and its implementation have been 
* deleted as they are no longer needed. 

[e91e3](https://github.com/Afakto/afakto/commit/e91e390034dffbb) echarp *2024-06-27 14:29:35*

**upgrade merge of jhipster_upgrade branch into main**


[b1898](https://github.com/Afakto/afakto/commit/b1898628c5fae4e) echarp *2024-06-25 21:02:31*

**generated jhipster_upgrade using JHipster 8.6.0**


[f45fa](https://github.com/Afakto/afakto/commit/f45fabb4c92cff1) echarp *2024-06-25 20:43:18*

**reference merge of jhipster_upgrade branch into main**


[cdddd](https://github.com/Afakto/afakto/commit/cdddd92241c09ec) echarp *2024-06-25 20:43:06*

**generated jhipster_upgrade using JHipster ^8.5.0**


[3658e](https://github.com/Afakto/afakto/commit/3658ea38c9e65fe) echarp *2024-06-25 20:43:05*


## 0.18.0 (2024-06-22)

### Features

-  **invoice**  remove default invoices from new cession ([428f8](https://github.com/Afakto/afakto/commit/428f8e8666e0cc1) echarp)  
-  update front dependencies ([7d804](https://github.com/Afakto/afakto/commit/7d804f6d5cf1265) echarp)  
-  **CessionQueryService**  add factorInstitution filter ([3e1bf](https://github.com/Afakto/afakto/commit/3e1bf4bb7714926) echarp)  
-  **pom.xml**  upgrade dependencies ([52e15](https://github.com/Afakto/afakto/commit/52e153d22c23cf5) echarp)  
-  update contract related classes and frontend ([82a7a](https://github.com/Afakto/afakto/commit/82a7ad07788713c) echarp)  
-  **Cession**  enhance filtering and sorting capabilities ([ca378](https://github.com/Afakto/afakto/commit/ca37886b6ac20f4) echarp)  
-  enhance Buyer entity and related components ([4913b](https://github.com/Afakto/afakto/commit/4913b07abeded04) echarp)  
-  **Afakto.jdl**  update JHipster entities and relationships ([3edae](https://github.com/Afakto/afakto/commit/3edae738d4ec167) echarp)  
-  **Afakto.jdl**  regenerate domain model with new entities ([06788](https://github.com/Afakto/afakto/commit/06788c543c7a623) echarp)  
-  **CessionDetails**  load data on component mount ([882ce](https://github.com/Afakto/afakto/commit/882ce29fab1b89a) echarp)  
-  **router**  simplify router setup and reorder imports ([50d68](https://github.com/Afakto/afakto/commit/50d683fe8812938) echarp)  
-  **BuyersList**  enhance table and add gauge column ([9fb34](https://github.com/Afakto/afakto/commit/9fb34373b44f3b2) echarp)  
-  **Cession**  update routing and component naming ([b8377](https://github.com/Afakto/afakto/commit/b83770943e1d5f9) echarp)  
-  **BuyerDetails**  refactor BuyerEdit to BuyerDetails ([51025](https://github.com/Afakto/afakto/commit/51025c694e0ef1b) echarp)  
-  **invoice**  Enhance invoice filtering and sorting ([2288c](https://github.com/Afakto/afakto/commit/2288cc6a5bbd73c) echarp)  
-  optimize Vue.js layout and error handling ([cfc0d](https://github.com/Afakto/afakto/commit/cfc0d522a6994e9) echarp)  
-  add defaultOverDue field to Contract ([3845d](https://github.com/Afakto/afakto/commit/3845d230456b279) echarp)  
-  optimize Vue components and validation rules ([b8571](https://github.com/Afakto/afakto/commit/b85717e4c8887dd) echarp)  

### Bug Fixes

-  correct local storage keys for column settings ([26613](https://github.com/Afakto/afakto/commit/26613a6625c876b) echarp)  

### Other changes

**Merge branch 'env-prod'**


[83501](https://github.com/Afakto/afakto/commit/83501e0a31caa55) echarp *2024-06-19 16:17:01*

**Merge pull request #53 from khanhpham100398/feature/gitops-workflow-upgrade**

* Feature/gitops workflow upgrade 

[bc856](https://github.com/Afakto/afakto/commit/bc856a1b17d420d) Phạm Khanh *2024-06-17 03:21:32*

**[terraform] - revert application name back to afakto**


[361b4](https://github.com/Afakto/afakto/commit/361b43d99473407) Khanh Pham *2024-06-14 08:16:46*

**[github-workflow] - Change name of the Authorize local IP to access the Storage Account step to ... Afakto Storage Account.**


[436c2](https://github.com/Afakto/afakto/commit/436c29801f47f72) Khanh Pham *2024-06-14 05:28:08*

**[github-workflow] - Add backup strategy for the step Get GitHub agent IP (2).**


[53add](https://github.com/Afakto/afakto/commit/53add9578316a5b) Khanh Pham *2024-06-13 10:26:20*

**[github-workflow] - Add backup strategy for the step Get GitHub agent IP.**


[bbcc5](https://github.com/Afakto/afakto/commit/bbcc528d23fc715) Khanh Pham *2024-06-13 10:23:09*

**[github-workflow] - Display local ip to debug.**


[50596](https://github.com/Afakto/afakto/commit/50596844a29a131) Khanh Pham *2024-06-13 10:18:57*

**[github-workflow] - Remove option --yes from the step Set Terraform Storage Account networking rule to DENY by default.**


[40571](https://github.com/Afakto/afakto/commit/40571a4384cb085) Khanh Pham *2024-06-13 09:57:32*

**[github-workflow] - Add a new step to set network rule to DENY by default for Terraform Storage Account.**


[8b55c](https://github.com/Afakto/afakto/commit/8b55c7eb48b8d82) Khanh Pham *2024-06-13 09:37:50*

**[github-workflow] - Change secret name AFAKTO_STORAGE_ACCOUNT to PROD_AFAKTO_STORAGE_ACCOUNT in order to distinguish the name in differrent environments.**


[5fbcd](https://github.com/Afakto/afakto/commit/5fbcdaa16fa9dd0) Khanh Pham *2024-06-12 10:55:05*

**[github-workflow] - Upgrage Apply Terraform configuration step to version 0.14.0**


[be0a1](https://github.com/Afakto/afakto/commit/be0a1a111812a8a) Khanh Pham *2024-06-12 10:09:07*

**[terraform] - Change application_name to afaktolab**


[04280](https://github.com/Afakto/afakto/commit/0428078cf8c972f) Khanh Pham *2024-06-11 06:24:20*


## 0.17.0 (2024-06-11)

### Features

-  **i18n**  update labels and translations ([f519e](https://github.com/Afakto/afakto/commit/f519e44f0ecf905) echarp)  
-  update UI and routing ([18231](https://github.com/Afakto/afakto/commit/18231645fc3a93e) echarp)  
-  Add ledger account to bank transactions ([c04f6](https://github.com/Afakto/afakto/commit/c04f6c2eaf5418b) echarp)  
-  add valueDate to BankTransaction entity ([46e1d](https://github.com/Afakto/afakto/commit/46e1d3f5fdf4268) echarp)  

### Bug Fixes

-  correct handling of empty object in ColumnsFiltering ([ec439](https://github.com/Afakto/afakto/commit/ec439da1ffb4b13) echarp)  

### Other changes

**favicon**


[455c3](https://github.com/Afakto/afakto/commit/455c36f095536d5) SDaller *2024-06-09 16:41:02*

**Merge remote-tracking branch 'refs/remotes/origin/env-prod' into env-prod**


[613e2](https://github.com/Afakto/afakto/commit/613e282435368c0) echarp *2024-06-07 12:50:00*

**Update gitops.yml - Reverted to use implicit protocol in akamai IP discovery**


[0facd](https://github.com/Afakto/afakto/commit/0facd38405fa4ba) dnguyen15mantu *2024-05-31 04:34:18*

**Use https:// for akamai IP discovery**


[ae58e](https://github.com/Afakto/afakto/commit/ae58ea0e6fd6a16) echarp *2024-05-30 09:59:20*


## 0.16.0 (2024-06-07)

### Features

-  **export**  export bank transactions as a csv file ([cbb1a](https://github.com/Afakto/afakto/commit/cbb1ac30062e9ac) echarp)  
-  add default sort by "created" to queries ([17e64](https://github.com/Afakto/afakto/commit/17e64c33fdeb541) echarp)  
-  **axios**  optimize axios configuration for readability ([64206](https://github.com/Afakto/afakto/commit/64206b972137094) echarp)  
-  **BankTransaction**  enhance filtering and sorting ([66be8](https://github.com/Afakto/afakto/commit/66be856685bf1a3) echarp)  
-  **BankTransactionsList**  enhance filtering ([d10fb](https://github.com/Afakto/afakto/commit/d10fbe289f86a8b) echarp)  
-  **pom.xml**  upgrade hypersistence-utils-hibernate to v3.7.6 ([fb3bf](https://github.com/Afakto/afakto/commit/fb3bf52278c71a7) echarp)  
-  Improve UI and UX of ColumnsFiltering.vue ([1f2b0](https://github.com/Afakto/afakto/commit/1f2b0962c7713c3) echarp)  
-  **CessionDetail.vue**  **CessionNew.vue**  improve UI and readability ([8a877](https://github.com/Afakto/afakto/commit/8a8779b4c7eba8e) echarp)  
-  **CessionsList.vue**  optimize table and navigation ([f10d1](https://github.com/Afakto/afakto/commit/f10d1fd806570f5) echarp)  
-  top align toolbar and enhance filters ([0491e](https://github.com/Afakto/afakto/commit/0491e00c3320fdd) echarp)  
-  **BankTransactionsList.vue**  add column filtering and visibility ([7f7d0](https://github.com/Afakto/afakto/commit/7f7d02461ecf8ac) echarp)  
-  **BInput.vue**  add currency and percent types, refactor styles ([ae2e9](https://github.com/Afakto/afakto/commit/ae2e9bb8467580e) echarp)  
-  add filter and column visibility to bank transactions ([90911](https://github.com/Afakto/afakto/commit/909111494f4d04e) echarp)  

### Bug Fixes

-  **BuyerEdit.vue**  use q-input for SIREN instead of b-input ([befbe](https://github.com/Afakto/afakto/commit/befbe21807725d2) echarp)  
-  **pom.xml**  downgrade maven version to 3.8.5 ([ddda5](https://github.com/Afakto/afakto/commit/ddda574ae0cdc26) echarp)  

### Other changes

**reference merge of jhipster_upgrade branch into main**


[0987e](https://github.com/Afakto/afakto/commit/0987e9f09e5b1f1) echarp *2024-06-02 13:13:45*

**generated jhipster_upgrade using JHipster 8.5.0**


[57986](https://github.com/Afakto/afakto/commit/57986275b6c5569) echarp *2024-06-02 13:13:44*


## 0.15.2 (2024-05-30)

### Features

-  **logout**  mechanism (hack) to logout in production ([37148](https://github.com/Afakto/afakto/commit/37148290c11924e) echarp)  
-  **front**  invoice and bank transaction amounts in green or red ([a3f02](https://github.com/Afakto/afakto/commit/a3f021a85361bae) echarp)  

## 0.15.1 (2024-05-21)

### Features

-  **bank transaction**  modify date type from timestamp to simple "date" ([210c9](https://github.com/Afakto/afakto/commit/210c923c6d17137) echarp)  
-  **dasbhoard**  correct potential funding sign, which can be negative ([1800f](https://github.com/Afakto/afakto/commit/1800f3e8e3be0d2) echarp)  
-  **dashboard**  clean some dashboard elements ([5d3a5](https://github.com/Afakto/afakto/commit/5d3a5fb2ccf0f27) echarp)  

### Bug Fixes

-  **bank transaction**  import files from oldest to newest, required to get last balances on dashboard ([71691](https://github.com/Afakto/afakto/commit/716919f008687fa) echarp)  
-  **exports**  sort by invoice date **and** number when exporting balance ([e431e](https://github.com/Afakto/afakto/commit/e431ec20e6b6d5b) echarp)  

## 0.15.0 (2024-05-18)

### Features

-  **dashboard**  calculate "potential funding" based on last bank transaction balances ([2e271](https://github.com/Afakto/afakto/commit/2e2716e6954c47c) echarp)  
-  **buyer**  add "No credit limit" indication ([8155b](https://github.com/Afakto/afakto/commit/8155bd70f7142d9) echarp)  
-  **bank transaction**  manage balance per line ([dfd96](https://github.com/Afakto/afakto/commit/dfd9624c3709309) echarp)  

### Bug Fixes

-  **cession**  handle companies with parent upon cession creation ([9d2bd](https://github.com/Afakto/afakto/commit/9d2bd5092876989) echarp)  
-  **i4c**  handle buyer with same siren without blocking import ([d9eb7](https://github.com/Afakto/afakto/commit/d9eb7646d846e9e) echarp)  

## 0.14.1 (2024-05-14)

### Other changes


## 0.14.0 (2024-05-13)

### Features

-  **bank transaction**  display currency using common mechanism ([457d3](https://github.com/Afakto/afakto/commit/457d3279b08628f) echarp)  
-  **dashboard**  improve contract situation data ([fbad0](https://github.com/Afakto/afakto/commit/fbad03a560b7f81) echarp)  
-  **dashboard**  display better apex charts, dark/light ([6500f](https://github.com/Afakto/afakto/commit/6500fbeff1a7d32) echarp)  
-  **front**  display a credit gauge for each buyer ([d7efa](https://github.com/Afakto/afakto/commit/d7efa9182bbc602) echarp)  
-  **security**  manage users without organisation ([07887](https://github.com/Afakto/afakto/commit/0788758242d79a8) echarp)  
-  **front**  handle number inputs without moving cursor around ([a9ef0](https://github.com/Afakto/afakto/commit/a9ef0ccf8bd00c2) echarp)  
-  **cession**  change section name from "sold" to "sellable" ([19e28](https://github.com/Afakto/afakto/commit/19e2805e3d0dd40) echarp)  
-  **security**  logout from auth0 using proper url coming from backend ([ba881](https://github.com/Afakto/afakto/commit/ba8810c39961659) echarp)  

## 0.13.3 (2024-05-06)

### Features

-  **cession**  optimize balance file creation ([ac805](https://github.com/Afakto/afakto/commit/ac80532c707678f) echarp)  
-  **cession**  using invoice balance and not funded amount ([4fb0f](https://github.com/Afakto/afakto/commit/4fb0f35c6b28da3) echarp)  
-  **front**  improve buyer form ([219ce](https://github.com/Afakto/afakto/commit/219cec9a88167df) echarp)  
-  **cession**  do not export "null" when factor code is absent ([98535](https://github.com/Afakto/afakto/commit/985357c3a9da90f) echarp)  

## 0.13.2 (2024-05-03)

### Features

-  **front**  organise more evenly dashboard elements ([d193e](https://github.com/Afakto/afakto/commit/d193e3f36ad1b20) echarp)  
-  **front**  organise dashboard more evenly ([7bbb1](https://github.com/Afakto/afakto/commit/7bbb10aa4d40015) echarp)  
-  **contract**  reorganise the contract detail screen ([f71fa](https://github.com/Afakto/afakto/commit/f71fa0c38191016) echarp)  
-  **front**  capitalize, using small-caps, all titles ([6a741](https://github.com/Afakto/afakto/commit/6a741b5891f523a) echarp)  
-  **cession**  manage cession invoice addition properly ([4a898](https://github.com/Afakto/afakto/commit/4a898d42463f816) echarp)  
-  **front**  correct some display elements ([71858](https://github.com/Afakto/afakto/commit/718584b08c84bf5) echarp)  

### Bug Fixes

-  **buyer**  upgrade siren API to v3.11 ([e7f35](https://github.com/Afakto/afakto/commit/e7f35bcb22e8868) echarp)  

## 0.13.1 (2024-05-03)

### Features

-  **cession**  display tabs with icons ([af2a6](https://github.com/Afakto/afakto/commit/af2a60c848055c8) echarp)  
-  **cession**  increase max number of invoices to 3000 ([2e487](https://github.com/Afakto/afakto/commit/2e4871d757d93ca) echarp)  

### Other changes

**Merge branch 'main' of https://github.com/mantucorp/afakto**


[8dcf9](https://github.com/Afakto/afakto/commit/8dcf9aa9af5ef4d) SDaller *2024-05-03 07:52:50*

**"Dashboard Tooltip"**


[97000](https://github.com/Afakto/afakto/commit/97000fd90de4b3b) SDaller *2024-05-03 07:50:28*


## 0.13.0 (2024-05-02)

### Features

-  **cession**  display cession abstract as a calc, with colors ([fe187](https://github.com/Afakto/afakto/commit/fe1870d91d9ffeb) echarp)  
-  **front**  standardize save buttons ([41578](https://github.com/Afakto/afakto/commit/415786b1b83a026) echarp)  
-  **cession**  display global count and sum in cessions' list ([99536](https://github.com/Afakto/afakto/commit/995369525376fd4) echarp)  
-  **cession**  select sellable invoices, which have proper buyer data ([9df4c](https://github.com/Afakto/afakto/commit/9df4ceee277f23f) echarp)  
-  **dashboard**  display better data ([1cce5](https://github.com/Afakto/afakto/commit/1cce5950316eeb8) echarp)  
-  **front**  capitalize all buttons and table headers ([9c8e3](https://github.com/Afakto/afakto/commit/9c8e340f551198c) echarp)  
-  **front**  all tables now 15 lines per default ([4f42a](https://github.com/Afakto/afakto/commit/4f42a451e14ee64) echarp)  
-  **cession**  redirect to created cession ([b3b3b](https://github.com/Afakto/afakto/commit/b3b3b945aa577f9) echarp)  
-  **cession**  clean some cession tests, with mock spring batch ([18f02](https://github.com/Afakto/afakto/commit/18f020c177fae15) echarp)  
-  **cession**  generate and download cession file ([75880](https://github.com/Afakto/afakto/commit/758801964522815) echarp)  
-  **cession**  place close button to be accessible ([b4cfc](https://github.com/Afakto/afakto/commit/b4cfcc87656a70f) echarp)  
-  **cession**  improve cession creation and list, no file mgmt ([5e316](https://github.com/Afakto/afakto/commit/5e316d0f026178e) echarp)  
-  **cession**  add cession module, from database to some front elements ([4629b](https://github.com/Afakto/afakto/commit/4629b695a42a6d9) echarp)  
-  **front**  improve some design elements, like title font ([6f756](https://github.com/Afakto/afakto/commit/6f7560e20ddfdbd) echarp)  
-  **cession**  display sellable/paid/current in tabs ([9a735](https://github.com/Afakto/afakto/commit/9a7351c9caec9b8) echarp)  
-  **front**  remove some boldness from titles ([822fe](https://github.com/Afakto/afakto/commit/822fe30e4c4f6f6) echarp)  
-  **cession**  improve cession screen ([ee610](https://github.com/Afakto/afakto/commit/ee61061c64f4f9e) echarp)  
-  **cession**  list sellable, sold and current invoices ([dee61](https://github.com/Afakto/afakto/commit/dee610d38517278) echarp)  
-  **cession**  select contract and display sellable invoices ([80b9f](https://github.com/Afakto/afakto/commit/80b9f42b90292d2) echarp)  
-  **cession**  setup initial page ([50440](https://github.com/Afakto/afakto/commit/5044018e45033c7) echarp)  
-  **imports**  improve error message and stop after 666 errors ([49553](https://github.com/Afakto/afakto/commit/495539fe4a609b2) echarp)  
-  **dashboard**  bolden dashboard titles ([1b52d](https://github.com/Afakto/afakto/commit/1b52dc4dbee89f8) echarp)  
-  **dashboard**  remove available to sell history ([b934b](https://github.com/Afakto/afakto/commit/b934b40223ba64c) echarp)  
-  **imports**  manage more logs upon csv import error ([47f2e](https://github.com/Afakto/afakto/commit/47f2ec5a243230c) echarp)  

### Other changes

**Merge branch 'env-prod'**


[342fd](https://github.com/Afakto/afakto/commit/342fd02c79d220f) echarp *2024-04-30 07:30:05*


## 0.12.0 (2024-04-30)

### Features

-  **imports**  improve error message and stop after 666 errors ([fb5ab](https://github.com/Afakto/afakto/commit/fb5ab5c393f42fd) echarp)  
-  **cession**  add cession module, from database to some front elements ([f8a55](https://github.com/Afakto/afakto/commit/f8a55399d4d563c) echarp)  
-  **cession**  improve cession creation and list, no file mgmt ([d8bb1](https://github.com/Afakto/afakto/commit/d8bb1dbf603db22) echarp)  
-  **cession**  improve cession screen ([a4c59](https://github.com/Afakto/afakto/commit/a4c5925d4dd91d1) echarp)  
-  **dashboard**  bolden dashboard titles ([a1916](https://github.com/Afakto/afakto/commit/a19165828d23e79) echarp)  
-  **cession**  display sellable/paid/current in tabs ([9ddcd](https://github.com/Afakto/afakto/commit/9ddcdd35cd1ed2c) echarp)  
-  **front**  improve some design elements, like title font ([9624d](https://github.com/Afakto/afakto/commit/9624d354c78c7e5) echarp)  
-  **cession**  setup initial page ([7a209](https://github.com/Afakto/afakto/commit/7a2098aac4c97bb) echarp)  
-  **cession**  select contract and display sellable invoices ([7429b](https://github.com/Afakto/afakto/commit/7429be7ff062340) echarp)  
-  **cession**  place close button to be accessible ([5e853](https://github.com/Afakto/afakto/commit/5e85386c0251d8f) echarp)  
-  **cession**  list sellable, sold and current invoices ([41465](https://github.com/Afakto/afakto/commit/414652069ef4d37) echarp)  
-  **front**  remove some boldness from titles ([16ae2](https://github.com/Afakto/afakto/commit/16ae2ab7162a5b3) echarp)  
-  **imports**  manage more logs upon csv import error ([01696](https://github.com/Afakto/afakto/commit/01696423af1e2ba) echarp)  
-  **dashboard**  remove available to sell history ([010bd](https://github.com/Afakto/afakto/commit/010bd7611e303f9) echarp)  

### Other changes

**Merge pull request #50 from dnguyen15mantu/feature/change-region-to-francecentral**

* Change the Azure region to France Central 

[16aa0](https://github.com/Afakto/afakto/commit/16aa000a982c3d9) dnguyen15mantu *2024-04-24 04:08:35*

**Change the Azure region to France Central**


[2dfae](https://github.com/Afakto/afakto/commit/2dfae5eb0608417) dnguyen15mantu *2024-04-23 10:20:47*


## 0.11.1 (2024-04-12)

### Features

-  **dashboard**  finalize presentation, with (mostly?) good requests ([f6187](https://github.com/Afakto/afakto/commit/f6187ee692bd725) echarp)  
-  **dashboard**  setup tooltips on dashboard's cards ([1cc80](https://github.com/Afakto/afakto/commit/1cc802fb4944e43) echarp)  
-  **dashboard**  setup situation bar proper requests ([b5acb](https://github.com/Afakto/afakto/commit/b5acb1425cdfa94) echarp)  
-  **dashboard**  add invoice payment date, necessary to calculate factor debt ([42d34](https://github.com/Afakto/afakto/commit/42d34449eab0816) echarp)  
-  **dashboard**  proper factor debt and historic withdrawal requests ([a3207](https://github.com/Afakto/afakto/commit/a3207798496c8ff) echarp)  
-  **dashboard**  improve display ([dbf8b](https://github.com/Afakto/afakto/commit/dbf8bc9183d027f) echarp)  
-  **dashboard**  add all cards, fake and incomplete ([ecb03](https://github.com/Afakto/afakto/commit/ecb03578b57bff1) echarp)  
-  **dashboard**  add historical graph ([9b050](https://github.com/Afakto/afakto/commit/9b050c72b2a98fe) echarp)  
-  **dashboard**  display total factor debt ([8976c](https://github.com/Afakto/afakto/commit/8976c62cb6f6919) echarp)  
-  **front**  simplify drawer organisation ([23b76](https://github.com/Afakto/afakto/commit/23b764fd34b1464) echarp)  
-  **front**  complete menu with proper settings page ([a65c6](https://github.com/Afakto/afakto/commit/a65c6cdcea6dfec) echarp)  
-  **front**  remove shadows and background color ([8af53](https://github.com/Afakto/afakto/commit/8af53c455a1da32) echarp)  
-  **settings**  setup working settings page ([c9271](https://github.com/Afakto/afakto/commit/c927158ceadb036) echarp)  
-  **exports**  finish bnp remittance export file ([f69ee](https://github.com/Afakto/afakto/commit/f69eed88b67df33) echarp)  
-  **exports**  finish bnp remittance export file ([57db8](https://github.com/Afakto/afakto/commit/57db8c3e4028d09) echarp)  
-  **exports**  finish bnp balance export file ([6f973](https://github.com/Afakto/afakto/commit/6f97370021319a3) echarp)  
-  **exports**  setup an initial BNP ATC balance file ([d9271](https://github.com/Afakto/afakto/commit/d9271618f1c1a59) echarp)  
-  **front**  attempt to simplify auth0 call ([c521f](https://github.com/Afakto/afakto/commit/c521f6d468a8dd9) echarp)  
-  **company**  remove the "process statements" button ([2951c](https://github.com/Afakto/afakto/commit/2951cac0b9a4284) echarp)  
-  **imports**  manage mt940 and mt942 files ([a8b54](https://github.com/Afakto/afakto/commit/a8b5405cf1c2d73) echarp)  
-  **front**  order contracts by activation date ([f395d](https://github.com/Afakto/afakto/commit/f395dac6eb7b048) echarp)  
-  **dashboard**  setup the total factor debt card ([aea57](https://github.com/Afakto/afakto/commit/aea5763ddaad98e) echarp)  
-  **front**  sort by date or name appropriately ([9f2f1](https://github.com/Afakto/afakto/commit/9f2f1be8f50bf30) echarp)  

### Bug Fixes

-  **imports**  add a text to BigDecimal conversion, to handle negative amounts in i4c ([835d7](https://github.com/Afakto/afakto/commit/835d7364016385c) echarp)  
-  **backend**  correct BigDecimal.TWO, which is not accepted on azure ([09e07](https://github.com/Afakto/afakto/commit/09e0798e3f7704d) echarp)  
-  **backend**  correct BigDecimal.TWO, which is not accepted on azure ([3bde8](https://github.com/Afakto/afakto/commit/3bde8cecbe03e6b) echarp)  
-  **tests**  correct some architecture setup ([7e7bc](https://github.com/Afakto/afakto/commit/7e7bce80b9a1da5) echarp)  
-  **tests**  correct an archival directory creation ([ee192](https://github.com/Afakto/afakto/commit/ee19227488adce3) echarp)  

### Other changes

**Merge branch 'env-prod'**


[2e225](https://github.com/Afakto/afakto/commit/2e225ee5c9fce43) echarp *2024-04-06 17:01:12*

**Merge branches 'main' and 'main' of github.com:mantucorp/afakto**


[e33b0](https://github.com/Afakto/afakto/commit/e33b01be466f43f) daniela.crv *2024-03-25 13:03:37*

**ajout du composant card dans le dashboard (in progress)**


[32612](https://github.com/Afakto/afakto/commit/326122290b66a33) daniela.crv *2024-03-25 13:03:30*


## 0.11.0 (2024-04-06)

### Features

-  **front**  simplify drawer organisation ([d770e](https://github.com/Afakto/afakto/commit/d770eed877ab9ad) echarp)  
-  **front**  remove shadows and background color ([d3bad](https://github.com/Afakto/afakto/commit/d3bad8fc26ff10c) echarp)  
-  **settings**  setup working settings page ([c6015](https://github.com/Afakto/afakto/commit/c60151ec32b53d6) echarp)  
-  **dashboard**  display total factor debt ([bfa58](https://github.com/Afakto/afakto/commit/bfa58e9c3c619e3) echarp)  
-  **dashboard**  add all cards, fake and incomplete ([bc0ec](https://github.com/Afakto/afakto/commit/bc0ec22735fa820) echarp)  
-  **dashboard**  add historical graph ([94b15](https://github.com/Afakto/afakto/commit/94b1577f347901f) echarp)  
-  **front**  complete menu with proper settings page ([799b9](https://github.com/Afakto/afakto/commit/799b98998af1c0d) echarp)  
-  **exports**  finish bnp remittance export file ([6837e](https://github.com/Afakto/afakto/commit/6837e4fb717ec6f) echarp)  
-  **exports**  finish bnp remittance export file ([109de](https://github.com/Afakto/afakto/commit/109dec781c56eaf) echarp)  
-  **dashboard**  setup the total factor debt card ([f9175](https://github.com/Afakto/afakto/commit/f917573a4a2967d) echarp)  
-  **front**  order contracts by activation date ([aea2e](https://github.com/Afakto/afakto/commit/aea2e31dc66c910) echarp)  
-  **front**  attempt to simplify auth0 call ([9d157](https://github.com/Afakto/afakto/commit/9d157364cc242d4) echarp)  
-  **imports**  manage mt940 and mt942 files ([6bcaf](https://github.com/Afakto/afakto/commit/6bcaff992750b28) echarp)  
-  **exports**  setup an initial BNP ATC balance file ([5dc69](https://github.com/Afakto/afakto/commit/5dc69ba7ea11695) echarp)  
-  **company**  remove the "process statements" button ([3acef](https://github.com/Afakto/afakto/commit/3acefaf3bbe8994) echarp)  
-  **exports**  finish bnp balance export file ([23c78](https://github.com/Afakto/afakto/commit/23c783911a63840) echarp)  
-  **front**  sort by date or name appropriately ([01523](https://github.com/Afakto/afakto/commit/01523ae1e7d9b46) echarp)  

### Bug Fixes

-  **tests**  correct some architecture setup ([9d9bc](https://github.com/Afakto/afakto/commit/9d9bc661d0297e3) echarp)  
-  **tests**  correct an archival directory creation ([93721](https://github.com/Afakto/afakto/commit/93721bb599c4355) echarp)  

### Other changes

**ajout du composant card dans le dashboard (in progress)**


[a3e07](https://github.com/Afakto/afakto/commit/a3e07de3d9a1a43) daniela.crv *2024-04-06 17:00:55*

**Merge pull request #47 from khanhpham100398/feature/azure-file-exchanger-vm-creation**

* Feature/azure file exchanger vm creation 

[f5787](https://github.com/Afakto/afakto/commit/f57873740aa3fe0) Phạm Khanh *2024-04-03 04:19:42*

**Merge pull request #46 from khanhpham100398/feature/azure-reorganize-subnets**

* Feature/azure reorganize subnets 

[990d5](https://github.com/Afakto/afakto/commit/990d59fc24e5c8c) Phạm Khanh *2024-04-03 03:33:52*

**[terraform] - revert application name back to afakto**


[5fcad](https://github.com/Afakto/afakto/commit/5fcad33468318c8) PHAM Khanh *2024-04-02 10:07:36*

**[terraform] - update subnet 2 to have a service endpoint**


[a3862](https://github.com/Afakto/afakto/commit/a386236da4e6c29) PHAM Khanh *2024-04-01 09:04:47*

**[terraform] - add a service endpoint for subnet 2 to storage account**


[fd2be](https://github.com/Afakto/afakto/commit/fd2be273c298a03) PHAM Khanh *2024-04-01 08:56:31*

**[terraform] - add a linux vm named file-exchanger**


[7966b](https://github.com/Afakto/afakto/commit/7966bb987c8e834) PHAM Khanh *2024-04-01 07:32:57*

**[terraform] - add subnet #2**


[e46fe](https://github.com/Afakto/afakto/commit/e46fe96906f12b9) PHAM Khanh *2024-04-01 07:02:11*

**[terraform] - revert application name back to afakto**


[ae136](https://github.com/Afakto/afakto/commit/ae1367c0af2e641) PHAM Khanh *2024-04-01 06:30:00*

**[terraform] - fix virtual-network/outputs.tf**


[103bf](https://github.com/Afakto/afakto/commit/103bfd230ccda92) PHAM Khanh *2024-04-01 04:57:43*

**[terraform] - replace the old subnet with a new one**


[3889b](https://github.com/Afakto/afakto/commit/3889b2f9e782f61) PHAM Khanh *2024-04-01 04:51:05*

**[terraform] - change module folder name to virtual-network**


[5ccef](https://github.com/Afakto/afakto/commit/5ccef122e06dde5) PHAM Khanh *2024-03-29 09:56:01*

**[terraform] - change application_name variable to afaktolab**


[90dcf](https://github.com/Afakto/afakto/commit/90dcf1de38f5a5a) PHAM Khanh *2024-03-29 08:12:33*

**Merge pull request #45 from khanhpham100398/feature/azure-upgrade-postgresql-to-v16**

* Feature/azure upgrade postgresql to v16 

[3038e](https://github.com/Afakto/afakto/commit/3038e0132b1d06d) Phạm Khanh *2024-03-26 04:34:36*

**Merge branch 'env-prod' into feature/azure-upgrade-postgresql-to-v16**


[76647](https://github.com/Afakto/afakto/commit/76647b58e2b86ce) Phạm Khanh *2024-03-26 04:26:58*

**Merge pull request #43 from dnguyen15mantu/feature/azure-asp-plan-resize**

* Change ASP sku to use variable and downsize the ASP to B2 

[0bdf8](https://github.com/Afakto/afakto/commit/0bdf870efff8fe9) dnguyen15mantu *2024-03-26 04:12:22*

**[terraform] - revert application name back to afakto**


[59dba](https://github.com/Afakto/afakto/commit/59dba7195d4182a) PHAM Khanh *2024-03-25 11:14:34*

**[github workflow] - allow disable IP access to storage account as always**


[de628](https://github.com/Afakto/afakto/commit/de6283c9aed2f0e) PHAM Khanh *2024-03-25 04:16:39*

**[terraform] - revert postgresql back to version 16 after testing**


[c7863](https://github.com/Afakto/afakto/commit/c7863e94dce1231) PHAM Khanh *2024-03-25 04:11:47*

**[terraform] - revert postgresql back to version 13 temporarily for testing**


[5ca32](https://github.com/Afakto/afakto/commit/5ca32fcb3b2c125) PHAM Khanh *2024-03-25 02:28:56*

**[terraform] - fix module postgresql to have version suffix with its name**


[25878](https://github.com/Afakto/afakto/commit/258788b20914595) PHAM Khanh *2024-03-22 05:08:45*

**[terraform] - update module postgresql to have version suffix with its name**


[2f2b8](https://github.com/Afakto/afakto/commit/2f2b874ad40ba45) PHAM Khanh *2024-03-22 04:43:58*

**[terraform] - update module app-service to use app_service_plan_sku_name variable**


[04b51](https://github.com/Afakto/afakto/commit/04b51f6634137a3) PHAM Khanh *2024-03-22 03:39:30*

**[terraform] - upgrade postgresql to version 16**


[05922](https://github.com/Afakto/afakto/commit/05922a47607cba7) PHAM Khanh *2024-03-22 03:33:55*

**[terraform] - update module postgresql to use database_version variable**


[963e0](https://github.com/Afakto/afakto/commit/963e027b964f37d) PHAM Khanh *2024-03-22 03:32:27*

**[terraform] - change application_name to afaktolab**


[765a1](https://github.com/Afakto/afakto/commit/765a160ef65fa91) PHAM Khanh *2024-03-22 03:26:31*

**Change ASP sku to use varible and downsize the ASP to B2**


[70f4b](https://github.com/Afakto/afakto/commit/70f4bffecce6d9c) NGUYEN Duc Tung *2024-03-21 08:07:52*


## 0.10.3 (2024-03-25)

### Features

-  **imports**  manage buyer approved amount ([cd93a](https://github.com/Afakto/afakto/commit/cd93af04d2d77bc) echarp)  
-  **front**  sort invoices by their date ([8cc2a](https://github.com/Afakto/afakto/commit/8cc2a0c38844e81) echarp)  
-  **front**  sort bank transactions by their date (default) ([c234b](https://github.com/Afakto/afakto/commit/c234beeaf1c26f2) echarp)  

### Bug Fixes

-  **back**  allow country multiple presences in JSON ([15e11](https://github.com/Afakto/afakto/commit/15e118bf3d9cfe3) echarp)  
-  **back**  change some configuration to limit improper logs ([98a23](https://github.com/Afakto/afakto/commit/98a235aa859321c) echarp)  
-  **front**  correct xsrf cookie handling following jhipster 8.2 upgrade ([492d5](https://github.com/Afakto/afakto/commit/492d56939dadc7a) echarp)  

### Other changes

**Merge branch 'main' of github.com:mantucorp/afakto**


[2ee0b](https://github.com/Afakto/afakto/commit/2ee0b5664b158b6) daniela.crv *2024-03-21 15:27:31*

**test add component card dashboard (in progess)**


[7d0d8](https://github.com/Afakto/afakto/commit/7d0d847745b8ee1) daniela.crv *2024-03-21 15:27:26*


## 0.10.2 (2024-03-21)

### Bug Fixes

-  **imports**  correct i22 amount ([0f461](https://github.com/Afakto/afakto/commit/0f461aaca050e28) echarp)  
-  **tests**  correct some equality tests ([849d9](https://github.com/Afakto/afakto/commit/849d9eea6cb98ee) echarp)  

## 0.10.1 (2024-03-21)

### Features

-  **imports**  finalize buyer, invoice and bank transactions imports ([48fd8](https://github.com/Afakto/afakto/commit/48fd89504bc9aac) echarp)  
-  **i4c**  display factor amount in buyer and invoice lists ([7f41c](https://github.com/Afakto/afakto/commit/7f41c6ae5522114) echarp)  
-  **front**  clean smallish screen elements ([48d4f](https://github.com/Afakto/afakto/commit/48d4fb5c57e6dcc) echarp)  

### Other changes

**Merge branch 'main' of github.com:mantucorp/afakto**


[be28b](https://github.com/Afakto/afakto/commit/be28b6b8ede0937) daniela.crv *2024-03-21 09:17:03*

**add colums list buyers, add dashboard component (in progess), update title**


[993b2](https://github.com/Afakto/afakto/commit/993b28d7a775a34) daniela.crv *2024-03-21 09:11:58*

**Merge branch 'main' into env-prod**


[e0e4c](https://github.com/Afakto/afakto/commit/e0e4c5b0c01f806) echarp *2024-03-19 16:14:13*

**Merge pull request #39 from mantucorp/feature/azure-files-mounting-and-application-insights-enabled**

* Feature/azure files mounting and application insights enabled 

[f8ab0](https://github.com/Afakto/afakto/commit/f8ab028c7892ef9) Phạm Khanh *2024-03-19 03:30:27*

**Merge pull request #38 from khanhpham100398/feature/azure-files-mounting-and-application-insights-enabled**

* Feature/azure files mounting and application insights enabled 

[a838d](https://github.com/Afakto/afakto/commit/a838d390cee9d6c) Phạm Khanh *2024-03-18 09:04:56*

**[terraform] - revert application name back to afakto**


[23b71](https://github.com/Afakto/afakto/commit/23b716be4b0ab4c) PHAM Khanh *2024-03-18 08:58:58*

**Merge branch 'mantucorp:env-prod' into env-prod**


[27e95](https://github.com/Afakto/afakto/commit/27e952032ef770d) Phạm Khanh *2024-03-18 08:32:42*

**[terraform] - enable app insights for linux app service**


[e8932](https://github.com/Afakto/afakto/commit/e89321a9dd26e43) PHAM Khanh *2024-03-15 09:23:34*

**[terraform] - ignore changes with storage account**


[75dc2](https://github.com/Afakto/afakto/commit/75dc2a9183ac246) PHAM Khanh *2024-03-15 07:17:11*

**[terraform] - ignore changes with integrated vnet**


[a41d7](https://github.com/Afakto/afakto/commit/a41d75c304770ff) PHAM Khanh *2024-03-15 03:49:11*

**[github workflow] - decrease wait time to 30s in authorize IP step**


[29f2e](https://github.com/Afakto/afakto/commit/29f2ed6ab735ae6) PHAM Khanh *2024-03-15 02:30:05*

**[github workflow] - increase wait time to 60s in authorize IP step**


[74809](https://github.com/Afakto/afakto/commit/748091fd0b6ae34) PHAM Khanh *2024-03-14 10:37:36*

**[github workflow] - increase wait time to 40s in authorize IP step**


[cce3f](https://github.com/Afakto/afakto/commit/cce3ff0d52abbea) PHAM Khanh *2024-03-14 10:32:53*

**Merge branch 'env-prod' of github.com:khanhpham100398/afakto into env-prod**


[b070e](https://github.com/Afakto/afakto/commit/b070ec019b4a209) PHAM Khanh *2024-03-14 10:30:35*

**[terraform] - update mount point path to /var/afakto in main.tf**


[8acab](https://github.com/Afakto/afakto/commit/8acab299b44d618) PHAM Khanh *2024-03-14 10:28:51*

**[github workflow] - increase wait time in authrize IP step**


[25402](https://github.com/Afakto/afakto/commit/254023a26547fa1) PHAM Khanh *2024-03-14 09:44:16*

**[terraform] - change mount point path to /var/afo in main.tf**


[2a35c](https://github.com/Afakto/afakto/commit/2a35c3f9d9c3c8b) PHAM Khanh *2024-03-14 08:03:29*

**[github workflow] - fix manage-infrastructure job**


[19606](https://github.com/Afakto/afakto/commit/19606057cc39f1d) PHAM Khanh *2024-03-13 11:20:51*

**[github workflow] - add allow agent ip steps to manage-infrastructure job**


[4d334](https://github.com/Afakto/afakto/commit/4d3345add5331a7) PHAM Khanh *2024-03-13 11:13:01*

**[terraform] - fix main.tf**


[c5842](https://github.com/Afakto/afakto/commit/c584266ce64ae42) PHAM Khanh *2024-03-13 04:20:01*

**[terraform] - fix nfs-files-storage module**


[e7c76](https://github.com/Afakto/afakto/commit/e7c76a084fa30d4) PHAM Khanh *2024-03-13 04:14:10*

**[terraform] - integrate the vnet to linux app service**


[cf9c5](https://github.com/Afakto/afakto/commit/cf9c5d763022088) PHAM Khanh *2024-03-13 04:01:46*

**[terraform] - fix nfs-files-storage module**


[bf7ca](https://github.com/Afakto/afakto/commit/bf7ca1e32ec6636) PHAM Khanh *2024-03-11 10:29:00*

**Add nfs files storage**


[f93a1](https://github.com/Afakto/afakto/commit/f93a101d622b6fb) PHAM Khanh *2024-03-11 10:20:57*

**Change application_name to afaktolab**


[ba672](https://github.com/Afakto/afakto/commit/ba67230deb12a02) PHAM Khanh *2024-03-11 07:20:29*

**Merge branch 'mantucorp:env-prod' into env-prod**


[16c96](https://github.com/Afakto/afakto/commit/16c96303b0098d0) Phạm Khanh *2024-03-11 07:17:32*

**Merge pull request #1 from dnguyen15mantu/feature/azure-application-insights-migration**

* Migrate the Application Insights to use the mode Log Analytics Workspace 

[e814d](https://github.com/Afakto/afakto/commit/e814d43d6f50f74) dnguyen15mantu *2024-02-27 10:51:41*


## 0.10.0 (2024-03-19)

### Features

-  **back**  import i4c files from bnp factor (no ftp) ([fe394](https://github.com/Afakto/afakto/commit/fe3946378b6307d) echarp)  
-  **backend**  add a file service, to move and gzip some resources ([d523d](https://github.com/Afakto/afakto/commit/d523d9140ca8a1c) echarp)  
-  **invoice**  change LocalDatetime to simpler LocalDate ([987a8](https://github.com/Afakto/afakto/commit/987a809b215bb52) echarp)  
-  **dashboard**  add backend starter kit! :) ([2c2de](https://github.com/Afakto/afakto/commit/2c2de2974ba20ae) echarp)  

### Bug Fixes

-  **backend**  change more Datetime to LocalDate ([67d1e](https://github.com/Afakto/afakto/commit/67d1ee7dcf00564) echarp)  
-  **backend**  remove useless mapper ([3a4f0](https://github.com/Afakto/afakto/commit/3a4f0886fcf7bef) echarp)  

### Other changes

**Merge branch 'main' of github.com:mantucorp/afakto**


[f7aaf](https://github.com/Afakto/afakto/commit/f7aafaf6b8071ce) daniela.crv *2024-03-15 08:07:59*

**apply new font in title, update Menu and component BTitle (in progress)**


[be365](https://github.com/Afakto/afakto/commit/be36540f782da3a) daniela.crv *2024-03-15 08:07:29*


## 0.9.2 (2024-03-12)

### Features

-  **bank transaction**  improve display and reference number ([a9fc9](https://github.com/Afakto/afakto/commit/a9fc994d5c861ab) echarp)  
-  **bank transaction**  add categories ([ad48d](https://github.com/Afakto/afakto/commit/ad48d90dce7da0d) echarp)  
-  **upload**  improve error handling, with more helpful messages ([b5e9c](https://github.com/Afakto/afakto/commit/b5e9cc89419a8f1) echarp)  
-  **upload**  better error display, with raw data ([5d127](https://github.com/Afakto/afakto/commit/5d12742ae77f8d4) echarp)  

### Other changes

**Merge branch 'main' of github.com:mantucorp/afakto**


[7b8d1](https://github.com/Afakto/afakto/commit/7b8d1ea3ea1d83a) daniela.crv *2024-03-11 09:19:08*

**Add dashboardPage with a card, update Menu**


[b98fc](https://github.com/Afakto/afakto/commit/b98fcd02e49b5d2) daniela.crv *2024-03-11 09:18:48*


## 0.9.1 (2024-03-07)

### Features

-  **buyer**  allow to upload excel buyer files ([eb678](https://github.com/Afakto/afakto/commit/eb6786d8a5440e5) echarp)  

## 0.9.0 (2024-03-06)

### Features

-  **invoice**  allow to upload excel invoice files ([53da5](https://github.com/Afakto/afakto/commit/53da5891796f90e) echarp)  
-  **front**  clean invoice list and edition ([4d33f](https://github.com/Afakto/afakto/commit/4d33f6f8da97f57) echarp)  
-  **invoice**  refactor model to have one currency declared per invoice ([714c4](https://github.com/Afakto/afakto/commit/714c49b2295edf8) echarp)  
-  **front**  add a css loading marker, to eventually replace the loading variable used in many screens ([810cd](https://github.com/Afakto/afakto/commit/810cd662caddbf6) echarp)  
-  **front**  clean logout and main index vue ([2b19d](https://github.com/Afakto/afakto/commit/2b19de14e12f5aa) echarp)  
-  **back**  import i22 files from bnp factor (no ftp) ([d92dd](https://github.com/Afakto/afakto/commit/d92dd5342b286a2) echarp)  

### Bug Fixes

-  **front**  remove css loading status on axios errors ([1a97c](https://github.com/Afakto/afakto/commit/1a97c5cb6c70f40) echarp)  

### Other changes

**Add help Page**


[f246e](https://github.com/Afakto/afakto/commit/f246ec64538badc) daniela.crv *2024-03-06 10:46:39*

**Merge branch 'main' of github.com:mantucorp/afakto**


[15810](https://github.com/Afakto/afakto/commit/158101c9a9352d3) daniela.crv *2024-03-05 17:28:00*

**change style Menu and add Settings page**


[477a8](https://github.com/Afakto/afakto/commit/477a8464abb3aaa) daniela.crv *2024-03-05 17:21:50*

**Merge branch 'env-prod'**


[07551](https://github.com/Afakto/afakto/commit/0755107d95c543f) echarp *2024-02-28 09:07:36*

**Menu - add new items (help, settings)**


[3b098](https://github.com/Afakto/afakto/commit/3b0984e0bed786c) daniela.crv *2024-02-27 17:04:16*

**menu - change icon compagnies, add item menu dashbaord**


[89dcd](https://github.com/Afakto/afakto/commit/89dcd335ef12f69) daniela.crv *2024-02-27 14:17:03*

**Merge branch 'main' of github.com:mantucorp/afakto**


[3cb0e](https://github.com/Afakto/afakto/commit/3cb0e39907d0086) daniela.crv *2024-02-27 09:35:09*

**change logo in Home Page, update Menu**


[f5e0d](https://github.com/Afakto/afakto/commit/f5e0df49fcc1b4f) daniela.crv *2024-02-26 16:21:20*


## 0.8.0 (2024-02-28)

### Bug Fixes

-  **build**  remove another misplaced reference to jdbc driver ([5b9bc](https://github.com/Afakto/afakto/commit/5b9bc3c800d8e67) echarp)  
-  **build**  correct jdbc driver for liquibase ([233c4](https://github.com/Afakto/afakto/commit/233c40e46016234) echarp)  

### Other changes

**Menu - add new items (help, settings)**


[835e0](https://github.com/Afakto/afakto/commit/835e087effc6bae) daniela.crv *2024-02-28 09:07:20*

**change logo in Home Page, update Menu**


[75c3d](https://github.com/Afakto/afakto/commit/75c3d38821a57ce) daniela.crv *2024-02-28 09:07:20*

**menu - change icon compagnies, add item menu dashbaord**


[10ca9](https://github.com/Afakto/afakto/commit/10ca91133083752) daniela.crv *2024-02-28 09:07:20*

**Merge pull request #31 from dnguyen15mantu/feature/azure-application-insights-migration**

* Feature/azure application insights migration 

[4d9e3](https://github.com/Afakto/afakto/commit/4d9e32e6bd88593) dnguyen15mantu *2024-02-28 04:31:05*

**Revert the keyvault main.tf**


[b0c51](https://github.com/Afakto/afakto/commit/b0c516c094a959d) dnguyen15mantu *2024-02-27 11:14:02*

**Revert the main variables.tf**


[190bc](https://github.com/Afakto/afakto/commit/190bc554bf3b9a6) dnguyen15mantu *2024-02-27 11:13:14*

**Merge branch 'env-prod' into feature/azure-application-insights-migration**


[52295](https://github.com/Afakto/afakto/commit/52295b438a82ac7) dnguyen15mantu *2024-02-27 10:51:12*

**Change application name to afaktolab to test.**


[ab7e2](https://github.com/Afakto/afakto/commit/ab7e2cf368f0ee9) PHAM Khanh *2024-02-27 09:18:41*

**Fake changes to trigger the pipeline**


[fcebd](https://github.com/Afakto/afakto/commit/fcebdd233ee69f9) PHAM Khanh *2024-02-27 09:10:57*

**Migrate the Application Insights to use the mode Log Analytics Workspace**


[ad14f](https://github.com/Afakto/afakto/commit/ad14f8ecfa3eb02) NGUYEN Duc Tung *2024-02-27 08:29:53*


## 0.7.0 (2024-02-26)

### Features

-  **front**  clean bank transaction list and edit (mostly) ([1a494](https://github.com/Afakto/afakto/commit/1a494f227822992) echarp)  
-  **front**  change menu wording, pluralized ([51865](https://github.com/Afakto/afakto/commit/51865704d46bd88) echarp)  
-  **front**  unify screen button placements ([ff980](https://github.com/Afakto/afakto/commit/ff980100d6f24e5) echarp)  

### Other changes

**Merge branch 'main' of github.com:mantucorp/afakto**


[0f9bc](https://github.com/Afakto/afakto/commit/0f9bce08f7cabc8) daniela.crv *2024-02-26 08:25:34*

**add font neon file**


[ab2b0](https://github.com/Afakto/afakto/commit/ab2b0bd34e9cb43) daniela.crv *2024-02-23 16:18:55*

**Merge branch 'main' of github.com:mantucorp/afakto**


[df058](https://github.com/Afakto/afakto/commit/df0580453fe205c) daniela.crv *2024-02-23 13:49:41*

**change default color, adding news colors varibles**


[73cda](https://github.com/Afakto/afakto/commit/73cda4e4acf6b91) daniela.crv *2024-02-23 13:49:27*


## 0.6.0 (2024-02-19)

### Features

-  **front**  add buyer import samples ([867e7](https://github.com/Afakto/afakto/commit/867e72792bec96b) echarp)  
-  **front**  allow multiple invoice upload, plus file samples ([ee5d6](https://github.com/Afakto/afakto/commit/ee5d6ee86e0a327) echarp)  
-  **front**  reorganise main menu ([3c811](https://github.com/Afakto/afakto/commit/3c811c9ddcb7753) echarp)  

### Bug Fixes

-  **front**  remove some axios error interceptor ([06d36](https://github.com/Afakto/afakto/commit/06d3622262520e0) echarp)  

## 0.5.1 (2024-02-16)

### Features

-  **front**  improve request limit presentation ([3d6ab](https://github.com/Afakto/afakto/commit/3d6ab29537a1041) echarp)  
-  **front**  add some number type mask ([bd1fb](https://github.com/Afakto/afakto/commit/bd1fb2754d9d1e0) echarp)  
-  **db**  populate factors table ([9f51d](https://github.com/Afakto/afakto/commit/9f51d743bb7ade1) echarp)  

## 0.5.0 (2024-02-15)

### Features

-  **front**  localize monetary inputs ([96611](https://github.com/Afakto/afakto/commit/9661191a02ffbcb) echarp)  
-  **all**  add number and number type to companies ([3141b](https://github.com/Afakto/afakto/commit/3141b1fcc06b942) echarp)  
-  **front**  format more currencies ([8bc06](https://github.com/Afakto/afakto/commit/8bc064ab8b2d954) echarp)  
-  **front**  format dates and numbers according to locale ([3e152](https://github.com/Afakto/afakto/commit/3e15219d2c23da6) echarp)  

### Bug Fixes

-  **db**  try to correct liquibase changesets ([78e8b](https://github.com/Afakto/afakto/commit/78e8bd8e4aa0a0a) echarp)  

## 0.4.0 (2024-02-12)

### Features

-  **front**  warn user when invoice currency is not allowed ([521e9](https://github.com/Afakto/afakto/commit/521e98405c58746) echarp)  
-  **front**  reorganize invoice screens ([bb2ec](https://github.com/Afakto/afakto/commit/bb2ecd979c01985) echarp)  
-  **front**  translate some labels ([869cb](https://github.com/Afakto/afakto/commit/869cb49c60d98a3) echarp)  
-  **front**  translate some buyer labels ([915b6](https://github.com/Afakto/afakto/commit/915b60a7c31c5c9) echarp)  
-  **front**  delete all languages except fr and en ([219b5](https://github.com/Afakto/afakto/commit/219b590f949639c) echarp)  
-  **all**  rename client into buyer ([5f70e](https://github.com/Afakto/afakto/commit/5f70eefa94c25c5) echarp)  
-  **back**  rename client_contact into contact ([f15e6](https://github.com/Afakto/afakto/commit/f15e6a183936924) echarp)  

### Bug Fixes

-  **db**  correct liquibase diff generation ([60a7c](https://github.com/Afakto/afakto/commit/60a7cd04bf2303c) echarp)  
-  **front**  allow avatars from more sources ([345b2](https://github.com/Afakto/afakto/commit/345b2468c09d0be) echarp)  

## 0.3.0 (2024-02-07)

### Features

-  **company**  add parent company and address ([bbc17](https://github.com/Afakto/afakto/commit/bbc170543fc2453) echarp)  
-  **back**  factorise client_address to address, and add it to company ([bfb2f](https://github.com/Afakto/afakto/commit/bfb2f09eafee25e) echarp)  

### Bug Fixes

-  **build**  rollback modernizer, which was too modern :) ([1623f](https://github.com/Afakto/afakto/commit/1623fe9e1a82560) echarp)  
-  **build**  correct property name ([abafb](https://github.com/Afakto/afakto/commit/abafb0d908eb0c0) echarp)  
-  **front**  correct company column names ([99f4a](https://github.com/Afakto/afakto/commit/99f4aab7867ac44) echarp)  

### Other changes

**Version 0.3.0**


[f4f2a](https://github.com/Afakto/afakto/commit/f4f2a6763b9f91d) echarp *2024-02-07 19:25:05*

**Merge branch 'main' into env-prod**


[c330a](https://github.com/Afakto/afakto/commit/c330a7424e0d768) echarp *2024-02-06 14:02:06*

**Merge pull request #29 from mantucorp/main**

* Version 0.2.0 

[3042a](https://github.com/Afakto/afakto/commit/3042aeaa82200aa) echarp *2024-02-01 20:39:43*

**Merge pull request #28 from mantucorp/main**

* Version 0.1.2 

[7b16e](https://github.com/Afakto/afakto/commit/7b16ea91a2b523c) echarp *2024-01-31 16:36:39*

**Merge pull request #23 from mantucorp/main**

* Deploy version 0.1.1-SNAPSHOT 

[65198](https://github.com/Afakto/afakto/commit/6519881a74c63af) echarp *2024-01-26 10:35:36*

**Merge pull request #20 from mantucorp/main**

* Deploy version 0.1.0-SNAPSHOT 

[640ce](https://github.com/Afakto/afakto/commit/640cefcfc3ff968) echarp *2024-01-25 09:02:04*


## 0.2.1 (2024-02-06)

### Bug Fixes

-  **front**  correct currency selector ([b1c16](https://github.com/Afakto/afakto/commit/b1c16b3dc770134) echarp)  
-  **front**  accept/reject credit limit should work both in the client and list screens ([fb61a](https://github.com/Afakto/afakto/commit/fb61a2ff7d11476) echarp)  
-  **front**  clean quasar base items ([85721](https://github.com/Afakto/afakto/commit/85721439dc8e6e5) echarp)  

### Other changes

**Version 0.2.1**


[f7ca0](https://github.com/Afakto/afakto/commit/f7ca05e47bc6dc6) echarp *2024-02-06 14:01:45*


## 0.2.0 (2024-02-01)

### Bug Fixes

-  **contract**  the external insurance sub form works again ([61a75](https://github.com/Afakto/afakto/commit/61a757c4990ccac) echarp)  
-  **db**  add missing foreign key from contract to creditInsurancePolicy ([11e95](https://github.com/Afakto/afakto/commit/11e9516a9eb2453) echarp)  
-  credit insurance internal/external can be saved. ([7e24f](https://github.com/Afakto/afakto/commit/7e24f2c4760475f) echarp)  

### Other changes

**Version 0.2.0**

* Contract edition: external insurance policy 

[695af](https://github.com/Afakto/afakto/commit/695afffc3e23a30) echarp *2024-02-01 20:15:24*


## 0.1.2 (2024-01-31)

### Bug Fixes

-  Contract countries selection working again ([60cc1](https://github.com/Afakto/afakto/commit/60cc1c6d59e2200) echarp)  

### Other changes

**Version 0.1.2**


[7cd75](https://github.com/Afakto/afakto/commit/7cd754c6faebdb0) echarp *2024-01-31 16:26:55*

**Selectors not using elastic search url anymore (for countries, companies, etc.)**


[f8abc](https://github.com/Afakto/afakto/commit/f8abc3e16916bc3) echarp *2024-01-31 15:34:11*

**Small changes, to logs and cache config**


[cc449](https://github.com/Afakto/afakto/commit/cc449c4574bb031) echarp *2024-01-30 15:16:32*

**Authorize blob loading**


[3e310](https://github.com/Afakto/afakto/commit/3e3103ee80121f9) echarp *2024-01-30 10:12:48*

**Simplified package.json, enough for quasar itself, standalone**


[135b5](https://github.com/Afakto/afakto/commit/135b5c6c5d4c7f2) echarp *2024-01-29 21:53:07*

**Debug level more rational**


[e9062](https://github.com/Afakto/afakto/commit/e9062d88f23c09d) echarp *2024-01-29 16:50:34*

**backend start should not start tests**


[f3bcf](https://github.com/Afakto/afakto/commit/f3bcf57300f4eb4) echarp *2024-01-27 15:51:09*

**cypress removed from quasar, but still present at root**


[b753b](https://github.com/Afakto/afakto/commit/b753b38739bc7a5) echarp *2024-01-27 15:50:49*

**quasar building directly into the target directory**


[955ef](https://github.com/Afakto/afakto/commit/955ef2bec22e08a) echarp *2024-01-27 13:17:55*

**Removed husky (which does not seem useful)**


[54ada](https://github.com/Afakto/afakto/commit/54ada70043a0b49) echarp *2024-01-27 13:16:54*


## 0.1.1 (2024-01-26)

### Other changes

**Version 0.1.1**


[794bb](https://github.com/Afakto/afakto/commit/794bb44dcd570c0) echarp *2024-01-26 10:25:42*

**quasar dev setup in npm**


[22b1d](https://github.com/Afakto/afakto/commit/22b1db985beb633) echarp *2024-01-26 09:42:33*

**Removed remants of elastic search configuration**


[a95f3](https://github.com/Afakto/afakto/commit/a95f3a5dbf76cc7) echarp *2024-01-26 09:41:26*

**Missing labels and translations**


[5b55d](https://github.com/Afakto/afakto/commit/5b55d4b4e921455) echarp *2024-01-26 09:36:31*

**Authorize avatars from google and github**


[4dd7d](https://github.com/Afakto/afakto/commit/4dd7d5363d50c72) echarp *2024-01-25 17:15:51*

**Unblocks java 21, but still targets java 17. Refs #19**


[2a40e](https://github.com/Afakto/afakto/commit/2a40ec097b03ece) echarp *2024-01-25 16:59:28*


## 0.1.0 (2024-01-24)

### Other changes

**Version 0.1.0**


[e9df2](https://github.com/Afakto/afakto/commit/e9df2b5f332dd2f) echarp *2024-01-24 09:23:31*

**First step for #19**


[2ec05](https://github.com/Afakto/afakto/commit/2ec0527e009d243) echarp *2024-01-24 09:20:14*

**Removed unused java imports**


[3bda7](https://github.com/Afakto/afakto/commit/3bda71ed6e13838) echarp *2024-01-23 18:26:36*

**Cleaned up markdown documentation**


[91658](https://github.com/Afakto/afakto/commit/916589b49ec53b4) echarp *2024-01-23 11:36:40*

**Replacing auth0 rules with auth0 actions**


[7b3fa](https://github.com/Afakto/afakto/commit/7b3faf56a23569b) echarp *2024-01-22 16:38:15*

**Cleaner doc (no more formatting warnings)**


[cff7a](https://github.com/Afakto/afakto/commit/cff7addd518f245) echarp *2024-01-22 16:37:31*

**Merge pull request #15 from mantucorp/feature/simon-test**

* test 

[faaa9](https://github.com/Afakto/afakto/commit/faaa984e54b7da4) echarp *2024-01-22 14:28:55*

**Merge pull request #17 from mantucorp/feature/add-under-factor**

* add under factor 

[a0e19](https://github.com/Afakto/afakto/commit/a0e197cc5ef0137) mantucorp *2023-11-03 14:46:02*

**add under factor**


[bc695](https://github.com/Afakto/afakto/commit/bc695e27d0fb4dd) Jean-Claude ANTONIO *2023-11-02 14:33:01*

**Merge pull request #16 from mantucorp/feature/remove-elastic**

* Feature/remove elastic 

[b7c85](https://github.com/Afakto/afakto/commit/b7c8597bd221733) mantucorp *2023-11-02 09:50:48*

**Fix query**


[b4825](https://github.com/Afakto/afakto/commit/b482570fbae0a5b) Jean-Claude ANTONIO *2023-10-30 17:05:25*

**add ignore case**


[58b00](https://github.com/Afakto/afakto/commit/58b00a3dd825c34) Jean-Claude ANTONIO *2023-10-30 16:48:05*

**fix query**


[e6c78](https://github.com/Afakto/afakto/commit/e6c785873abf79a) Jean-Claude ANTONIO *2023-10-30 16:40:45*

**first step**


[48564](https://github.com/Afakto/afakto/commit/485640e21c4fcb3) Jean-Claude ANTONIO *2023-10-30 16:23:47*

**remove factor_code**


[d13e4](https://github.com/Afakto/afakto/commit/d13e431b47e66ad) Jean-Claude ANTONIO *2023-10-27 15:57:46*

**add contract mapper to ELK**


[057af](https://github.com/Afakto/afakto/commit/057af2dc6734282) Jean-Claude ANTONIO *2023-10-27 12:20:44*

**Merge pull request #14 from mantucorp/feature/cicd-actions-auto-delete-old-workflows-runs**

* Feature/cicd actions auto delete old workflows runs 

[4535c](https://github.com/Afakto/afakto/commit/4535c8260ef15bb) mantucorp *2023-09-14 15:09:24*

**Create a new Actions workflow auto-delete-old-workflows-runs.yml**


[c4560](https://github.com/Afakto/afakto/commit/c45605ceaf8d8dc) dnguyen15mantu *2023-09-13 04:32:36*

**fix liquibase**


[b5c04](https://github.com/Afakto/afakto/commit/b5c0443e59dc14e) Jean-Claude ANTONIO *2023-09-07 12:39:11*

**=redeploy**


[94fe2](https://github.com/Afakto/afakto/commit/94fe2625d7ff062) Jean-Claude ANTONIO *2023-09-07 12:22:56*

**Merge branch 'main' into env-prod**


[267a6](https://github.com/Afakto/afakto/commit/267a6d962f10764) Jean-Claude ANTONIO *2023-09-07 08:53:17*

**test fix reinding**


[bf0c8](https://github.com/Afakto/afakto/commit/bf0c82225697d3a) Jean-Claude ANTONIO *2023-09-07 08:48:46*

**Merge branch 'main' into env-prod**


[6ff02](https://github.com/Afakto/afakto/commit/6ff02ab943ecc44) Jean-Claude ANTONIO *2023-09-07 07:13:29*

**fix authorities tests**


[4578f](https://github.com/Afakto/afakto/commit/4578f34f3dbffff) Jean-Claude ANTONIO *2023-09-05 13:03:07*

**Merge branch 'main' into env-prod**


[806ae](https://github.com/Afakto/afakto/commit/806aefb945d6005) Jean-Claude ANTONIO *2023-08-31 16:31:55*

**remove pops**


[0be92](https://github.com/Afakto/afakto/commit/0be92304793da61) Jean-Claude ANTONIO *2023-08-31 16:31:28*

**remove pops**


[f4132](https://github.com/Afakto/afakto/commit/f4132b7a2933938) Jean-Claude ANTONIO *2023-08-31 16:29:26*

**Merge branch 'main' into env-prod**


[87379](https://github.com/Afakto/afakto/commit/873791e8a95aea0) Jean-Claude ANTONIO *2023-08-31 16:13:49*

**Add info for required fields**


[155f8](https://github.com/Afakto/afakto/commit/155f8e001a38168) Jean-Claude ANTONIO *2023-08-31 16:12:19*

**Merge branch 'main' into env-prod**


[95821](https://github.com/Afakto/afakto/commit/95821262024cabb) Jean-Claude ANTONIO *2023-08-31 14:56:34*

**test indexing**


[1b156](https://github.com/Afakto/afakto/commit/1b156ab6699c366) Jean-Claude ANTONIO *2023-08-31 14:55:38*

**test**


[07624](https://github.com/Afakto/afakto/commit/0762422ec61fa8d) Jean-Claude ANTONIO *2023-08-31 09:30:11*

**delete unused file**


[6f821](https://github.com/Afakto/afakto/commit/6f821163374f2a0) Jean-Claude ANTONIO *2023-08-31 08:50:08*

**add logs for indexer**


[65edb](https://github.com/Afakto/afakto/commit/65edb385d7501d2) Jean-Claude ANTONIO *2023-08-31 08:47:07*

**FIx tests**


[32c35](https://github.com/Afakto/afakto/commit/32c3575750ebc7a) Jean-Claude ANTONIO *2023-08-29 10:05:14*

**update documentation**


[35157](https://github.com/Afakto/afakto/commit/3515739e0b05694) Jean-Claude ANTONIO *2023-08-18 13:46:37*

**Update documentation**


[44360](https://github.com/Afakto/afakto/commit/443601160f36db1) Jean-Claude ANTONIO *2023-08-18 13:02:51*

**test**


[6f2ce](https://github.com/Afakto/afakto/commit/6f2ce06cb9443ee) SDaller *2023-08-15 09:34:59*

**Merge pull request #13 from nguyenanhdongmantulab/env-prod**

* Env prod 

[1e937](https://github.com/Afakto/afakto/commit/1e937924777e0bd) Nguyễn Anh Đông *2023-07-14 08:13:50*

**Merge branch 'env-prod' of https://github.com/nguyenanhdongmantulab/afakto into env-prod**


[8712e](https://github.com/Afakto/afakto/commit/8712e1e09861289) root *2023-07-14 08:01:59*

**test**


[687bd](https://github.com/Afakto/afakto/commit/687bdc8f685f760) root *2023-07-14 08:01:23*

**remove test file**


[90f34](https://github.com/Afakto/afakto/commit/90f34f567e4fca6) root *2023-07-13 19:47:45*

**create test file**


[47ee8](https://github.com/Afakto/afakto/commit/47ee869bf8e5a87) root *2023-07-13 19:41:06*

**[env-prod] Merge branch 'main' into env-prod**


[090d3](https://github.com/Afakto/afakto/commit/090d38bb36c843b) Jean-Claude Antonio *2023-07-04 14:43:48*

**Merge pull request #12 from mantucorp/feature/file-uploads**

* External Credit Insurance page 

[80ac6](https://github.com/Afakto/afakto/commit/80ac6c02fe8170b) Jean-Claude Antonio *2023-07-04 14:39:52*

**end point for External Credit Isurance**


[239d5](https://github.com/Afakto/afakto/commit/239d53b2bbdf47a) Sphe-Manuel *2023-07-04 14:33:06*

**External Credit Insurance page**


[9ac3e](https://github.com/Afakto/afakto/commit/9ac3e5c36f57e79) Sphe-Manuel *2023-07-04 14:20:10*

**Merge branch 'main' into env-prod**


[3091f](https://github.com/Afakto/afakto/commit/3091f493806e0a0) Jean-Claude ANTONIO *2023-06-30 12:51:19*

**fix null column**


[c7da1](https://github.com/Afakto/afakto/commit/c7da106a375efb1) Jean-Claude ANTONIO *2023-06-30 12:50:21*

**test**


[20eba](https://github.com/Afakto/afakto/commit/20eba2b446329db) Jean-Claude ANTONIO *2023-06-29 21:37:28*

**fix merge**


[c95e9](https://github.com/Afakto/afakto/commit/c95e92f4b8bf96f) Jean-Claude ANTONIO *2023-06-29 16:09:49*

**Merge branch 'main' into env-prod**


[2432a](https://github.com/Afakto/afakto/commit/2432a47adc3c39d) Jean-Claude ANTONIO *2023-06-29 15:47:51*

**correct typo**


[19811](https://github.com/Afakto/afakto/commit/198111905d7dda1) Jean-Claude ANTONIO *2023-06-29 15:43:10*

**Merge pull request #11 from mantucorp/feature/file-uploads**

* Feature/file uploads 

[7b883](https://github.com/Afakto/afakto/commit/7b88341e0013d61) Jean-Claude Antonio *2023-06-29 14:32:24*

**Update InvoiceServiceImpl.java**


[c990b](https://github.com/Afakto/afakto/commit/c990be2db5b025b) Sphesihle Madonsela *2023-06-28 17:11:02*

**Update ClientServiceImpl.java**


[e6f56](https://github.com/Afakto/afakto/commit/e6f5605cf959bad) Sphesihle Madonsela *2023-06-28 17:09:17*

**Update CompanyService.java**


[64a5b](https://github.com/Afakto/afakto/commit/64a5b19edba20ff) Sphesihle Madonsela *2023-06-28 17:03:41*

**Update PaymentMethod.java**


[a6256](https://github.com/Afakto/afakto/commit/a6256fea2f53a5c) Sphesihle Madonsela *2023-06-28 17:00:07*

**Update CompanyEdit.vue**


[7da60](https://github.com/Afakto/afakto/commit/7da60dc29641ada) Sphesihle Madonsela *2023-06-28 16:58:17*

**Layered Arch fixed**


[06a2e](https://github.com/Afakto/afakto/commit/06a2e79cef73888) Sphe-Manuel *2023-06-28 14:45:26*

**Resolved conflict during merge**


[208f9](https://github.com/Afakto/afakto/commit/208f90a1c031557) Sphe-Manuel *2023-06-28 14:34:31*

**Resolved conflict during merge**


[ffd0a](https://github.com/Afakto/afakto/commit/ffd0a78f285db18) Sphe-Manuel *2023-06-28 14:29:32*

**Resolved conflict during merge**


[a6648](https://github.com/Afakto/afakto/commit/a664834c10f69e9) Sphe-Manuel *2023-06-28 14:11:48*

**Resolved conflict during merge**


[2e19e](https://github.com/Afakto/afakto/commit/2e19ee537549cfd) Sphe-Manuel *2023-06-28 14:03:39*

**CreditPolicy Fake elastic search**


[f5b9f](https://github.com/Afakto/afakto/commit/f5b9f643c8531a3) Sphe-Manuel *2023-06-28 11:37:53*

**CreditInsuranceResourceTest**


[06667](https://github.com/Afakto/afakto/commit/0666755139bb829) Sphe-Manuel *2023-06-28 09:23:47*

**renamed the credit_insurance_name**


[24436](https://github.com/Afakto/afakto/commit/244364d4a8fe827) Sphe-Manuel *2023-06-27 11:04:25*

**Client response pulled**


[9559b](https://github.com/Afakto/afakto/commit/9559bad115f635f) Sphe-Manuel *2023-06-26 18:23:47*

**Invoice refactor 2**


[ac40f](https://github.com/Afakto/afakto/commit/ac40f4413c0b99a) Sphe-Manuel *2023-06-26 17:18:30*

**Invoice refactor**


[b7086](https://github.com/Afakto/afakto/commit/b708684cc078621) Sphe-Manuel *2023-06-26 17:16:40*

**Fixed the tests, more to come**


[a8dcb](https://github.com/Afakto/afakto/commit/a8dcb77f31c28fa) Sphe-Manuel *2023-06-26 16:42:16*

**Handled the case of mssing cols or empty**


[2e29e](https://github.com/Afakto/afakto/commit/2e29e92c2daeee3) Sphe-Manuel *2023-06-26 14:43:42*

**Refacotor and client upload, testing to follow**


[63250](https://github.com/Afakto/afakto/commit/63250e120b57841) Sphe-Manuel *2023-06-26 13:09:29*

**REmoved elastic search**


[26a13](https://github.com/Afakto/afakto/commit/26a13f37eee18e6) Sphe-Manuel *2023-06-23 18:16:08*

**ResponeUpload class**


[4871f](https://github.com/Afakto/afakto/commit/4871fd9d7ae94e3) Sphe-Manuel *2023-06-23 10:15:50*

**UploadREsponseDTO**


[c8da5](https://github.com/Afakto/afakto/commit/c8da5c16991a261) Sphe-Manuel *2023-06-23 09:42:55*

**Merge branch 'feature/file-uploads' of github.com:mantucorp/afakto into feature/file-uploads**


[77de0](https://github.com/Afakto/afakto/commit/77de0ecc8f78011) Sphe-Manuel *2023-06-23 09:32:56*

**Credit Insurance Policy, front-end**


[7d16d](https://github.com/Afakto/afakto/commit/7d16d793450697c) Sphe-Manuel *2023-06-22 05:01:15*

**client uploads almost done**


[9ab7b](https://github.com/Afakto/afakto/commit/9ab7b63cc8b1767) Sphe-Manuel *2023-06-21 18:02:01*

**client_comapny table crated**


[c60a5](https://github.com/Afakto/afakto/commit/c60a5ce2160880f) Sphe-Manuel *2023-06-21 08:47:08*

**Error fixed in the mappedBy**


[2f10a](https://github.com/Afakto/afakto/commit/2f10a78b7cb9f63) Sphe-Manuel *2023-06-19 07:30:36*

**Almost there**


[c2e9d](https://github.com/Afakto/afakto/commit/c2e9d679271f891) Sphe-Manuel *2023-06-15 13:29:55*

**New Entities**


[264e1](https://github.com/Afakto/afakto/commit/264e1aa21064dc2) Sphe-Manuel *2023-06-15 10:13:29*

**credit insurance policy**


[ea54d](https://github.com/Afakto/afakto/commit/ea54daaf594b138) Sphe-Manuel *2023-06-14 11:29:54*

**CreditInsurancePolict Entity**


[416d5](https://github.com/Afakto/afakto/commit/416d54bfea810d3) Sphe-Manuel *2023-06-14 10:12:54*

**fix elasticsearch dependency in test**


[d87f6](https://github.com/Afakto/afakto/commit/d87f6499361685a) Jean-Claude ANTONIO *2023-06-14 10:01:09*

**Credit Insurance**


[9d599](https://github.com/Afakto/afakto/commit/9d599739325a905) Sphe-Manuel *2023-06-14 07:44:06*

**Added 2 changesets**


[54926](https://github.com/Afakto/afakto/commit/54926760555520c) Sphe-Manuel *2023-06-13 14:14:20*

**Fixed layered Arch**


[fb222](https://github.com/Afakto/afakto/commit/fb22226c29d7dc5) Sphe-Manuel *2023-06-12 20:11:15*

**Merge branch 'main' into feature/file-uploads**


[2c309](https://github.com/Afakto/afakto/commit/2c30927be8408a0) Sphe-Manuel *2023-06-12 14:20:25*

**clients**


[73e93](https://github.com/Afakto/afakto/commit/73e933fb9a53c16) Sphe-Manuel *2023-06-12 14:19:03*

**test env in build**


[5a67e](https://github.com/Afakto/afakto/commit/5a67e9811164753) Jean-Claude ANTONIO *2023-06-12 14:05:25*

**test env in build**


[25f67](https://github.com/Afakto/afakto/commit/25f679b30dcae35) Jean-Claude ANTONIO *2023-06-12 14:05:25*

**Clinetes**


[41859](https://github.com/Afakto/afakto/commit/4185962698fec91) Sphe-Manuel *2023-06-12 13:52:04*

**Merge branch 'feature/file-uploads' of github.com:mantucorp/afakto into feature/file-uploads**


[f1b87](https://github.com/Afakto/afakto/commit/f1b874d780ca6d0) Sphe-Manuel *2023-06-12 12:48:39*

**LegalEntityTest Fixed**


[ea708](https://github.com/Afakto/afakto/commit/ea7083feb5fcd9c) Sphe-Manuel *2023-06-12 12:40:09*

**Merge branch 'main' into feature/file-uploads**


[22dc4](https://github.com/Afakto/afakto/commit/22dc4e5ca019296) Sphesihle Madonsela *2023-06-12 06:28:54*

**Client works for all clients with different id types**


[a2bf5](https://github.com/Afakto/afakto/commit/a2bf518f1c3e530) Sphe-Manuel *2023-06-11 18:13:29*

**LegalEntity Controller needs to be fixed**


[75fca](https://github.com/Afakto/afakto/commit/75fca5dd9969165) Sphe-Manuel *2023-06-11 16:33:44*

**Refactored , now need to test**


[437f4](https://github.com/Afakto/afakto/commit/437f497af0eff25) Sphe-Manuel *2023-06-11 09:44:57*

**Better code**


[5c22c](https://github.com/Afakto/afakto/commit/5c22cf74698c74d) Sphe-Manuel *2023-06-10 12:01:17*

**refactor 1**


[f8bd8](https://github.com/Afakto/afakto/commit/f8bd82074872276) Sphe-Manuel *2023-06-10 10:03:01*

**simplify**


[895bf](https://github.com/Afakto/afakto/commit/895bf669a865fc5) Jean-Claude ANTONIO *2023-06-09 14:12:29*

**simplify**


[0877c](https://github.com/Afakto/afakto/commit/0877c5d16b7f40d) Jean-Claude ANTONIO *2023-06-09 14:12:29*

**test**


[f5b7d](https://github.com/Afakto/afakto/commit/f5b7dbf89b6319d) Jean-Claude ANTONIO *2023-06-09 13:54:02*

**test**


[902a8](https://github.com/Afakto/afakto/commit/902a8b3214e1713) Jean-Claude ANTONIO *2023-06-09 13:54:02*

**test**


[bcbb8](https://github.com/Afakto/afakto/commit/bcbb89ec50ce93e) Jean-Claude ANTONIO *2023-06-09 13:50:18*

**test**


[0dad2](https://github.com/Afakto/afakto/commit/0dad2e2c4bb0873) Jean-Claude ANTONIO *2023-06-09 13:50:18*

**test**


[d8dd0](https://github.com/Afakto/afakto/commit/d8dd079c97222c0) Jean-Claude ANTONIO *2023-06-09 13:41:35*

**test**


[c27d1](https://github.com/Afakto/afakto/commit/c27d1f5eb64c4a3) Jean-Claude ANTONIO *2023-06-09 13:41:35*

**test**


[6e824](https://github.com/Afakto/afakto/commit/6e824bddf709f34) Jean-Claude ANTONIO *2023-06-09 13:24:31*

**test**


[6414c](https://github.com/Afakto/afakto/commit/6414cb11b72a62d) Jean-Claude ANTONIO *2023-06-09 13:24:31*

**add test in pipeline**


[bd9db](https://github.com/Afakto/afakto/commit/bd9dbaca2d52a02) Jean-Claude ANTONIO *2023-06-09 13:00:28*

**add test in pipeline**


[20218](https://github.com/Afakto/afakto/commit/20218ab4c75501e) Jean-Claude ANTONIO *2023-06-09 13:00:28*

**test**


[ca90b](https://github.com/Afakto/afakto/commit/ca90b84430d916d) Jean-Claude ANTONIO *2023-06-09 12:45:00*

**test**


[427c3](https://github.com/Afakto/afakto/commit/427c3736ceca1d9) Jean-Claude ANTONIO *2023-06-09 12:37:24*

**test**


[a3ed6](https://github.com/Afakto/afakto/commit/a3ed6ee150218a2) Jean-Claude ANTONIO *2023-06-09 12:25:32*

**test**


[84fc9](https://github.com/Afakto/afakto/commit/84fc9db794a91dc) Jean-Claude ANTONIO *2023-06-09 12:17:15*

**Merge branch 'main' into env-prod**


[2ef75](https://github.com/Afakto/afakto/commit/2ef758bb802c762) Jean-Claude ANTONIO *2023-06-09 12:01:30*

**Merge pull request #9 from mantucorp/feature/file-uploads**

* Feature/file uploads 

[36ef0](https://github.com/Afakto/afakto/commit/36ef0abf00f9ad6) mantucorp *2023-06-09 11:57:14*

**Merge pull request #9 from mantucorp/feature/file-uploads**

* Feature/file uploads 

[09b1f](https://github.com/Afakto/afakto/commit/09b1f05e4dd3870) mantucorp *2023-06-09 11:57:14*

**For clinets in insee api**


[5507c](https://github.com/Afakto/afakto/commit/5507c290168881e) Sphe-Manuel *2023-06-09 11:44:56*

**For clinets in insee api**


[43277](https://github.com/Afakto/afakto/commit/43277104d3755d6) Sphe-Manuel *2023-06-09 11:44:56*

**client updated**


[d13d4](https://github.com/Afakto/afakto/commit/d13d4dedd203f7d) Sphe-Manuel *2023-06-09 11:42:35*

**client updated**


[156af](https://github.com/Afakto/afakto/commit/156afd0db6641bc) Sphe-Manuel *2023-06-09 11:42:35*

**Saveall updated**


[f9162](https://github.com/Afakto/afakto/commit/f9162f7e10e1ac1) Sphe-Manuel *2023-06-09 10:27:20*

**Saveall updated**


[9305c](https://github.com/Afakto/afakto/commit/9305cb7ec333986) Sphe-Manuel *2023-06-09 10:27:20*

**country auto filled no to 'France'**


[9f25b](https://github.com/Afakto/afakto/commit/9f25b85f20dc87d) Sphe-Manuel *2023-06-09 04:03:01*

**country auto filled no to 'France'**


[4f208](https://github.com/Afakto/afakto/commit/4f2083e1f00838e) Sphe-Manuel *2023-06-09 04:03:01*

**added search icons**


[eebd0](https://github.com/Afakto/afakto/commit/eebd08f4de9656f) Sphe-Manuel *2023-06-09 03:33:51*

**added search icons**


[09b68](https://github.com/Afakto/afakto/commit/09b68f5cbf291e9) Sphe-Manuel *2023-06-09 03:33:51*

**invoices saveAll issue when called**


[ec7d4](https://github.com/Afakto/afakto/commit/ec7d466e6b233b0) Sphe-Manuel *2023-06-09 02:30:18*

**invoices saveAll issue when called**


[52a16](https://github.com/Afakto/afakto/commit/52a1686b4d8bc5d) Sphe-Manuel *2023-06-09 02:30:18*

**chageset for invoices added**


[61c71](https://github.com/Afakto/afakto/commit/61c71e71f704a8d) Sphe-Manuel *2023-06-08 14:47:16*

**chageset for invoices added**


[2fbbe](https://github.com/Afakto/afakto/commit/2fbbedc2797bee9) Sphe-Manuel *2023-06-08 14:47:16*

**if client found exists perform an update**


[7700c](https://github.com/Afakto/afakto/commit/7700c9ec111ca4e) Sphe-Manuel *2023-06-08 13:15:54*

**if client found exists perform an update**


[75465](https://github.com/Afakto/afakto/commit/7546586b063ab23) Sphe-Manuel *2023-06-08 13:15:54*

**Can upload client from csv file working**


[42280](https://github.com/Afakto/afakto/commit/4228050e855fad6) Sphe-Manuel *2023-06-08 10:23:15*

**Can upload client from csv file working**


[13b0f](https://github.com/Afakto/afakto/commit/13b0f0eaf00a106) Sphe-Manuel *2023-06-08 10:23:15*

**Front-end upload almost there**


[dcb59](https://github.com/Afakto/afakto/commit/dcb599b07350259) Sphe-Manuel *2023-06-07 10:11:09*

**Front-end upload almost there**


[0cfeb](https://github.com/Afakto/afakto/commit/0cfeb81f8e04d37) Sphe-Manuel *2023-06-07 10:11:09*

**invoice upload page**


[f8f89](https://github.com/Afakto/afakto/commit/f8f89286170b46f) Sphe-Manuel *2023-06-07 07:24:43*

**invoice upload page**


[8af97](https://github.com/Afakto/afakto/commit/8af977f2678221a) Sphe-Manuel *2023-06-07 07:24:43*

**add missing insee attributes**


[3c93e](https://github.com/Afakto/afakto/commit/3c93ebd2bff7398) Jean-Claude ANTONIO *2023-06-05 15:52:29*

**rm default**


[6c000](https://github.com/Afakto/afakto/commit/6c000f8fc189f9c) Jean-Claude ANTONIO *2023-06-05 15:40:58*

**remove CI from branch**


[113f0](https://github.com/Afakto/afakto/commit/113f01a8ffbb38d) Jean-Claude ANTONIO *2023-06-05 15:25:52*

**Merge branch 'bugfix/stop-deleting-secrets' into env-prod**


[3b8e4](https://github.com/Afakto/afakto/commit/3b8e4873053adf1) Jean-Claude ANTONIO *2023-06-05 15:24:56*

**fix build due to structure**


[e8b3a](https://github.com/Afakto/afakto/commit/e8b3ac263584e43) Jean-Claude ANTONIO *2023-06-05 15:10:38*

**test keep secret 4**


[5e293](https://github.com/Afakto/afakto/commit/5e293ac48f32c14) Jean-Claude ANTONIO *2023-06-05 14:59:42*

**test keep secret 3**


[def2c](https://github.com/Afakto/afakto/commit/def2cec8e5111f2) Jean-Claude ANTONIO *2023-06-05 14:24:27*

**test keep secret 2**


[e88f6](https://github.com/Afakto/afakto/commit/e88f6d906aa2c22) Jean-Claude ANTONIO *2023-06-05 14:03:52*

**rv unused run**


[f81b4](https://github.com/Afakto/afakto/commit/f81b42cf08d5131) Jean-Claude ANTONIO *2023-06-05 13:58:50*

**test keep secret 1**


[db3ff](https://github.com/Afakto/afakto/commit/db3ffaa96e2d90d) Jean-Claude ANTONIO *2023-06-05 13:54:59*

**paymenntsDTO build, other clientDTO bulid can be ommitted**


[fb5a3](https://github.com/Afakto/afakto/commit/fb5a3eab82c88da) Sphe-Manuel *2023-06-05 08:23:03*

**paymenntsDTO build, other clientDTO bulid can be ommitted**


[ecffb](https://github.com/Afakto/afakto/commit/ecffb7a80798940) Sphe-Manuel *2023-06-05 08:23:03*

**InvoiceDTO and ClientDTO partial builts**


[e0a31](https://github.com/Afakto/afakto/commit/e0a31b56eba7063) Sphe-Manuel *2023-06-05 08:07:29*

**InvoiceDTO and ClientDTO partial builts**


[afbed](https://github.com/Afakto/afakto/commit/afbed27f8039af0) Sphe-Manuel *2023-06-05 08:07:29*

**Refactored the code**


[f1f93](https://github.com/Afakto/afakto/commit/f1f9343e66330c7) Sphe-Manuel *2023-06-02 08:59:22*

**Refactored the code**


[aa7ad](https://github.com/Afakto/afakto/commit/aa7adfff2043e4d) Sphe-Manuel *2023-06-02 08:59:22*

**saveAll - clients**


[91e24](https://github.com/Afakto/afakto/commit/91e24d832f920b3) Sphe-Manuel *2023-06-01 14:55:11*

**saveAll - clients**


[7bc7c](https://github.com/Afakto/afakto/commit/7bc7c5ebe725236) Sphe-Manuel *2023-06-01 14:55:11*

**Merge branch 'main' into env-prod**


[1c469](https://github.com/Afakto/afakto/commit/1c469fc3d632c5a) Jean-Claude ANTONIO *2023-06-01 13:36:47*

**Merge pull request #8 from mantucorp/feature/insee_connectivity**

* LegalEntityController Tests 

[7b453](https://github.com/Afakto/afakto/commit/7b453208f01b8e7) mantucorp *2023-06-01 13:34:19*

**Merge pull request #8 from mantucorp/feature/insee_connectivity**

* LegalEntityController Tests 

[13b54](https://github.com/Afakto/afakto/commit/13b5443e7c618a9) mantucorp *2023-06-01 13:34:19*

**invoice saveAll for multiple invoices**


[484bb](https://github.com/Afakto/afakto/commit/484bbfee24ebb7b) Sphe-Manuel *2023-06-01 12:54:53*

**invoice saveAll for multiple invoices**


[0efd0](https://github.com/Afakto/afakto/commit/0efd08c6304515f) Sphe-Manuel *2023-06-01 12:54:53*

**test**


[f2435](https://github.com/Afakto/afakto/commit/f243519edfc1518) Jean-Claude ANTONIO *2023-06-01 10:34:11*

**File Upload Service**


[d3c4e](https://github.com/Afakto/afakto/commit/d3c4ed5ca25cd4e) Sphe-Manuel *2023-06-01 07:34:04*

**File Upload Service**


[7c66d](https://github.com/Afakto/afakto/commit/7c66dc39172417f) Sphe-Manuel *2023-06-01 07:34:04*

**test**


[8ae7b](https://github.com/Afakto/afakto/commit/8ae7b34faf6c7a3) Jean-Claude ANTONIO *2023-05-31 16:13:55*

**Controller Tests**


[cd161](https://github.com/Afakto/afakto/commit/cd161276919c339) Sphe-Manuel *2023-05-31 13:00:12*

**Controller Tests**


[7fe44](https://github.com/Afakto/afakto/commit/7fe441b63f9c15c) Sphe-Manuel *2023-05-31 13:00:12*

**Merge branch 'main' into env-prod**


[cc735](https://github.com/Afakto/afakto/commit/cc7355f1630165b) Jean-Claude ANTONIO *2023-05-31 11:50:26*

**Merge pull request #7 from mantucorp/feature/insee_connectivity**

* contract_country changelog 

[0b45d](https://github.com/Afakto/afakto/commit/0b45d1edc799c04) mantucorp *2023-05-31 11:48:37*

**Merge pull request #7 from mantucorp/feature/insee_connectivity**

* contract_country changelog 

[00f9b](https://github.com/Afakto/afakto/commit/00f9b8f0dfe8e10) mantucorp *2023-05-31 11:48:37*

**contract_country changelog**


[e1a2f](https://github.com/Afakto/afakto/commit/e1a2fe8ac652cae) Sphe-Manuel *2023-05-31 08:09:47*

**contract_country changelog**


[ba86d](https://github.com/Afakto/afakto/commit/ba86d610870f081) Sphe-Manuel *2023-05-31 08:09:47*

**fix architecture rule**


[760a3](https://github.com/Afakto/afakto/commit/760a30150b2fc04) Jean-Claude ANTONIO *2023-05-30 14:38:49*

**Merge pull request #6 from mantucorp/feature/insee_connectivity**

* Insee-Connectivity 

[76daf](https://github.com/Afakto/afakto/commit/76daf7c8ee1209a) mantucorp *2023-05-30 14:15:06*

**Merge pull request #6 from mantucorp/feature/insee_connectivity**

* Insee-Connectivity 

[1b63b](https://github.com/Afakto/afakto/commit/1b63b07589944d2) mantucorp *2023-05-30 14:15:06*

**Update application.yml**


[77341](https://github.com/Afakto/afakto/commit/773410d46af04fd) Sphesihle Madonsela *2023-05-30 13:25:27*

**Update application.yml**


[179cc](https://github.com/Afakto/afakto/commit/179cc6f09f2f6d9) Sphesihle Madonsela *2023-05-30 13:25:27*

**Update application.yml**


[fd935](https://github.com/Afakto/afakto/commit/fd935ac8dd11620) Sphesihle Madonsela *2023-05-30 13:24:22*

**Update application.yml**


[ae4b5](https://github.com/Afakto/afakto/commit/ae4b53e5efb003b) Sphesihle Madonsela *2023-05-30 13:24:22*

**Update address.model.ts**


[dcf40](https://github.com/Afakto/afakto/commit/dcf404ec0194248) Sphesihle Madonsela *2023-05-30 09:46:12*

**Update address.model.ts**


[76cf7](https://github.com/Afakto/afakto/commit/76cf70ff345c6b5) Sphesihle Madonsela *2023-05-30 09:46:12*

**Update CountryEdit.vue**


[5c660](https://github.com/Afakto/afakto/commit/5c660f11fa5bffd) Sphesihle Madonsela *2023-05-30 09:30:48*

**Update CountryEdit.vue**


[49b0b](https://github.com/Afakto/afakto/commit/49b0b137a7bdb3c) Sphesihle Madonsela *2023-05-30 09:30:48*

**Update contract.service.ts**


[9f758](https://github.com/Afakto/afakto/commit/9f7584c5aa3db99) Sphesihle Madonsela *2023-05-30 09:27:42*

**Update contract.service.ts**


[838ed](https://github.com/Afakto/afakto/commit/838ed08f29b82f9) Sphesihle Madonsela *2023-05-30 09:27:42*

**Update ClientEdit.vue**


[66faa](https://github.com/Afakto/afakto/commit/66faa870c3149bb) Sphesihle Madonsela *2023-05-30 09:22:21*

**Update ClientEdit.vue**


[1c122](https://github.com/Afakto/afakto/commit/1c122582fc6826d) Sphesihle Madonsela *2023-05-30 09:22:21*

**Update ClientEdit.vue**

* Labels to Code 

[b4691](https://github.com/Afakto/afakto/commit/b46918aaba33792) Sphesihle Madonsela *2023-05-30 09:18:52*

**Update ClientEdit.vue**

* Labels to Code 

[4be31](https://github.com/Afakto/afakto/commit/4be3142cdf54809) Sphesihle Madonsela *2023-05-30 09:18:52*

**Minor changes on the front-end client**


[bcab2](https://github.com/Afakto/afakto/commit/bcab24e4a349311) Sphe-Manuel *2023-05-30 07:12:10*

**Minor changes on the front-end client**


[b24b7](https://github.com/Afakto/afakto/commit/b24b7e963a95d24) Sphe-Manuel *2023-05-30 07:12:10*

**package.json fixed, updated the client to call when SIREN is selected as ID**


[e0096](https://github.com/Afakto/afakto/commit/e009658319ae8f6) Sphe-Manuel *2023-05-29 13:57:19*

**package.json fixed, updated the client to call when SIREN is selected as ID**


[47d98](https://github.com/Afakto/afakto/commit/47d9800feb23982) Sphe-Manuel *2023-05-29 13:57:19*

**Pinia Installed**


[d1537](https://github.com/Afakto/afakto/commit/d1537d850aed554) Sphe-Manuel *2023-05-29 07:38:28*

**Pinia Installed**


[1882f](https://github.com/Afakto/afakto/commit/1882fea889f1c9f) Sphe-Manuel *2023-05-29 07:38:28*

**Merge branch 'feature/insee_connectivity' of github.com:mantucorp/afakto into feature/insee_connectivity**


[94596](https://github.com/Afakto/afakto/commit/945963bbe3724d3) Sphe-Manuel *2023-05-26 07:07:22*

**Merge branch 'feature/insee_connectivity' of github.com:mantucorp/afakto into feature/insee_connectivity**


[7d427](https://github.com/Afakto/afakto/commit/7d4270a3517732b) Sphe-Manuel *2023-05-26 07:07:22*

**Merge branch 'main' into feature/insee_connectivity**


[db60e](https://github.com/Afakto/afakto/commit/db60e0aa3b8a232) Sphesihle Madonsela *2023-05-26 06:25:00*

**Merge branch 'main' into feature/insee_connectivity**


[acef5](https://github.com/Afakto/afakto/commit/acef5f531640110) Sphesihle Madonsela *2023-05-26 06:25:00*

**Remove the country replacement**


[441d3](https://github.com/Afakto/afakto/commit/441d3794c75f54f) Sphe-Manuel *2023-05-25 08:02:46*

**Remove the country replacement**


[3b8f0](https://github.com/Afakto/afakto/commit/3b8f09a1fbc5b36) Sphe-Manuel *2023-05-25 08:02:46*

**Minor changes**


[2ac27](https://github.com/Afakto/afakto/commit/2ac272d72dc2277) Sphe-Manuel *2023-05-25 07:35:33*

**Minor changes**


[01dd0](https://github.com/Afakto/afakto/commit/01dd01d2d4f8ca6) Sphe-Manuel *2023-05-25 07:35:33*

**Merge pull request #4 from mantucorp/bugfix/fix-common-automation-test-errors**

* fix common main tests 

[c4ca3](https://github.com/Afakto/afakto/commit/c4ca38d354d4f8a) mantucorp *2023-05-24 15:28:04*

**Merge pull request #4 from mantucorp/bugfix/fix-common-automation-test-errors**

* fix common main tests 

[2a626](https://github.com/Afakto/afakto/commit/2a62685c2063ca9) mantucorp *2023-05-24 15:28:04*

**fix common main tests**


[96aa4](https://github.com/Afakto/afakto/commit/96aa430c2ff555c) Jean-Claude ANTONIO *2023-05-24 15:01:59*

**fix common main tests**


[7735c](https://github.com/Afakto/afakto/commit/7735c0732dd7bcc) Jean-Claude ANTONIO *2023-05-24 15:01:59*

**Added 2 changelogs for str number and name**


[fd67e](https://github.com/Afakto/afakto/commit/fd67e681bca1af1) Sphe-Manuel *2023-05-24 10:38:53*

**Added 2 changelogs for str number and name**


[5df6e](https://github.com/Afakto/afakto/commit/5df6e353c5b94d6) Sphe-Manuel *2023-05-24 10:38:53*

**ContractComp completed**


[b4106](https://github.com/Afakto/afakto/commit/b4106170c3c7a1e) Sphe-Manuel *2023-05-24 09:46:41*

**ContractComp completed**


[59a5c](https://github.com/Afakto/afakto/commit/59a5c2222a26caf) Sphe-Manuel *2023-05-24 09:46:41*

**Can filter with alpha3Code now**


[d93fb](https://github.com/Afakto/afakto/commit/d93fb02d435415d) Sphe-Manuel *2023-05-24 07:15:17*

**Can filter with alpha3Code now**


[141b9](https://github.com/Afakto/afakto/commit/141b945cff604f9) Sphe-Manuel *2023-05-24 07:15:17*

**contract back-end almost done**


[2df97](https://github.com/Afakto/afakto/commit/2df9794170d0024) Sphe-Manuel *2023-05-23 10:47:41*

**contract back-end almost done**


[0b8e4](https://github.com/Afakto/afakto/commit/0b8e440114131b4) Sphe-Manuel *2023-05-23 10:47:41*

**countries imported from a csv**


[b12e5](https://github.com/Afakto/afakto/commit/b12e561d1c9148c) Sphe-Manuel *2023-05-23 06:18:30*

**countries imported from a csv**


[3b48f](https://github.com/Afakto/afakto/commit/3b48f2933a39ce9) Sphe-Manuel *2023-05-23 06:18:30*

**Make the columns unique, and made sure the delete country button works**


[4abe4](https://github.com/Afakto/afakto/commit/4abe4134a37a87f) Sphe-Manuel *2023-05-18 07:03:53*

**Make the columns unique, and made sure the delete country button works**


[454d0](https://github.com/Afakto/afakto/commit/454d05ece5d2553) Sphe-Manuel *2023-05-18 07:03:53*

**Schema changed**


[ad3c2](https://github.com/Afakto/afakto/commit/ad3c203d44e623f) Sphe-Manuel *2023-05-18 06:38:28*

**Schema changed**


[6c315](https://github.com/Afakto/afakto/commit/6c31554cc8b50e4) Sphe-Manuel *2023-05-18 06:38:28*

**If country is null fill with {FR, France}**


[64a3c](https://github.com/Afakto/afakto/commit/64a3c2f958f91b5) Sphe-Manuel *2023-05-17 10:21:23*

**If country is null fill with {FR, France}**


[5893d](https://github.com/Afakto/afakto/commit/5893d6325077b5b) Sphe-Manuel *2023-05-17 10:21:23*

**Caching the results in the LegalEntityController, updated the CacheConfiguration File**


[89da3](https://github.com/Afakto/afakto/commit/89da33bc786c82b) Sphe-Manuel *2023-05-17 09:20:57*

**Caching the results in the LegalEntityController, updated the CacheConfiguration File**


[4dc5b](https://github.com/Afakto/afakto/commit/4dc5bf02349efc0) Sphe-Manuel *2023-05-17 09:20:57*

**Front-end almost done,  back-end needs testing**


[256bc](https://github.com/Afakto/afakto/commit/256bc857a7aabd9) Sphe-Manuel *2023-05-17 06:59:01*

**Front-end almost done,  back-end needs testing**


[0deb3](https://github.com/Afakto/afakto/commit/0deb385a2231767) Sphe-Manuel *2023-05-17 06:59:01*

**Can use some of the available DTO for better response**


[7b830](https://github.com/Afakto/afakto/commit/7b83040b5679665) Sphe-Manuel *2023-05-16 10:24:14*

**Can use some of the available DTO for better response**


[743fa](https://github.com/Afakto/afakto/commit/743fa527a61147f) Sphe-Manuel *2023-05-16 10:24:14*

**connected to the front-end app, we need a to use a AddressDTO for our Response**


[63bc4](https://github.com/Afakto/afakto/commit/63bc464ca3ed30e) Sphe-Manuel *2023-05-16 10:17:37*

**connected to the front-end app, we need a to use a AddressDTO for our Response**


[4d41f](https://github.com/Afakto/afakto/commit/4d41f533d68be7c) Sphe-Manuel *2023-05-16 10:17:37*

**Exception handling, and DTO**


[d907e](https://github.com/Afakto/afakto/commit/d907e1ccaa8e77a) Sphe-Manuel *2023-05-15 10:04:37*

**Exception handling, and DTO**


[25e33](https://github.com/Afakto/afakto/commit/25e3310bf0e2aca) Sphe-Manuel *2023-05-15 10:04:37*

**Documentation added**


[b97d0](https://github.com/Afakto/afakto/commit/b97d01b5fc41439) Sphe-Manuel *2023-05-15 06:33:24*

**Documentation added**


[243e5](https://github.com/Afakto/afakto/commit/243e5109fe3952b) Sphe-Manuel *2023-05-15 06:33:24*

**Class Diagram**


[b6773](https://github.com/Afakto/afakto/commit/b677378864f31e9) Sphe-Manuel *2023-05-12 09:03:07*

**Class Diagram**


[43e56](https://github.com/Afakto/afakto/commit/43e56ef353141c1) Sphe-Manuel *2023-05-12 09:03:07*

**FeignClinet and controller setup**


[dfd00](https://github.com/Afakto/afakto/commit/dfd00e67d536f94) Sphe-Manuel *2023-05-12 07:27:33*

**FeignClinet and controller setup**


[9ccfb](https://github.com/Afakto/afakto/commit/9ccfbb2766788d1) Sphe-Manuel *2023-05-12 07:27:33*

**the Feign clinet cofigns**


[d0c01](https://github.com/Afakto/afakto/commit/d0c0185ef5c16b6) Sphe-Manuel *2023-05-11 07:41:32*

**the Feign clinet cofigns**


[14cca](https://github.com/Afakto/afakto/commit/14cca16c878362e) Sphe-Manuel *2023-05-11 07:41:32*

**update readme**


[458f4](https://github.com/Afakto/afakto/commit/458f4b49564b054) Jean-Claude ANTONIO *2023-04-20 14:42:03*

**update readme**


[3b6d2](https://github.com/Afakto/afakto/commit/3b6d2761d01233e) Jean-Claude ANTONIO *2023-04-20 14:42:03*

**Update documentation**


[f93aa](https://github.com/Afakto/afakto/commit/f93aa7c66137ce5) Jean-Claude ANTONIO *2023-04-19 16:04:17*

**Update documentation**


[40b0b](https://github.com/Afakto/afakto/commit/40b0bde012a10cd) Jean-Claude ANTONIO *2023-04-19 16:04:17*

**Remove login constraint causing error**


[25a4d](https://github.com/Afakto/afakto/commit/25a4d5f630ab6fb) Jean-Claude ANTONIO *2023-04-19 14:20:45*

**Remove login constraint causing error**


[0d371](https://github.com/Afakto/afakto/commit/0d3717e80baba98) Jean-Claude ANTONIO *2023-04-19 14:20:45*

**Refactor**


[f7f4f](https://github.com/Afakto/afakto/commit/f7f4fbbee99d427) Jean-Claude Antonio *2023-04-07 13:16:30*

**Refactor**


[2a5da](https://github.com/Afakto/afakto/commit/2a5daa40cc1fb92) Jean-Claude Antonio *2023-04-07 13:16:30*

**Fix test file in prod**


[e7349](https://github.com/Afakto/afakto/commit/e73491fdac1ddcb) Jean-Claude Antonio *2022-12-19 21:49:51*

**Fix test file in prod**


[120d6](https://github.com/Afakto/afakto/commit/120d661fcd4e021) Jean-Claude Antonio *2022-12-19 21:49:51*

**add arch exception**


[8aa53](https://github.com/Afakto/afakto/commit/8aa5372c3fab290) Jean-Claude Antonio *2022-12-19 20:13:43*

**add arch exception**


[01e8b](https://github.com/Afakto/afakto/commit/01e8b8862145280) Jean-Claude Antonio *2022-12-19 20:13:43*

**update**


[9b5d5](https://github.com/Afakto/afakto/commit/9b5d54fc08219bb) Jean-Claude Antonio *2022-12-17 20:39:00*

**update**


[9715d](https://github.com/Afakto/afakto/commit/9715d181c73851f) Jean-Claude Antonio *2022-12-17 20:39:00*

**Add Bank Statemetnb Front End**


[81db4](https://github.com/Afakto/afakto/commit/81db4d48368f8bb) Jean-Claude Antonio *2022-12-17 17:22:15*

**Add Bank Statemetnb Front End**


[0eb2a](https://github.com/Afakto/afakto/commit/0eb2a967e8a69dd) Jean-Claude Antonio *2022-12-17 17:22:15*

**update bank transaction service**


[63077](https://github.com/Afakto/afakto/commit/630773d3826ac1f) Jean-Claude Antonio *2022-12-15 19:31:44*

**update bank transaction service**


[566ef](https://github.com/Afakto/afakto/commit/566effe42a5c701) Jean-Claude Antonio *2022-12-15 19:31:44*

**add trx persistence**


[e4307](https://github.com/Afakto/afakto/commit/e430773e7ffce6c) Jean-Claude Antonio *2022-12-14 22:05:26*

**add trx persistence**


[2ccbf](https://github.com/Afakto/afakto/commit/2ccbf2a4c0fc2a4) Jean-Claude Antonio *2022-12-14 22:05:26*

**fix country navigation**


[c6aaf](https://github.com/Afakto/afakto/commit/c6aaf51d8e959d1) Jean-Claude Antonio *2022-12-13 21:22:17*

**fix country navigation**


[19df4](https://github.com/Afakto/afakto/commit/19df497924dcb38) Jean-Claude Antonio *2022-12-13 21:22:17*

**add MT940 Tests**


[f1c53](https://github.com/Afakto/afakto/commit/f1c53b32b54f4bf) Jean-Claude Antonio *2022-12-13 19:27:43*

**add MT940 Tests**


[28e0a](https://github.com/Afakto/afakto/commit/28e0aa656c4f243) Jean-Claude Antonio *2022-12-13 19:27:43*

**improve display**


[f010e](https://github.com/Afakto/afakto/commit/f010e0bbedd63b5) Jean-Claude Antonio *2022-12-04 21:52:21*

**improve display**


[0f5ff](https://github.com/Afakto/afakto/commit/0f5ffa880802093) Jean-Claude Antonio *2022-12-04 21:52:21*

**add titles**


[eb5d3](https://github.com/Afakto/afakto/commit/eb5d3bbb2fa79d3) Jean-Claude Antonio *2022-12-04 21:33:22*

**add titles**


[afebd](https://github.com/Afakto/afakto/commit/afebda422f530f4) Jean-Claude Antonio *2022-12-04 21:33:22*

**rm valid checksum**


[9b264](https://github.com/Afakto/afakto/commit/9b26454fba63e89) Jean-Claude Antonio *2022-12-04 13:03:42*

**rm valid checksum**


[97116](https://github.com/Afakto/afakto/commit/9711670d9359852) Jean-Claude Antonio *2022-12-04 13:03:42*

**empty**


[c264a](https://github.com/Afakto/afakto/commit/c264a6f67057fc0) Jean-Claude Antonio *2022-12-02 21:57:34*

**empty**


[4d008](https://github.com/Afakto/afakto/commit/4d0083858cb63c6) Jean-Claude Antonio *2022-12-02 21:28:53*

**test checksum**


[b407a](https://github.com/Afakto/afakto/commit/b407a0d3813b110) Jean-Claude Antonio *2022-12-02 20:42:22*

**test checksum**


[24fe1](https://github.com/Afakto/afakto/commit/24fe1e8beb11190) Jean-Claude Antonio *2022-12-02 20:42:22*

**change checksum**


[f0a19](https://github.com/Afakto/afakto/commit/f0a19a03370369b) Jean-Claude Antonio *2022-12-02 20:08:34*

**change checksum**


[9a064](https://github.com/Afakto/afakto/commit/9a064ccf9955034) Jean-Claude Antonio *2022-12-02 20:08:34*

**test change vault id**


[b2641](https://github.com/Afakto/afakto/commit/b2641551b5c9eb7) Jean-Claude Antonio *2022-12-02 17:53:41*

**test change vault id**


[80fa6](https://github.com/Afakto/afakto/commit/80fa629422e2bdd) Jean-Claude Antonio *2022-12-02 17:53:41*

**Fix architecture warning**


[f528a](https://github.com/Afakto/afakto/commit/f528a341fb89b53) Jean-Claude Antonio *2022-12-02 17:25:24*

**Fix architecture warning**


[36443](https://github.com/Afakto/afakto/commit/36443ead8b92796) Jean-Claude Antonio *2022-12-02 17:25:24*

**fix dto equals**


[ab32f](https://github.com/Afakto/afakto/commit/ab32fdef16d187b) Jean-Claude Antonio *2022-12-02 15:53:01*

**fix dto equals**


[4c4c3](https://github.com/Afakto/afakto/commit/4c4c3b6b8dea6d0) Jean-Claude Antonio *2022-12-02 15:53:01*

**empty**


[a931d](https://github.com/Afakto/afakto/commit/a931d956540b47f) Jean-Claude Antonio *2022-12-02 14:11:03*

**empty**


[c4542](https://github.com/Afakto/afakto/commit/c4542514675068c) Jean-Claude Antonio *2022-12-02 13:53:22*

**simplify CI/CD**


[7e2c4](https://github.com/Afakto/afakto/commit/7e2c48269cfe614) Jean-Claude Antonio *2022-12-02 13:16:50*

**simplify CI/CD**


[7a610](https://github.com/Afakto/afakto/commit/7a610169e11cec1) Jean-Claude Antonio *2022-12-02 13:16:50*

**set java 17 for github action**


[3255d](https://github.com/Afakto/afakto/commit/3255dd832c04058) Jean-Claude Antonio *2022-12-02 13:08:50*

**set java 17 for github action**


[18bc1](https://github.com/Afakto/afakto/commit/18bc1ac6b402624) Jean-Claude Antonio *2022-12-02 13:08:50*

**Add nullAnalysis**


[f98f3](https://github.com/Afakto/afakto/commit/f98f385600ab541) Jean-Claude Antonio *2022-12-02 12:57:03*

**Add nullAnalysis**


[cd696](https://github.com/Afakto/afakto/commit/cd6964b6c940320) Jean-Claude Antonio *2022-12-02 12:57:03*

**remove generic warning**


[b5c42](https://github.com/Afakto/afakto/commit/b5c4268044c3ff5) Jean-Claude Antonio *2022-12-02 12:56:48*

**remove generic warning**


[0291e](https://github.com/Afakto/afakto/commit/0291e67da83d216) Jean-Claude Antonio *2022-12-02 12:56:48*

**update layout**


[ee2b0](https://github.com/Afakto/afakto/commit/ee2b0f2f0620bf0) Jean-Claude Antonio *2022-11-24 22:32:39*

**update layout**


[7e3d0](https://github.com/Afakto/afakto/commit/7e3d0278e230428) Jean-Claude Antonio *2022-11-24 22:32:39*

**update contract**


[b7b2c](https://github.com/Afakto/afakto/commit/b7b2cd44624cc8c) Jean-Claude Antonio *2022-11-24 21:57:23*

**update contract**


[2bc84](https://github.com/Afakto/afakto/commit/2bc8459fd23eb99) Jean-Claude Antonio *2022-11-24 21:57:23*

**draft contract**


[6e23b](https://github.com/Afakto/afakto/commit/6e23b9a7e8c58cb) Jean-Claude Antonio *2022-11-23 20:20:05*

**draft contract**


[24fcb](https://github.com/Afakto/afakto/commit/24fcb3f818a2c01) Jean-Claude Antonio *2022-11-23 20:20:05*

**fix Ref Data**


[cb69f](https://github.com/Afakto/afakto/commit/cb69f36f2f6a5b4) Jean-Claude Antonio *2022-11-18 17:34:30*

**fix Ref Data**


[c0257](https://github.com/Afakto/afakto/commit/c0257bce1b7d5cd) Jean-Claude Antonio *2022-11-18 17:34:30*

**WIP add Companies & FactorInstitutions**


[b88d7](https://github.com/Afakto/afakto/commit/b88d7240f9a299a) Jean-Claude Antonio *2022-11-13 19:27:42*

**WIP add Companies & FactorInstitutions**


[2c363](https://github.com/Afakto/afakto/commit/2c3638d01b11a20) Jean-Claude Antonio *2022-11-13 19:27:42*

**fix response in reindex**


[57779](https://github.com/Afakto/afakto/commit/57779b8b30f20a2) Jean-Claude Antonio *2022-11-13 09:09:48*

**fix response in reindex**


[05345](https://github.com/Afakto/afakto/commit/05345fe0b56c178) Jean-Claude Antonio *2022-11-13 09:09:48*

**skip fields in audit**


[6266f](https://github.com/Afakto/afakto/commit/6266f21ec3c31c1) Jean-Claude Antonio *2022-11-05 19:28:58*

**skip fields in audit**


[20125](https://github.com/Afakto/afakto/commit/201251108e74f29) Jean-Claude Antonio *2022-11-05 19:28:58*

**Add user in audit**


[bf490](https://github.com/Afakto/afakto/commit/bf4904a6b96d9e7) Jean-Claude Antonio *2022-11-05 16:00:08*

**Add user in audit**


[be140](https://github.com/Afakto/afakto/commit/be14038d9ce543b) Jean-Claude Antonio *2022-11-05 16:00:08*

**improve audit**


[e72d3](https://github.com/Afakto/afakto/commit/e72d3b6a2bf306e) Jean-Claude Antonio *2022-11-04 23:44:49*

**improve audit**


[626f0](https://github.com/Afakto/afakto/commit/626f01807e168f1) Jean-Claude Antonio *2022-11-04 23:44:49*

**audit v1**


[8395e](https://github.com/Afakto/afakto/commit/8395ebd2f8fb9f6) Jean-Claude Antonio *2022-11-04 22:24:49*

**audit v1**


[80785](https://github.com/Afakto/afakto/commit/807859656ea60d2) Jean-Claude Antonio *2022-11-04 22:24:49*

**clean**


[bca39](https://github.com/Afakto/afakto/commit/bca3920ca3c1d1d) Jean-Claude Antonio *2022-11-03 14:06:42*

**clean**


[aad89](https://github.com/Afakto/afakto/commit/aad89989771906f) Jean-Claude Antonio *2022-11-03 14:06:42*

**change model**


[cccd3](https://github.com/Afakto/afakto/commit/cccd3b124280b9b) Jean-Claude Antonio *2022-11-02 17:05:42*

**change model**


[9c847](https://github.com/Afakto/afakto/commit/9c8479d61121e53) Jean-Claude Antonio *2022-11-02 17:05:42*

**Fix paging**


[efd66](https://github.com/Afakto/afakto/commit/efd66549bd44d61) Jean-Claude Antonio *2022-11-02 15:42:03*

**Fix paging**


[35114](https://github.com/Afakto/afakto/commit/3511426f31fd22f) Jean-Claude Antonio *2022-11-02 15:42:03*

**changer UI**


[9b096](https://github.com/Afakto/afakto/commit/9b0960798cb0244) Jean-Claude Antonio *2022-11-02 11:46:11*

**changer UI**


[73cb3](https://github.com/Afakto/afakto/commit/73cb3e07be887c9) Jean-Claude Antonio *2022-11-02 11:46:11*

**chane layout**


[ffb5e](https://github.com/Afakto/afakto/commit/ffb5e726140808f) Jean-Claude Antonio *2022-11-02 10:09:25*

**chane layout**


[1f98c](https://github.com/Afakto/afakto/commit/1f98c8a13a43358) Jean-Claude Antonio *2022-11-02 10:09:25*

**add model**


[7a2ad](https://github.com/Afakto/afakto/commit/7a2ade72d8c1157) Jean-Claude Antonio *2022-11-02 09:58:05*

**add model**


[0ec12](https://github.com/Afakto/afakto/commit/0ec12c570c60165) Jean-Claude Antonio *2022-11-02 09:58:05*

**fix**


[8c86c](https://github.com/Afakto/afakto/commit/8c86cc08613974c) Jean-Claude Antonio *2022-11-01 18:02:46*

**fix**


[0c847](https://github.com/Afakto/afakto/commit/0c84777e2584875) Jean-Claude Antonio *2022-11-01 18:02:46*

**empty**


[4e301](https://github.com/Afakto/afakto/commit/4e30173f79ea1c0) Jean-Claude Antonio *2022-11-01 17:57:47*

**empty**


[ba572](https://github.com/Afakto/afakto/commit/ba572c08a56c879) Jean-Claude Antonio *2022-11-01 17:56:26*

**test**


[c344b](https://github.com/Afakto/afakto/commit/c344b80b887e9e4) Jean-Claude Antonio *2022-11-01 17:53:55*

**test**


[734e8](https://github.com/Afakto/afakto/commit/734e85c36e24389) Jean-Claude Antonio *2022-11-01 17:53:55*

**empty**


[bb098](https://github.com/Afakto/afakto/commit/bb0987806ad395d) Jean-Claude Antonio *2022-11-01 17:44:59*

**fix terraform**


[471e0](https://github.com/Afakto/afakto/commit/471e0eb64b159df) Jean-Claude Antonio *2022-11-01 17:40:33*

**fix terraform**


[14351](https://github.com/Afakto/afakto/commit/14351de187bb91d) Jean-Claude Antonio *2022-11-01 17:40:33*

**empty**


[c5f9e](https://github.com/Afakto/afakto/commit/c5f9e7eeb9a829e) Jean-Claude Antonio *2022-11-01 17:27:30*

**fix vault**


[24e87](https://github.com/Afakto/afakto/commit/24e87575f2686dc) Jean-Claude Antonio *2022-11-01 17:21:49*

**fix vault**


[24d07](https://github.com/Afakto/afakto/commit/24d07629f7d0e16) Jean-Claude Antonio *2022-11-01 17:21:49*

**set vault for oauth**


[8b72e](https://github.com/Afakto/afakto/commit/8b72e8a9ce9ad75) Jean-Claude Antonio *2022-11-01 17:15:30*

**set vault for oauth**


[6a278](https://github.com/Afakto/afakto/commit/6a2783ee64a2d68) Jean-Claude Antonio *2022-11-01 17:15:30*

**empty**


[31c88](https://github.com/Afakto/afakto/commit/31c885d8175ee9f) Jean-Claude Antonio *2022-11-01 15:47:22*

**add ehcache**


[d4218](https://github.com/Afakto/afakto/commit/d42181c72eb3423) Jean-Claude Antonio *2022-11-01 15:38:03*

**add ehcache**


[d2c8f](https://github.com/Afakto/afakto/commit/d2c8fece0ae45f5) Jean-Claude Antonio *2022-11-01 15:38:03*

**rm redis**


[e9e95](https://github.com/Afakto/afakto/commit/e9e95e6c16b1d38) Jean-Claude Antonio *2022-11-01 14:28:59*

**rm redis**


[e0dce](https://github.com/Afakto/afakto/commit/e0dce757f635aaa) Jean-Claude Antonio *2022-11-01 14:28:59*

**change readme**


[fbee6](https://github.com/Afakto/afakto/commit/fbee6057f57f1e8) Jean-Claude Antonio *2022-11-01 14:03:10*

**change readme**


[8519e](https://github.com/Afakto/afakto/commit/8519e69b2b9d851) Jean-Claude Antonio *2022-11-01 14:03:10*

**rm websocket tests**


[a7fef](https://github.com/Afakto/afakto/commit/a7fefddc816a67f) Jean-Claude Antonio *2022-10-30 19:01:31*

**rm websocket tests**


[6f68c](https://github.com/Afakto/afakto/commit/6f68c3330c0d79b) Jean-Claude Antonio *2022-10-30 19:01:31*

**change page**


[e2c07](https://github.com/Afakto/afakto/commit/e2c07db32925f3c) Jean-Claude Antonio *2022-10-30 17:21:25*

**change page**


[9f648](https://github.com/Afakto/afakto/commit/9f6488b5f47d93f) Jean-Claude Antonio *2022-10-30 17:21:25*

**fix  accepted rejected**


[7d95e](https://github.com/Afakto/afakto/commit/7d95e7affb7fd56) Jean-Claude Antonio *2022-10-30 16:14:53*

**fix  accepted rejected**


[7a93b](https://github.com/Afakto/afakto/commit/7a93ba065c5b068) Jean-Claude Antonio *2022-10-30 16:14:53*

**change db name**


[847a4](https://github.com/Afakto/afakto/commit/847a4cc06212b5a) Jean-Claude Antonio *2022-10-30 14:06:47*

**change db name**


[15870](https://github.com/Afakto/afakto/commit/1587009afe4db61) Jean-Claude Antonio *2022-10-30 14:06:47*

**Fix infinite loop serialize elasticsearch**


[d0b6b](https://github.com/Afakto/afakto/commit/d0b6b205d094d3a) Jean-Claude Antonio *2022-10-29 21:55:58*

**Fix infinite loop serialize elasticsearch**


[58501](https://github.com/Afakto/afakto/commit/58501571873e656) Jean-Claude Antonio *2022-10-29 21:55:58*

**fix dialog**


[b1ab2](https://github.com/Afakto/afakto/commit/b1ab2d1b4fd76d1) Jean-Claude Antonio *2022-10-27 22:40:15*

**fix dialog**


[0b64c](https://github.com/Afakto/afakto/commit/0b64c8746f4a3d4) Jean-Claude Antonio *2022-10-27 22:40:15*

**fix I18N**


[f21f3](https://github.com/Afakto/afakto/commit/f21f325a957d94f) Jean-Claude Antonio *2022-10-27 21:36:58*

**fix I18N**


[d4e67](https://github.com/Afakto/afakto/commit/d4e675d317d8964) Jean-Claude Antonio *2022-10-27 21:36:58*

**fix request credit limit**


[e32f1](https://github.com/Afakto/afakto/commit/e32f1eb60c185e4) Jean-Claude Antonio *2022-10-27 20:48:23*

**fix request credit limit**


[8fca2](https://github.com/Afakto/afakto/commit/8fca26c075a8b2e) Jean-Claude Antonio *2022-10-27 20:48:23*

**refactor**


[bbf3d](https://github.com/Afakto/afakto/commit/bbf3d0c92e55493) Jean-Claude Antonio *2022-10-21 18:11:17*

**refactor**


[3203a](https://github.com/Afakto/afakto/commit/3203a55d303a8f9) Jean-Claude Antonio *2022-10-21 18:11:17*

**cosmetic**


[c8564](https://github.com/Afakto/afakto/commit/c85648b6e3f18d5) Jean-Claude Antonio *2022-10-19 06:11:09*

**cosmetic**


[afdde](https://github.com/Afakto/afakto/commit/afdde224820156d) Jean-Claude Antonio *2022-10-19 06:11:09*

**set port**


[8c577](https://github.com/Afakto/afakto/commit/8c5775a4e7b2769) Jean-Claude Antonio *2022-10-19 06:10:31*

**set port**


[28bac](https://github.com/Afakto/afakto/commit/28bacde4d54c3d8) Jean-Claude Antonio *2022-10-19 06:10:31*

**change primary color**


[8337e](https://github.com/Afakto/afakto/commit/8337e246bfa2e3f) Jean-Claude Antonio *2022-10-17 20:34:01*

**change primary color**


[751ee](https://github.com/Afakto/afakto/commit/751ee49583a9de0) Jean-Claude Antonio *2022-10-17 20:34:01*

**fix new**


[86086](https://github.com/Afakto/afakto/commit/860861882ffd7bf) Jean-Claude Antonio *2022-10-17 20:33:49*

**fix new**


[768be](https://github.com/Afakto/afakto/commit/768be4261d02b84) Jean-Claude Antonio *2022-10-17 20:33:49*

**Improve logs display**


[c9ef4](https://github.com/Afakto/afakto/commit/c9ef4e5be581ba6) Jean-Claude Antonio *2022-10-17 20:03:04*

**Improve logs display**


[8542a](https://github.com/Afakto/afakto/commit/8542a683ff55fb9) Jean-Claude Antonio *2022-10-17 20:03:04*

**cosmetic changes**


[c417c](https://github.com/Afakto/afakto/commit/c417c24cdd60998) Jean-Claude Antonio *2022-10-17 18:38:33*

**cosmetic changes**


[55ee6](https://github.com/Afakto/afakto/commit/55ee6111d42faa4) Jean-Claude Antonio *2022-10-17 18:38:33*

**update dependency audit**


[87f10](https://github.com/Afakto/afakto/commit/87f1018646a75f6) Jean-Claude Antonio *2022-10-17 18:13:04*

**update dependency audit**


[3c83b](https://github.com/Afakto/afakto/commit/3c83b41c7be6d27) Jean-Claude Antonio *2022-10-17 18:13:04*

**Display future health check for banks**


[bb5b8](https://github.com/Afakto/afakto/commit/bb5b8d8930328c0) Jean-Claude Antonio *2022-10-17 17:25:38*

**Display future health check for banks**


[6368f](https://github.com/Afakto/afakto/commit/6368f1419a67e06) Jean-Claude Antonio *2022-10-17 17:25:38*

**Improve code**


[aecbb](https://github.com/Afakto/afakto/commit/aecbbe862d2bfee) Jean-Claude Antonio *2022-10-17 17:25:13*

**Improve code**


[6d715](https://github.com/Afakto/afakto/commit/6d7152101c10bb7) Jean-Claude Antonio *2022-10-17 17:25:13*

**clean**


[36185](https://github.com/Afakto/afakto/commit/3618532e69139e7) Jean-Claude Antonio *2022-10-16 19:38:17*

**clean**


[20b0e](https://github.com/Afakto/afakto/commit/20b0e2a24ae661d) Jean-Claude Antonio *2022-10-16 19:38:17*

**refactor client service vue**


[99547](https://github.com/Afakto/afakto/commit/***************) Jean-Claude Antonio *2022-10-16 19:18:35*

**refactor client service vue**


[19504](https://github.com/Afakto/afakto/commit/19504cc553839a8) Jean-Claude Antonio *2022-10-16 19:18:35*

**add interger validation for nber of days**


[c4e60](https://github.com/Afakto/afakto/commit/c4e6011da894e8f) Jean-Claude Antonio *2022-10-16 12:53:21*

**add interger validation for nber of days**


[892f2](https://github.com/Afakto/afakto/commit/892f2b6a6308c9f) Jean-Claude Antonio *2022-10-16 12:53:21*

**Imporve client display**


[03b54](https://github.com/Afakto/afakto/commit/03b54362e3c2ca7) Jean-Claude Antonio *2022-10-16 11:57:41*

**Imporve client display**


[0026c](https://github.com/Afakto/afakto/commit/0026c9df712888e) Jean-Claude Antonio *2022-10-16 11:57:41*

**cosmetic changes**


[bca5a](https://github.com/Afakto/afakto/commit/bca5a08c5ab910b) Jean-Claude Antonio *2022-10-16 11:38:26*

**cosmetic changes**


[006e7](https://github.com/Afakto/afakto/commit/006e74af8dfc24b) Jean-Claude Antonio *2022-10-16 11:38:26*

**Refactor contact address to prepare for bank**


[5ce8c](https://github.com/Afakto/afakto/commit/5ce8cdb8d19d127) Jean-Claude Antonio *2022-10-15 19:38:49*

**Refactor contact address to prepare for bank**


[0cc27](https://github.com/Afakto/afakto/commit/0cc2768da4e6d0f) Jean-Claude Antonio *2022-10-15 19:38:49*

**rm unused pages**


[7205c](https://github.com/Afakto/afakto/commit/7205c01dbd93f4a) Jean-Claude Antonio *2022-10-14 16:21:57*

**rm unused pages**


[61cc5](https://github.com/Afakto/afakto/commit/61cc5409b06b470) Jean-Claude Antonio *2022-10-14 16:21:57*

**Fix order**


[efb91](https://github.com/Afakto/afakto/commit/efb9185d9227502) Jean-Claude Antonio *2022-10-14 16:17:57*

**Fix order**


[de85b](https://github.com/Afakto/afakto/commit/de85b8bfded965f) Jean-Claude Antonio *2022-10-14 16:17:57*

**update postgres docker**


[bec69](https://github.com/Afakto/afakto/commit/bec69cd4103df40) Jean-Claude Antonio *2022-10-14 14:59:29*

**update postgres docker**


[7c5a9](https://github.com/Afakto/afakto/commit/7c5a9f96151e1ec) Jean-Claude Antonio *2022-10-14 14:59:29*

**set layout**


[7ee03](https://github.com/Afakto/afakto/commit/7ee0376af2dc861) Jean-Claude Antonio *2022-10-13 22:55:08*

**set layout**


[04fdf](https://github.com/Afakto/afakto/commit/04fdfaf2d990b0f) Jean-Claude Antonio *2022-10-13 22:55:08*

**add MonetaryAmount**


[c2800](https://github.com/Afakto/afakto/commit/c2800b9f488a766) Jean-Claude Antonio *2022-10-13 22:36:51*

**add MonetaryAmount**


[a4499](https://github.com/Afakto/afakto/commit/a4499aded323788) Jean-Claude Antonio *2022-10-13 22:36:51*

**Add sample Health check**


[de572](https://github.com/Afakto/afakto/commit/de572d33df1c4ab) Jean-Claude Antonio *2022-10-12 15:48:13*

**Add sample Health check**


[1a135](https://github.com/Afakto/afakto/commit/1a135b9888db53e) Jean-Claude Antonio *2022-10-12 15:48:13*

**draft getting started**


[f8d5f](https://github.com/Afakto/afakto/commit/f8d5fefca8286cd) Jean-Claude Antonio *2022-10-11 20:11:16*

**draft getting started**


[c6b0c](https://github.com/Afakto/afakto/commit/c6b0c696b40e604) Jean-Claude Antonio *2022-10-11 20:11:16*

**use env variables**


[37f7a](https://github.com/Afakto/afakto/commit/37f7aecf37ed65f) Jean-Claude Antonio *2022-10-11 20:10:56*

**use env variables**


[07d81](https://github.com/Afakto/afakto/commit/07d81225afca742) Jean-Claude Antonio *2022-10-11 20:10:56*

**rm ROOT_API**


[73412](https://github.com/Afakto/afakto/commit/734125bdcf78c16) Jean-Claude Antonio *2022-10-11 18:32:00*

**rm ROOT_API**


[4f138](https://github.com/Afakto/afakto/commit/4f1389e310f4261) Jean-Claude Antonio *2022-10-11 18:32:00*

**test**


[9c204](https://github.com/Afakto/afakto/commit/9c2045707796cc7) Jean-Claude Antonio *2022-10-11 17:56:57*

**test**


[296b2](https://github.com/Afakto/afakto/commit/296b20bb18a0356) Jean-Claude Antonio *2022-10-11 17:56:57*

**test**


[b9746](https://github.com/Afakto/afakto/commit/b97466ce011af03) Jean-Claude Antonio *2022-10-11 17:49:56*

**test**


[25806](https://github.com/Afakto/afakto/commit/25806e6c4f7aba4) Jean-Claude Antonio *2022-10-11 17:49:56*

**test**


[e3aa6](https://github.com/Afakto/afakto/commit/e3aa6a11e601734) Jean-Claude Antonio *2022-10-11 16:00:48*

**test**


[2b2c5](https://github.com/Afakto/afakto/commit/2b2c5b3ad2d50f1) Jean-Claude Antonio *2022-10-11 16:00:48*

**test**


[54aab](https://github.com/Afakto/afakto/commit/54aabc26dd5e712) Jean-Claude Antonio *2022-10-11 15:53:44*

**test**


[1fdc1](https://github.com/Afakto/afakto/commit/1fdc1a20b3c6ce0) Jean-Claude Antonio *2022-10-11 15:53:44*

**test**


[a2aad](https://github.com/Afakto/afakto/commit/a2aad64f76760cf) Jean-Claude Antonio *2022-10-11 11:37:02*

**test**


[12af6](https://github.com/Afakto/afakto/commit/12af697dcc4f4a2) Jean-Claude Antonio *2022-10-11 11:37:02*

**test**


[2b957](https://github.com/Afakto/afakto/commit/2b95765c2cc3e65) Jean-Claude Antonio *2022-10-11 11:34:58*

**test**


[0b96f](https://github.com/Afakto/afakto/commit/0b96f8753b2e162) Jean-Claude Antonio *2022-10-11 11:34:58*

**test**


[c03e4](https://github.com/Afakto/afakto/commit/c03e4429f56527f) Jean-Claude Antonio *2022-10-10 18:42:01*

**test**


[698b4](https://github.com/Afakto/afakto/commit/698b476cb8085c3) Jean-Claude Antonio *2022-10-10 18:42:01*

**test**


[6bc7e](https://github.com/Afakto/afakto/commit/6bc7eed946671e2) Jean-Claude Antonio *2022-10-10 18:38:33*

**test**


[52485](https://github.com/Afakto/afakto/commit/524853909ed31d8) Jean-Claude Antonio *2022-10-10 18:38:33*

**SPA_CDN**


[e44e3](https://github.com/Afakto/afakto/commit/e44e348e682f2b5) Jean-Claude Antonio *2022-10-10 18:24:24*

**SPA_CDN**


[0f9f0](https://github.com/Afakto/afakto/commit/0f9f03b17ec148c) Jean-Claude Antonio *2022-10-10 18:24:24*

**Add ROOT_API in build CDN**


[b4c4c](https://github.com/Afakto/afakto/commit/b4c4c29f599d251) Jean-Claude Antonio *2022-10-09 21:23:19*

**Add ROOT_API in build CDN**


[3af91](https://github.com/Afakto/afakto/commit/3af9179cb592932) Jean-Claude Antonio *2022-10-09 21:23:19*

**remove /spa for CDN**


[b5e9c](https://github.com/Afakto/afakto/commit/b5e9c7225ca3f06) Jean-Claude Antonio *2022-10-09 21:06:28*

**remove /spa for CDN**


[4596a](https://github.com/Afakto/afakto/commit/4596aae12a48506) Jean-Claude Antonio *2022-10-09 21:06:28*

**Allow image from https://i2.wp.com https://s.gravatar.com**


[c8bd3](https://github.com/Afakto/afakto/commit/c8bd3d1a2a2b3b1) Jean-Claude Antonio *2022-10-09 21:05:49*

**Allow image from https://i2.wp.com https://s.gravatar.com**


[19968](https://github.com/Afakto/afakto/commit/199683a3c3f41e8) Jean-Claude Antonio *2022-10-09 21:05:49*

**fix spa**


[bef96](https://github.com/Afakto/afakto/commit/bef96cc65c0c295) Jean-Claude Antonio *2022-10-09 19:49:16*

**fix spa**


[a5019](https://github.com/Afakto/afakto/commit/a5019cedb3910e2) Jean-Claude Antonio *2022-10-09 19:49:16*

**Test CDN**


[34c86](https://github.com/Afakto/afakto/commit/34c86724496892d) Jean-Claude Antonio *2022-10-06 21:40:58*

**Test CDN**


[0c14c](https://github.com/Afakto/afakto/commit/0c14c239f7ea2ef) Jean-Claude Antonio *2022-10-06 21:40:58*

**set server IP**


[91d4e](https://github.com/Afakto/afakto/commit/91d4ed1a9917a7f) Jean-Claude Antonio *2022-10-05 19:10:29*

**set server IP**


[22954](https://github.com/Afakto/afakto/commit/2295450a99fd5d5) Jean-Claude Antonio *2022-10-05 19:10:29*

**fix embeded SPA**


[0b662](https://github.com/Afakto/afakto/commit/0b6628369c9cf96) Jean-Claude Antonio *2022-10-05 06:44:33*

**fix embeded SPA**


[00e2d](https://github.com/Afakto/afakto/commit/00e2d68f46b8e1f) Jean-Claude Antonio *2022-10-05 06:44:33*

**delete old wf**


[cdf56](https://github.com/Afakto/afakto/commit/cdf5600cd17e471) Jean-Claude Antonio *2022-10-03 20:47:53*

**delete old wf**


[851fc](https://github.com/Afakto/afakto/commit/851fc975826c3a9) Jean-Claude Antonio *2022-10-03 20:47:53*

**change port**


[4a716](https://github.com/Afakto/afakto/commit/4a71653f81e4b9c) Jean-Claude Antonio *2022-10-03 20:47:42*

**change port**


[07970](https://github.com/Afakto/afakto/commit/079707cba4c001b) Jean-Claude Antonio *2022-10-03 20:47:42*

**test**


[e79a6](https://github.com/Afakto/afakto/commit/e79a671f980b2b7) Jean-Claude Antonio *2022-10-03 20:08:00*

**test**


[1cd1d](https://github.com/Afakto/afakto/commit/1cd1d5005aa8687) Jean-Claude Antonio *2022-10-03 20:08:00*

**update SPRING_ELASTIC_URL**


[cc6d6](https://github.com/Afakto/afakto/commit/cc6d67239fbc1f2) Jean-Claude Antonio *2022-10-02 20:02:06*

**update SPRING_ELASTIC_URL**


[889e1](https://github.com/Afakto/afakto/commit/889e1342457dafa) Jean-Claude Antonio *2022-10-02 20:02:06*

**fix logs terraform**


[e76e1](https://github.com/Afakto/afakto/commit/e76e1f1112aad16) Jean-Claude Antonio *2022-10-02 19:47:48*

**fix logs terraform**


[ac742](https://github.com/Afakto/afakto/commit/ac7420c3f932ca4) Jean-Claude Antonio *2022-10-02 19:47:48*

**update terraform**


[d6dcb](https://github.com/Afakto/afakto/commit/d6dcbdb177d42c3) Jean-Claude Antonio *2022-10-02 19:45:07*

**update terraform**


[056bb](https://github.com/Afakto/afakto/commit/056bb0966bad783) Jean-Claude Antonio *2022-10-02 19:45:07*

**add app service default settings**


[7af6f](https://github.com/Afakto/afakto/commit/7af6fe704164776) Jean-Claude Antonio *2022-10-02 19:36:46*

**add app service default settings**


[4ec68](https://github.com/Afakto/afakto/commit/4ec687320ba7f04) Jean-Claude Antonio *2022-10-02 19:36:46*

**change sku_name**


[c0392](https://github.com/Afakto/afakto/commit/c0392829377446d) Jean-Claude Antonio *2022-10-02 19:21:02*

**change sku_name**


[206a5](https://github.com/Afakto/afakto/commit/206a580d5182de1) Jean-Claude Antonio *2022-10-02 19:21:02*

**always on**


[9a4b7](https://github.com/Afakto/afakto/commit/9a4b7397c0e302b) Jean-Claude Antonio *2022-10-02 19:17:23*

**always on**


[04cb0](https://github.com/Afakto/afakto/commit/04cb00e0cc46840) Jean-Claude Antonio *2022-10-02 19:17:23*

**udpate install script**


[f486b](https://github.com/Afakto/afakto/commit/f486b3b27c22157) Jean-Claude Antonio *2022-10-02 18:49:00*

**udpate install script**


[8b4ff](https://github.com/Afakto/afakto/commit/8b4ff721d411575) Jean-Claude Antonio *2022-10-02 18:49:00*

**test**


[b968d](https://github.com/Afakto/afakto/commit/b968d06a4d0ac4a) Jean-Claude Antonio *2022-09-29 14:59:41*

**test**


[5b623](https://github.com/Afakto/afakto/commit/5b623456f8ac4a5) Jean-Claude Antonio *2022-09-29 14:59:41*

**test stdout logs**


[57d60](https://github.com/Afakto/afakto/commit/57d6042d07fad5d) Jean-Claude Antonio *2022-09-29 14:56:06*

**test stdout logs**


[19c6d](https://github.com/Afakto/afakto/commit/19c6d51a3f92084) Jean-Claude Antonio *2022-09-29 14:56:06*

**change logs for prod**


[ae6fe](https://github.com/Afakto/afakto/commit/ae6fe02016c0b9f) Jean-Claude Antonio *2022-09-29 14:37:23*

**change logs for prod**


[783b8](https://github.com/Afakto/afakto/commit/783b87d3209cd15) Jean-Claude Antonio *2022-09-29 14:37:23*

**add redis ssl**


[e7ae9](https://github.com/Afakto/afakto/commit/e7ae929f8d1a489) Jean-Claude Antonio *2022-09-29 13:58:11*

**add redis ssl**


[0ffef](https://github.com/Afakto/afakto/commit/0ffefce8a6d603c) Jean-Claude Antonio *2022-09-29 13:58:11*

**test**


[96cfa](https://github.com/Afakto/afakto/commit/96cfa6c8f7b5bf9) Jean-Claude Antonio *2022-09-29 11:08:18*

**test**


[8deb5](https://github.com/Afakto/afakto/commit/8deb53ff6388d2d) Jean-Claude Antonio *2022-09-29 11:08:18*

**test**


[49cd9](https://github.com/Afakto/afakto/commit/49cd9e28df70df1) Jean-Claude Antonio *2022-09-28 20:41:10*

**test**


[2817e](https://github.com/Afakto/afakto/commit/2817e5f6ac65d48) Jean-Claude Antonio *2022-09-28 20:41:10*

**test without infra**


[8477b](https://github.com/Afakto/afakto/commit/8477b2662f74e85) Jean-Claude Antonio *2022-09-28 19:29:51*

**test without infra**


[17fcf](https://github.com/Afakto/afakto/commit/17fcf25a5b49e09) Jean-Claude Antonio *2022-09-28 19:29:51*

**test**


[d3d62](https://github.com/Afakto/afakto/commit/d3d62284dedaa83) Jean-Claude Antonio *2022-09-28 18:53:50*

**test**


[15bf5](https://github.com/Afakto/afakto/commit/15bf57d2de3b91c) Jean-Claude Antonio *2022-09-28 18:53:50*

**test**


[9c444](https://github.com/Afakto/afakto/commit/9c4447df3092ead) Jean-Claude Antonio *2022-09-28 12:54:24*

**test**


[9182b](https://github.com/Afakto/afakto/commit/9182bcc1c7515d3) Jean-Claude Antonio *2022-09-28 12:54:24*

**test**


[92223](https://github.com/Afakto/afakto/commit/9222357b966f2b7) Jean-Claude Antonio *2022-09-28 12:53:31*

**test**


[6480b](https://github.com/Afakto/afakto/commit/6480b6561db3347) Jean-Claude Antonio *2022-09-28 12:53:31*

**add env variables**


[f6652](https://github.com/Afakto/afakto/commit/f66524a59da7d1b) Jean-Claude Antonio *2022-09-27 16:15:08*

**add env variables**


[0f572](https://github.com/Afakto/afakto/commit/0f57226b9573901) Jean-Claude Antonio *2022-09-27 16:15:08*

**Use env for redis**


[88c6a](https://github.com/Afakto/afakto/commit/88c6aaac4221d0a) Jean-Claude Antonio *2022-09-27 14:05:28*

**Use env for redis**


[736d5](https://github.com/Afakto/afakto/commit/736d5c8f82efcf0) Jean-Claude Antonio *2022-09-27 14:05:28*

**empty**


[ef074](https://github.com/Afakto/afakto/commit/ef074102150590c) Jean-Claude Antonio *2022-09-26 21:01:43*

**test gitops-deploy-to-app-service-java**


[c749e](https://github.com/Afakto/afakto/commit/c749ebfa618a7e9) Jean-Claude Antonio *2022-09-26 20:38:37*

**test gitops-deploy-to-app-service-java**


[87a8f](https://github.com/Afakto/afakto/commit/87a8fda9345cbf8) Jean-Claude Antonio *2022-09-26 20:38:37*

**empty**


[2c8b7](https://github.com/Afakto/afakto/commit/2c8b7e1de650def) Jean-Claude Antonio *2022-09-26 20:24:35*

**empty**


[b03bb](https://github.com/Afakto/afakto/commit/b03bbede8f3687e) Jean-Claude Antonio *2022-09-26 19:59:43*

**Empty**


[d64e1](https://github.com/Afakto/afakto/commit/d64e162056e2cff) Jean-Claude Antonio *2022-09-26 19:39:48*

**test spring-apps-deploy**


[a234b](https://github.com/Afakto/afakto/commit/a234b019815df5e) Jean-Claude Antonio *2022-09-26 19:32:04*

**test spring-apps-deploy**


[419bc](https://github.com/Afakto/afakto/commit/419bc1aca2384ce) Jean-Claude Antonio *2022-09-26 19:32:04*

**test**


[8065b](https://github.com/Afakto/afakto/commit/8065bcda5528403) Jean-Claude Antonio *2022-09-26 19:28:53*

**test**


[360e2](https://github.com/Afakto/afakto/commit/360e2ab0d418bbe) Jean-Claude Antonio *2022-09-26 19:28:53*

**test**


[dedf1](https://github.com/Afakto/afakto/commit/dedf150567cd29d) Jean-Claude Antonio *2022-09-26 18:56:26*

**test**


[30f9a](https://github.com/Afakto/afakto/commit/30f9a50fe9176cb) Jean-Claude Antonio *2022-09-26 18:56:26*

**test**


[1e05e](https://github.com/Afakto/afakto/commit/1e05ef74b58d28d) Jean-Claude Antonio *2022-09-26 18:44:24*

**test**


[1809d](https://github.com/Afakto/afakto/commit/1809d008b5468bd) Jean-Claude Antonio *2022-09-26 18:44:24*

**test**


[23273](https://github.com/Afakto/afakto/commit/23273b0ffc234e2) Jean-Claude Antonio *2022-09-26 18:32:32*

**test**


[1c8d9](https://github.com/Afakto/afakto/commit/1c8d904b1d756b8) Jean-Claude Antonio *2022-09-26 18:32:32*

**use use_staging_deployment underscore**


[9e725](https://github.com/Afakto/afakto/commit/9e725c855163a59) Jean-Claude Antonio *2022-09-26 15:54:45*

**use use_staging_deployment underscore**


[3842c](https://github.com/Afakto/afakto/commit/3842ce903164452) Jean-Claude Antonio *2022-09-26 15:54:45*

**do no use use-staging-deployment**


[d0c69](https://github.com/Afakto/afakto/commit/d0c69d401a6be23) Jean-Claude Antonio *2022-09-26 15:40:19*

**do no use use-staging-deployment**


[56ec4](https://github.com/Afakto/afakto/commit/56ec4ecf8be2680) Jean-Claude Antonio *2022-09-26 15:40:19*

**tes without docker file**


[87431](https://github.com/Afakto/afakto/commit/8743180f7773051) Jean-Claude Antonio *2022-09-26 13:41:44*

**tes without docker file**


[5c65d](https://github.com/Afakto/afakto/commit/5c65d3a7d057a97) Jean-Claude Antonio *2022-09-26 13:41:44*

**Empty-Commit**


[bec09](https://github.com/Afakto/afakto/commit/bec09f8227a69c3) Jean-Claude Antonio *2022-09-25 21:43:54*

**Empty-Commit**


[5facc](https://github.com/Afakto/afakto/commit/5facc8ae2810935) Jean-Claude Antonio *2022-09-25 21:22:01*

**change location**


[c439f](https://github.com/Afakto/afakto/commit/c439f8cef2540d5) Jean-Claude Antonio *2022-09-25 21:05:04*

**change location**


[29505](https://github.com/Afakto/afakto/commit/2950576b42155da) Jean-Claude Antonio *2022-09-25 21:05:04*

**rv dep**


[e5d41](https://github.com/Afakto/afakto/commit/e5d41737c354f42) Jean-Claude Antonio *2022-09-25 13:54:20*

**rv dep**


[65d87](https://github.com/Afakto/afakto/commit/65d87d7ba532ce9) Jean-Claude Antonio *2022-09-25 13:54:20*

**Merge branch 'main' into env-prod**


[e7896](https://github.com/Afakto/afakto/commit/e7896300280cef9) Jean-Claude Antonio *2022-09-25 13:39:34*

**Merge branch 'main' into env-prod**


[36090](https://github.com/Afakto/afakto/commit/3609016552b3483) Jean-Claude Antonio *2022-09-25 13:39:34*

**try fix**


[6dfac](https://github.com/Afakto/afakto/commit/6dfac178a796186) Jean-Claude Antonio *2022-09-25 13:39:01*

**Empty-Commit**


[f74b3](https://github.com/Afakto/afakto/commit/f74b37daf1aa112) Jean-Claude Antonio *2022-09-25 12:53:24*

**Empty-Commit**


[8807d](https://github.com/Afakto/afakto/commit/8807d138ce7e7b8) Jean-Claude Antonio *2022-09-25 12:47:45*

**add init file**


[f1627](https://github.com/Afakto/afakto/commit/f16277e0ef095a3) Jean-Claude Antonio *2022-09-25 12:25:17*

**Merge branch 'main' into env-prod**


[80d20](https://github.com/Afakto/afakto/commit/80d2054eed95642) Jean-Claude Antonio *2022-09-23 13:05:36*

**change RG name**


[7326d](https://github.com/Afakto/afakto/commit/7326d331046dfbc) Jean-Claude Antonio *2022-09-23 13:04:46*

**test**


[4e881](https://github.com/Afakto/afakto/commit/4e8815538f8fbd2) Jean-Claude Antonio *2022-09-23 12:50:06*

**Merge branch 'main' into env-prod**


[f9afa](https://github.com/Afakto/afakto/commit/f9afaf1740bd8a0) Jean-Claude Antonio *2022-09-21 18:53:39*

**add Azure doc**


[11fef](https://github.com/Afakto/afakto/commit/11fef38e7894fca) Jean-Claude Antonio *2022-09-21 18:51:53*

**fix login**


[886cf](https://github.com/Afakto/afakto/commit/886cf1dae66cd3e) Jean-Claude Antonio *2022-09-21 08:41:37*

**replaceuex by Pinia**


[7f1e4](https://github.com/Afakto/afakto/commit/7f1e4cc5cef51a7) Jean-Claude Antonio *2022-09-19 21:05:01*

**fix store**


[1b525](https://github.com/Afakto/afakto/commit/1b525e63d9e76da) Jean-Claude Antonio *2022-09-16 13:54:10*

**add trace**


[b8f53](https://github.com/Afakto/afakto/commit/b8f5316d8277736) Jean-Claude Antonio *2022-09-16 13:30:41*

**try function instead of arrow for "TypeError: Cannot read properties of undefined (reading 'getters')" error**


[528c9](https://github.com/Afakto/afakto/commit/528c9c03cdb1b04) Jean-Claude Antonio *2022-09-16 13:16:08*

**change swagger-ui and i18n build paths**


[d64f6](https://github.com/Afakto/afakto/commit/d64f686d97541b0) Jean-Claude Antonio *2022-09-16 08:50:27*

**change output**


[29f5d](https://github.com/Afakto/afakto/commit/29f5d7a937f81a8) Jean-Claude Antonio *2022-09-15 21:57:50*

**change**


[37c64](https://github.com/Afakto/afakto/commit/37c6476f7beeb4b) Jean-Claude Antonio *2022-09-15 21:51:44*

**test**


[85247](https://github.com/Afakto/afakto/commit/85247f951bcb683) Jean-Claude Antonio *2022-09-15 21:46:08*

**fix  @typescript-eslint/rule-name**


[b34f4](https://github.com/Afakto/afakto/commit/b34f437d4b1231d) Jean-Claude Antonio *2022-09-15 21:25:35*

**update**


[39de3](https://github.com/Afakto/afakto/commit/39de3a92f4b62f2) Jean-Claude Antonio *2022-09-15 21:00:32*

**update typescript-eslint**


[00434](https://github.com/Afakto/afakto/commit/0043492e327abe2) Jean-Claude Antonio *2022-09-15 20:51:50*

**add typescript-eslint**


[a09d3](https://github.com/Afakto/afakto/commit/a09d3883486a460) Jean-Claude Antonio *2022-09-15 20:42:17*

**add azure static wep apps**


[6c82e](https://github.com/Afakto/afakto/commit/6c82eb1194925a5) Jean-Claude Antonio *2022-09-15 20:25:58*

**update**


[cc485](https://github.com/Afakto/afakto/commit/cc4856ac553015f) Jean-Claude Antonio *2022-09-15 19:21:58*

**rm unsused  steps**


[37759](https://github.com/Afakto/afakto/commit/3775971638402e8) Jean-Claude Antonio *2022-09-14 21:06:01*

**add git-ops**


[52f14](https://github.com/Afakto/afakto/commit/52f14c545a6d8c6) Jean-Claude Antonio *2022-09-14 20:50:11*

**test**


[f4259](https://github.com/Afakto/afakto/commit/f425901467f1e5e) Jean-Claude Antonio *2022-09-08 21:54:52*

**test**


[12c69](https://github.com/Afakto/afakto/commit/12c69393a2f465d) Jean-Claude Antonio *2022-09-08 21:13:26*

**add env in liqubase**


[ceee2](https://github.com/Afakto/afakto/commit/ceee2f596b65843) Jean-Claude Antonio *2022-09-08 21:06:43*

**add postgresql_role**


[5bf3d](https://github.com/Afakto/afakto/commit/5bf3d36602cb591) Jean-Claude Antonio *2022-09-08 20:43:13*

**test**


[6180c](https://github.com/Afakto/afakto/commit/6180c3a226226d4) Jean-Claude Antonio *2022-09-08 19:27:59*

**change default usr**


[81b4a](https://github.com/Afakto/afakto/commit/81b4af4be31b9b4) Jean-Claude Antonio *2022-09-08 19:17:24*

**skip test**


[6ef50](https://github.com/Afakto/afakto/commit/6ef504fccbb548f) Jean-Claude Antonio *2022-09-08 18:32:31*

**test**


[690ae](https://github.com/Afakto/afakto/commit/690ae324e481b5c) Jean-Claude Antonio *2022-09-07 21:11:45*

**test**


[6ced8](https://github.com/Afakto/afakto/commit/6ced8310ae78e44) Jean-Claude Antonio *2022-09-07 21:02:05*

**add map by**


[6de25](https://github.com/Afakto/afakto/commit/6de25da4ea8979f) Jean-Claude Antonio *2022-09-07 20:38:45*

**disable schema validation**


[f7680](https://github.com/Afakto/afakto/commit/f7680ad3c7cdc4a) Jean-Claude Antonio *2022-09-07 20:19:47*

**[main] fix javadoc**


[94190](https://github.com/Afakto/afakto/commit/941905f22ac7665) Jean-Claude Antonio *2022-09-05 20:47:38*

**[main] fix javadocs**


[cb0cd](https://github.com/Afakto/afakto/commit/cb0cdc36dc33dcd) Jean-Claude Antonio *2022-09-05 20:33:11*

**[main] Add github action**


[d6bda](https://github.com/Afakto/afakto/commit/d6bda9effb40724) Jean-Claude Antonio *2022-09-05 20:10:01*

**[main] Init**


[147c9](https://github.com/Afakto/afakto/commit/147c93f3b5b9cc6) Jean-Claude Antonio *2022-09-04 19:15:53*


