#!/bin/bash

ORIG_DIR=/mnt/afakto/archive/anjac/bnp_account_statement/********
TEST_DIR=/mnt/afakto/archive/test/bnp_account_statement/********
DEST_DIR=/mnt/afakto/temp/test/bnp_account_statement/********
DEST_DIR_FINAL=/mnt/afakto/in/test/bnp_account_statement/********

echo "Starting synchronisation from $ORIG_DIR to $DEST_DIR_FINAL"

mkdir -p $DEST_DIR
mkdir -p "$DEST_DIR_FINAL"

# Find latest file from DEST_DIR
latest=$(find $TEST_DIR -type f -printf "%f\n" | sort -r | head -n 1)

echo "Latest file synchronised: $latest"

# Track if we copied any files
files_copied=0

# List all the files from ORIG_DIR that follow latest by name
find $ORIG_DIR -type f -printf "%f\n" | sort | while read -r file; do
  if [[ "$file" > "$latest" ]]; then
    echo "Copying: $file"
    cp "$ORIG_DIR/$file" "$DEST_DIR/" && ((files_copied++))
  fi
done

gunzip $DEST_DIR/*
mv $DEST_DIR/* $DEST_DIR_FINAL

echo "Synchronization completed for $files_copied files"
