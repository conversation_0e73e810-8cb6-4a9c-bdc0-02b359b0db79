#!/bin/sh

set -e

# Parse command line arguments
skip_tests=false
version=""

for arg in "$@"; do
  case $arg in
  --no-test)
    skip_tests=true
    shift
    ;;
  *)
    if [ -z "$version" ]; then
      version="$arg"
    fi
    ;;
  esac
done

# Generate version number from date if not provided:
if [ -z "$version" ]; then
  version=$(date +%Y%m%d)
fi

# Run tests unless skipped
if [ "$skip_tests" = false ]; then
  echo "Running Maven tests to verify build"
  if ! ./bin/mvnw clean verify; then
    echo "Maven tests failed. Aborting deployment."
    exit 1
  fi

  echo "Running Cypress tests"
  cd src/test/javascript
  if ! npx cypress run --browser chromium; then
    echo "Cypress tests failed. Aborting deployment."
    cd ../../..
    exit 1
  fi
  cd ../../..
fi

echo "Updating version number to $version"
sed -i "0,/<version>.*<\/version>/s//<version>$version<\/version>/" pom.xml
git commit pom.xml -m "chore(build): version $version"

echo "Tagging commit with version $version"
git tag -a "$version" -m "chore(build): version $version"

echo "Regenerating changelog"
./bin/mvnw generate-sources
git add CHANGELOG.md
git commit --amend --no-edit
git tag -d "$version"
git tag -a "$version" -m "chore(build): version $version" # Recreate it pointing to the amended commit

echo "Pushing commit and tag to GitHub"
git push --follow-tags

echo "Merging commit into env-prod branch"
git checkout env-prod
git merge main
git push
git checkout main

echo "
  Deployment complete.
  Check GitHub Actions for build progress,
  and Azure for deployment status.
"
