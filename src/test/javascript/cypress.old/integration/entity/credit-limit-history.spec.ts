import { entityItemSelector } from '../../support/commands';
import {
  entityTableSelector,
  entityDetailsButtonSelector,
  entityDetailsBackButtonSelector,
  entityCreateButtonSelector,
  entityCreateSaveButtonSelector,
  entityCreateCancelButtonSelector,
  entityEditButtonSelector,
  entityDeleteButtonSelector,
  entityConfirmDeleteButtonSelector,
} from '../../support/entity';

describe('CreditLimitHistory e2e test', () => {
  const creditLimitHistoryPageUrl = '/credit-limit-history';
  const creditLimitHistoryPageUrlPattern = new RegExp('/credit-limit-history(\\?.*)?$');
  const username = Cypress.env('E2E_USERNAME') ?? 'user';
  const password = Cypress.env('E2E_PASSWORD') ?? 'user';
  const creditLimitHistorySample = {};

  let creditLimitHistory: any;
  let creditLimit: any;

  beforeEach(() => {
    cy.login(username, password);
  });

  beforeEach(() => {
    // create an instance at the required relationship entity:
    cy.authenticatedRequest({
      method: 'POST',
      url: '/api/credit-limits',
      body: {
        totalOutstanding: 95061,
        outstandingUnderFactor: 87338,
        currentCreditLimit: 84132,
        requestedCreditLimit: 38908,
        date: '2022-06-21T11:52:02.833Z',
        status: 'REQUESTED',
      },
    }).then(({ body }) => {
      creditLimit = body;
    });
  });

  beforeEach(() => {
    cy.intercept('GET', '/api/credit-limit-histories+(?*|)').as('entitiesRequest');
    cy.intercept('POST', '/api/credit-limit-histories').as('postEntityRequest');
    cy.intercept('DELETE', '/api/credit-limit-histories/*').as('deleteEntityRequest');
  });

  beforeEach(() => {
    // Simulate relationships api for better performance and reproducibility.
    cy.intercept('GET', '/api/credit-limits', {
      statusCode: 200,
      body: [creditLimit],
    });

    cy.intercept('GET', '/api/buyers', {
      statusCode: 200,
      body: [],
    });
  });

  afterEach(() => {
    if (creditLimitHistory) {
      cy.authenticatedRequest({
        method: 'DELETE',
        url: `/api/credit-limit-histories/${creditLimitHistory.id}`,
      }).then(() => {
        creditLimitHistory = undefined;
      });
    }
  });

  afterEach(() => {
    if (creditLimit) {
      cy.authenticatedRequest({
        method: 'DELETE',
        url: `/api/credit-limits/${creditLimit.id}`,
      }).then(() => {
        creditLimit = undefined;
      });
    }
  });

  it('CreditLimitHistories menu should load CreditLimitHistories page', () => {
    cy.visit('/');
    cy.clickOnEntityMenuItem('credit-limit-history');
    cy.wait('@entitiesRequest').then(({ response }) => {
      if (response!.body.length === 0) {
        cy.get(entityTableSelector).should('not.exist');
      } else {
        cy.get(entityTableSelector).should('exist');
      }
    });
    cy.getEntityHeading('CreditLimitHistory').should('exist');
    cy.url().should('match', creditLimitHistoryPageUrlPattern);
  });

  describe('CreditLimitHistory page', () => {
    describe('create button click', () => {
      beforeEach(() => {
        cy.visit(creditLimitHistoryPageUrl);
        cy.wait('@entitiesRequest');
      });

      it('should load create CreditLimitHistory page', () => {
        cy.get(entityCreateButtonSelector).click();
        cy.url().should('match', new RegExp('/credit-limit-history/new$'));
        cy.getEntityCreateUpdateHeading('CreditLimitHistory');
        cy.get(entityCreateSaveButtonSelector).should('exist');
        cy.get(entityCreateCancelButtonSelector).click();
        cy.wait('@entitiesRequest').then(({ response }) => {
          expect(response!.statusCode).to.equal(200);
        });
        cy.url().should('match', creditLimitHistoryPageUrlPattern);
      });
    });

    describe('with existing value', () => {
      beforeEach(() => {
        cy.authenticatedRequest({
          method: 'POST',
          url: '/api/credit-limit-histories',
          body: {
            ...creditLimitHistorySample,
            creditLimit: creditLimit,
          },
        }).then(({ body }) => {
          creditLimitHistory = body;

          cy.intercept(
            {
              method: 'GET',
              url: '/api/credit-limit-histories+(?*|)',
              times: 1,
            },
            {
              statusCode: 200,
              body: [creditLimitHistory],
            }
          ).as('entitiesRequestInternal');
        });

        cy.visit(creditLimitHistoryPageUrl);

        cy.wait('@entitiesRequestInternal');
      });

      it('detail button click should load details CreditLimitHistory page', () => {
        cy.get(entityDetailsButtonSelector).first().click();
        cy.getEntityDetailsHeading('creditLimitHistory');
        cy.get(entityDetailsBackButtonSelector).click();
        cy.wait('@entitiesRequest').then(({ response }) => {
          expect(response!.statusCode).to.equal(200);
        });
        cy.url().should('match', creditLimitHistoryPageUrlPattern);
      });

      it('edit button click should load edit CreditLimitHistory page', () => {
        cy.get(entityEditButtonSelector).first().click();
        cy.getEntityCreateUpdateHeading('CreditLimitHistory');
        cy.get(entityCreateSaveButtonSelector).should('exist');
        cy.get(entityCreateCancelButtonSelector).click();
        cy.wait('@entitiesRequest').then(({ response }) => {
          expect(response!.statusCode).to.equal(200);
        });
        cy.url().should('match', creditLimitHistoryPageUrlPattern);
      });

      it('last delete button click should delete instance of CreditLimitHistory', () => {
        cy.get(entityDeleteButtonSelector).last().click();
        cy.getEntityDeleteDialogHeading('creditLimitHistory').should('exist');
        cy.get(entityConfirmDeleteButtonSelector).click();
        cy.wait('@deleteEntityRequest').then(({ response }) => {
          expect(response!.statusCode).to.equal(204);
        });
        cy.wait('@entitiesRequest').then(({ response }) => {
          expect(response!.statusCode).to.equal(200);
        });
        cy.url().should('match', creditLimitHistoryPageUrlPattern);

        creditLimitHistory = undefined;
      });
    });
  });

  describe('new CreditLimitHistory page', () => {
    beforeEach(() => {
      cy.visit(`${creditLimitHistoryPageUrl}`);
      cy.get(entityCreateButtonSelector).click();
      cy.getEntityCreateUpdateHeading('CreditLimitHistory');
    });

    it('should create an instance of CreditLimitHistory', () => {
      cy.get(`[data-cy="startDate"]`).type('2022-06-21T02:47').should('have.value', '2022-06-21T02:47');

      cy.get(`[data-cy="endDate"]`).type('2022-06-21T04:18').should('have.value', '2022-06-21T04:18');

      cy.get(`[data-cy="creditLimit"]`).select(1);

      cy.get(entityCreateSaveButtonSelector).click();

      cy.wait('@postEntityRequest').then(({ response }) => {
        expect(response!.statusCode).to.equal(201);
        creditLimitHistory = response!.body;
      });
      cy.wait('@entitiesRequest').then(({ response }) => {
        expect(response!.statusCode).to.equal(200);
      });
      cy.url().should('match', creditLimitHistoryPageUrlPattern);
    });
  });
});
