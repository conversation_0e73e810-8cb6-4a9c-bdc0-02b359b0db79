import { entityItemSelector } from '../../support/commands';
import {
  entityTableSelector,
  entityDetailsButtonSelector,
  entityDetailsBackButtonSelector,
  entityCreateButtonSelector,
  entityCreateSaveButtonSelector,
  entityCreateCancelButtonSelector,
  entityEditButtonSelector,
  entityDeleteButtonSelector,
  entityConfirmDeleteButtonSelector,
} from '../../support/entity';

describe('Buyer e2e test', () => {
  const buyerPageUrl = '/buyer';
  const buyerPageUrlPattern = new RegExp('/buyer(\\?.*)?$');
  const username = Cypress.env('E2E_USERNAME') ?? 'user';
  const password = Cypress.env('E2E_PASSWORD') ?? 'user';
  const buyerSample = {
    uuid: '659b18b3-bf12-4a28-8c8c-10539ce5cd7d',
    code: 'Forward bleeding-edge Djibouti',
    type: 'SIRET',
    number: 'invoice',
  };

  let buyer: any;

  beforeEach(() => {
    cy.login(username, password);
  });

  beforeEach(() => {
    cy.intercept('GET', '/api/buyers+(?*|)').as('entitiesRequest');
    cy.intercept('POST', '/api/buyers').as('postEntityRequest');
    cy.intercept('DELETE', '/api/buyers/*').as('deleteEntityRequest');
  });

  afterEach(() => {
    if (buyer) {
      cy.authenticatedRequest({
        method: 'DELETE',
        url: `/api/buyers/${buyer.id}`,
      }).then(() => {
        buyer = undefined;
      });
    }
  });

  it('Buyers menu should load Buyers page', () => {
    cy.visit('/');
    cy.clickOnEntityMenuItem('buyer');
    cy.wait('@entitiesRequest').then(({ response }) => {
      if (response!.body.length === 0) {
        cy.get(entityTableSelector).should('not.exist');
      } else {
        cy.get(entityTableSelector).should('exist');
      }
    });
    cy.getEntityHeading('Buyer').should('exist');
    cy.url().should('match', buyerPageUrlPattern);
  });

  describe('Buyer page', () => {
    describe('create button click', () => {
      beforeEach(() => {
        cy.visit(buyerPageUrl);
        cy.wait('@entitiesRequest');
      });

      it('should load create Buyer page', () => {
        cy.get(entityCreateButtonSelector).click();
        cy.url().should('match', new RegExp('/buyer/new$'));
        cy.getEntityCreateUpdateHeading('Buyer');
        cy.get(entityCreateSaveButtonSelector).should('exist');
        cy.get(entityCreateCancelButtonSelector).click();
        cy.wait('@entitiesRequest').then(({ response }) => {
          expect(response!.statusCode).to.equal(200);
        });
        cy.url().should('match', buyerPageUrlPattern);
      });
    });

    describe('with existing value', () => {
      beforeEach(() => {
        cy.authenticatedRequest({
          method: 'POST',
          url: '/api/buyers',
          body: buyerSample,
        }).then(({ body }) => {
          buyer = body;

          cy.intercept(
            {
              method: 'GET',
              url: '/api/buyers+(?*|)',
              times: 1,
            },
            {
              statusCode: 200,
              headers: {
                link: '<http://localhost/api/buyers?page=0&size=20>; rel="last",<http://localhost/api/buyers?page=0&size=20>; rel="first"',
              },
              body: [buyer],
            }
          ).as('entitiesRequestInternal');
        });

        cy.visit(buyerPageUrl);

        cy.wait('@entitiesRequestInternal');
      });

      it('detail button click should load details Buyer page', () => {
        cy.get(entityDetailsButtonSelector).first().click();
        cy.getEntityDetailsHeading('buyer');
        cy.get(entityDetailsBackButtonSelector).click();
        cy.wait('@entitiesRequest').then(({ response }) => {
          expect(response!.statusCode).to.equal(200);
        });
        cy.url().should('match', buyerPageUrlPattern);
      });

      it('edit button click should load edit Buyer page', () => {
        cy.get(entityEditButtonSelector).first().click();
        cy.getEntityCreateUpdateHeading('Buyer');
        cy.get(entityCreateSaveButtonSelector).should('exist');
        cy.get(entityCreateCancelButtonSelector).click();
        cy.wait('@entitiesRequest').then(({ response }) => {
          expect(response!.statusCode).to.equal(200);
        });
        cy.url().should('match', buyerPageUrlPattern);
      });

      it('last delete button click should delete instance of Buyer', () => {
        cy.get(entityDeleteButtonSelector).last().click();
        cy.getEntityDeleteDialogHeading('buyer').should('exist');
        cy.get(entityConfirmDeleteButtonSelector).click();
        cy.wait('@deleteEntityRequest').then(({ response }) => {
          expect(response!.statusCode).to.equal(204);
        });
        cy.wait('@entitiesRequest').then(({ response }) => {
          expect(response!.statusCode).to.equal(200);
        });
        cy.url().should('match', buyerPageUrlPattern);

        buyer = undefined;
      });
    });
  });

  describe('new Buyer page', () => {
    beforeEach(() => {
      cy.visit(`${buyerPageUrl}`);
      cy.get(entityCreateButtonSelector).click();
      cy.getEntityCreateUpdateHeading('Buyer');
    });

    it('should create an instance of Buyer', () => {
      cy.get(`[data-cy="uuid"]`)
        .type('125ca587-b4e4-46e4-a57e-0899ac5b8433')
        .invoke('val')
        .should('match', new RegExp('125ca587-b4e4-46e4-a57e-0899ac5b8433'));

      cy.get(`[data-cy="code"]`).type('Money').should('have.value', 'Money');

      cy.get(`[data-cy="type"]`).select('VAT');

      cy.get(`[data-cy="number"]`).type('Chicken Rustic').should('have.value', 'Chicken Rustic');

      cy.get(`[data-cy="name"]`).type('facilitate payment Freeway').should('have.value', 'facilitate payment Freeway');

      cy.get(entityCreateSaveButtonSelector).click();

      cy.wait('@postEntityRequest').then(({ response }) => {
        expect(response!.statusCode).to.equal(201);
        buyer = response!.body;
      });
      cy.wait('@entitiesRequest').then(({ response }) => {
        expect(response!.statusCode).to.equal(200);
      });
      cy.url().should('match', buyerPageUrlPattern);
    });
  });
});
