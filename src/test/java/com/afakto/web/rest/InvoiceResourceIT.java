package com.afakto.web.rest;

import static com.afakto.security.AuthoritiesConstants.WRITER;
import static com.afakto.web.rest.TestUtil.sameNumber;
import static org.assertj.core.api.Assertions.assertThat;
import static org.hamcrest.Matchers.hasItem;
import static org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.csrf;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.delete;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.patch;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.put;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.UUID;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.cache.CacheManager;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;

import com.afakto.IntegrationTest;
import com.afakto.domain.Invoice;
import com.afakto.domain.enumeration.InvoiceType;
import com.afakto.repository.InvoiceRepository;
import com.afakto.repository.UserRepository;
import com.afakto.service.dto.InvoiceDTO;
import com.afakto.service.mapper.InvoiceMapper;

import jakarta.persistence.EntityManager;

/**
 * Integration tests for the {@link InvoiceResource} REST controller.
 */
@AutoConfigureMockMvc
@ExtendWith(MockitoExtension.class)
@IntegrationTest
@TestPropertySource(properties = {
        "spring.aop.auto=false"
})
@WithMockUser(authorities = { WRITER })
class InvoiceResourceIT {

    private static final String DEFAULT_INVOICE_NUMBER = "AAAAAAAAAA";
    private static final String UPDATED_INVOICE_NUMBER = "BBBBBBBBBB";

    private static final BigDecimal DEFAULT_AMOUNT = new BigDecimal(1);
    private static final BigDecimal UPDATED_AMOUNT = new BigDecimal(2);

    private static final BigDecimal DEFAULT_BALANCE = new BigDecimal(2000);
    private static final BigDecimal UPDATED_BALANCE = new BigDecimal(3500);

    private static final String DEFAULT_CURRENCY = "EUR";
    private static final String UPDATED_CURRENCY = "USD";

    private static final LocalDate DEFAULT_DATE = LocalDate.of(1, 1, 1);
    private static final LocalDate UPDATED_DATE = LocalDate.now();

    private static final LocalDate DEFAULT_DUE_DATE = LocalDate.of(1, 1, 1);
    private static final LocalDate UPDATED_DUE_DATE = LocalDate.now();

    private static final InvoiceType DEFAULT_TYPE = InvoiceType.INVOICE;
    private static final InvoiceType UPDATED_TYPE = InvoiceType.CREDIT_NOTE;

    private static final Boolean DEFAULT_EXCLUDED = false;
    private static final Boolean UPDATED_EXCLUDED = true;

    private static final String UPDATED_EXCLUSION_REASON = "BBBBBBBBBB";

    private static final String ENTITY_API_URL = "/api/invoices";
    private static final String ENTITY_API_URL_ID = ENTITY_API_URL + "/{id}";

    @Autowired
    private CacheManager cacheManager;

    @Autowired
    private InvoiceRepository invoiceRepository;

    @Autowired
    private InvoiceMapper invoiceMapper;

    @Autowired
    private EntityManager em;

    @Autowired
    private MockMvc restInvoiceMockMvc;

    private Invoice invoice;

    /**
     * Create an entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static Invoice createEntity(EntityManager em) {
        var buyer = BuyerResourceIT.createEntity(em);
        em.persist(buyer);

        return new Invoice()
                .setInvoiceNumber(DEFAULT_INVOICE_NUMBER)
                .setAmount(DEFAULT_AMOUNT)
                .setBalance(DEFAULT_BALANCE)
                .setCurrency(DEFAULT_CURRENCY)
                .setDate(DEFAULT_DATE)
                .setDueDate(DEFAULT_DUE_DATE)
                .setType(DEFAULT_TYPE)
                .setExcluded(DEFAULT_EXCLUDED)
                .setBuyer(buyer);
    }

    /**
     * Create an updated entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static Invoice createUpdatedEntity(EntityManager em) {
        return new Invoice()
                .setInvoiceNumber(UPDATED_INVOICE_NUMBER)
                .setAmount(UPDATED_AMOUNT)
                .setBalance(UPDATED_BALANCE)
                .setCurrency(UPDATED_CURRENCY)
                .setDate(UPDATED_DATE)
                .setDueDate(UPDATED_DUE_DATE)
                .setType(UPDATED_TYPE)
                .setExcluded(UPDATED_EXCLUDED)
                .setExclusionReason(UPDATED_EXCLUSION_REASON);
    }

    @BeforeEach
    void initTest() {
        invoice = createEntity(em);
        cacheManager.getCache(UserRepository.USERS_BY_LOGIN_CACHE).clear();
    }

    @Test
    @Transactional
    void createInvoice() throws Exception {
        int databaseSizeBeforeCreate = invoiceRepository.findAll().size();
        // Create the Invoice
        InvoiceDTO invoiceDTO = invoiceMapper.toDto(invoice);

        restInvoiceMockMvc
                .perform(
                        post(ENTITY_API_URL)
                                .with(csrf())
                                .contentType(MediaType.APPLICATION_JSON)
                                .content(TestUtil.convertObjectToJsonBytes(invoiceDTO)))
                .andExpect(status().isCreated());

        // Validate the Invoice in the database
        List<Invoice> invoiceList = invoiceRepository.findAll();
        assertThat(invoiceList).hasSize(databaseSizeBeforeCreate + 1);
        Invoice testInvoice = invoiceList.get(invoiceList.size() - 1);
        assertThat(testInvoice.getInvoiceNumber()).isEqualTo(DEFAULT_INVOICE_NUMBER);
        assertThat(testInvoice.getAmount()).isEqualByComparingTo(DEFAULT_AMOUNT);
        assertThat(testInvoice.getBalance()).isEqualByComparingTo(DEFAULT_BALANCE);
        assertThat(testInvoice.getDate()).isEqualTo(DEFAULT_DATE);
        assertThat(testInvoice.getDueDate()).isEqualTo(DEFAULT_DUE_DATE);
        assertThat(testInvoice.getType()).isEqualTo(DEFAULT_TYPE);
        assertThat(testInvoice.isExcluded()).isEqualTo(DEFAULT_EXCLUDED);
        assertThat(testInvoice.getExclusionReason()).isNull();
    }

    @Test
    @Transactional
    void createInvoiceWithExistingId() throws Exception {
        // Create the Invoice with an existing ID
        invoice.setId(UUID.randomUUID());
        InvoiceDTO invoiceDTO = invoiceMapper.toDto(invoice);

        int databaseSizeBeforeCreate = invoiceRepository.findAll().size();

        // An entity with an existing ID cannot be created, so this API call must fail
        restInvoiceMockMvc
                .perform(
                        post(ENTITY_API_URL)
                                .with(csrf())
                                .contentType(MediaType.APPLICATION_JSON)
                                .content(TestUtil.convertObjectToJsonBytes(invoiceDTO)))
                .andExpect(status().isBadRequest());

        // Validate the Invoice in the database
        List<Invoice> invoiceList = invoiceRepository.findAll();
        assertThat(invoiceList).hasSize(databaseSizeBeforeCreate);
    }

    @Test
    @Transactional
    void getAllInvoices() throws Exception {
        // Initialize the database
        invoiceRepository.saveAndFlush(invoice);

        // Get all the invoiceList
        restInvoiceMockMvc
                .perform(get(ENTITY_API_URL + "?sort=id,desc"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
                .andExpect(jsonPath("$.[*].id").value(hasItem(invoice.getId().toString())))
                .andExpect(jsonPath("$.[*].invoiceNumber").value(hasItem(DEFAULT_INVOICE_NUMBER)))
                .andExpect(jsonPath("$.[*].amount").value(hasItem(sameNumber(DEFAULT_AMOUNT))))
                .andExpect(jsonPath("$.[*].balance").value(hasItem(sameNumber(DEFAULT_BALANCE))))
                .andExpect(jsonPath("$.[*].date").value(hasItem(DEFAULT_DATE.toString())))
                .andExpect(jsonPath("$.[*].dueDate").value(hasItem(DEFAULT_DUE_DATE.toString())))
                .andExpect(jsonPath("$.[*].type").value(hasItem(DEFAULT_TYPE.toString())));
    }

    @Test
    @Transactional
    void getInvoice() throws Exception {
        // Initialize the database
        invoiceRepository.saveAndFlush(invoice);

        // Get the invoice
        restInvoiceMockMvc
                .perform(get(ENTITY_API_URL_ID, invoice.getId()))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
                .andExpect(jsonPath("$.id").value(invoice.getId().toString()))
                .andExpect(jsonPath("$.invoiceNumber").value(DEFAULT_INVOICE_NUMBER))
                .andExpect(jsonPath("$.amount").value(sameNumber(DEFAULT_AMOUNT)))
                .andExpect(jsonPath("$.balance").value(sameNumber(DEFAULT_BALANCE)))
                .andExpect(jsonPath("$.date").value(DEFAULT_DATE.toString()))
                .andExpect(jsonPath("$.dueDate").value(DEFAULT_DUE_DATE.toString()))
                .andExpect(jsonPath("$.type").value(DEFAULT_TYPE.toString()));
    }

    @Test
    @Transactional
    void getNonExistingInvoice() throws Exception {
        // Get the invoice
        restInvoiceMockMvc.perform(get(ENTITY_API_URL_ID, UUID.randomUUID())).andExpect(status().isNotFound());
    }

    @Test
    @Transactional
    void putNewInvoice() throws Exception {
        // Initialize the database
        invoiceRepository.saveAndFlush(invoice);

        int databaseSizeBeforeUpdate = invoiceRepository.findAll().size();

        // Update the invoice
        Invoice updatedInvoice = invoiceRepository.findById(invoice.getId()).orElseThrow();
        // Disconnect from session so that the updates on updatedInvoice are not
        // directly saved in db
        em.detach(updatedInvoice);
        updatedInvoice
                .setInvoiceNumber(UPDATED_INVOICE_NUMBER)
                .setAmount(UPDATED_AMOUNT)
                .setBalance(UPDATED_BALANCE)
                .setDate(UPDATED_DATE)
                .setDueDate(UPDATED_DUE_DATE)
                .setType(UPDATED_TYPE)
                .setExcluded(UPDATED_EXCLUDED)
                .setExclusionReason(UPDATED_EXCLUSION_REASON);
        InvoiceDTO invoiceDTO = invoiceMapper.toDto(updatedInvoice);

        restInvoiceMockMvc
                .perform(
                        put(ENTITY_API_URL_ID, invoiceDTO.getId())
                                .with(csrf())
                                .contentType(MediaType.APPLICATION_JSON)
                                .content(TestUtil.convertObjectToJsonBytes(invoiceDTO)))
                .andExpect(status().isOk());

        // Validate the Invoice in the database
        List<Invoice> invoiceList = invoiceRepository.findAll();
        assertThat(invoiceList).hasSize(databaseSizeBeforeUpdate);
        Invoice testInvoice = invoiceList.get(invoiceList.size() - 1);
        assertThat(testInvoice.getInvoiceNumber()).isEqualTo(UPDATED_INVOICE_NUMBER);
        assertThat(testInvoice.getAmount()).isEqualByComparingTo(UPDATED_AMOUNT);
        assertThat(testInvoice.getBalance()).isEqualByComparingTo(UPDATED_BALANCE);
        assertThat(testInvoice.getDate()).isEqualTo(UPDATED_DATE);
        assertThat(testInvoice.getDueDate()).isEqualTo(UPDATED_DUE_DATE);
        assertThat(testInvoice.getType()).isEqualTo(UPDATED_TYPE);
        assertThat(testInvoice.isExcluded()).isEqualTo(UPDATED_EXCLUDED);
        assertThat(testInvoice.getExclusionReason()).isEqualTo(UPDATED_EXCLUSION_REASON);
    }

    @Test
    @Transactional
    void putNonExistingInvoice() throws Exception {
        int databaseSizeBeforeUpdate = invoiceRepository.findAll().size();
        invoice.setId(UUID.randomUUID());

        // Create the Invoice
        InvoiceDTO invoiceDTO = invoiceMapper.toDto(invoice);

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restInvoiceMockMvc
                .perform(
                        put(ENTITY_API_URL_ID, invoiceDTO.getId())
                                .with(csrf())
                                .contentType(MediaType.APPLICATION_JSON)
                                .content(TestUtil.convertObjectToJsonBytes(invoiceDTO)))
                .andExpect(status().isBadRequest());

        // Validate the Invoice in the database
        List<Invoice> invoiceList = invoiceRepository.findAll();
        assertThat(invoiceList).hasSize(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithIdMismatchInvoice() throws Exception {
        int databaseSizeBeforeUpdate = invoiceRepository.findAll().size();
        invoice.setId(UUID.randomUUID());

        // Create the Invoice
        InvoiceDTO invoiceDTO = invoiceMapper.toDto(invoice);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restInvoiceMockMvc
                .perform(
                        put(ENTITY_API_URL_ID, UUID.randomUUID())
                                .with(csrf())
                                .contentType(MediaType.APPLICATION_JSON)
                                .content(TestUtil.convertObjectToJsonBytes(invoiceDTO)))
                .andExpect(status().isBadRequest());

        // Validate the Invoice in the database
        List<Invoice> invoiceList = invoiceRepository.findAll();
        assertThat(invoiceList).hasSize(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithMissingIdPathParamInvoice() throws Exception {
        int databaseSizeBeforeUpdate = invoiceRepository.findAll().size();
        invoice.setId(UUID.randomUUID());

        // Create the Invoice
        InvoiceDTO invoiceDTO = invoiceMapper.toDto(invoice);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restInvoiceMockMvc
                .perform(
                        put(ENTITY_API_URL)
                                .with(csrf())
                                .contentType(MediaType.APPLICATION_JSON)
                                .content(TestUtil.convertObjectToJsonBytes(invoiceDTO)))
                .andExpect(status().isMethodNotAllowed());

        // Validate the Invoice in the database
        List<Invoice> invoiceList = invoiceRepository.findAll();
        assertThat(invoiceList).hasSize(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void partialUpdateInvoiceWithPatch() throws Exception {
        // Initialize the database
        invoiceRepository.saveAndFlush(invoice);

        int databaseSizeBeforeUpdate = invoiceRepository.findAll().size();

        // Update the invoice using partial update
        Invoice partialUpdatedInvoice = new Invoice();
        partialUpdatedInvoice.setId(invoice.getId());

        partialUpdatedInvoice.setBalance(UPDATED_BALANCE).setDate(UPDATED_DATE).setDueDate(UPDATED_DUE_DATE);

        restInvoiceMockMvc
                .perform(
                        patch(ENTITY_API_URL_ID, partialUpdatedInvoice.getId())
                                .with(csrf())
                                .contentType("application/merge-patch+json")
                                .content(TestUtil.convertObjectToJsonBytes(partialUpdatedInvoice)))
                .andExpect(status().isOk());

        // Validate the Invoice in the database
        List<Invoice> invoiceList = invoiceRepository.findAll();
        assertThat(invoiceList).hasSize(databaseSizeBeforeUpdate);
        Invoice testInvoice = invoiceList.get(invoiceList.size() - 1);
        assertThat(testInvoice.getInvoiceNumber()).isEqualTo(DEFAULT_INVOICE_NUMBER);
        assertThat(testInvoice.getAmount()).isEqualByComparingTo(DEFAULT_AMOUNT);
        assertThat(testInvoice.getBalance()).isEqualByComparingTo(UPDATED_BALANCE);
        assertThat(testInvoice.getDate()).isEqualTo(UPDATED_DATE);
        assertThat(testInvoice.getDueDate()).isEqualTo(UPDATED_DUE_DATE);
        assertThat(testInvoice.getType()).isEqualTo(DEFAULT_TYPE);
    }

    @Test
    @Transactional
    void fullUpdateInvoiceWithPatch() throws Exception {
        // Initialize the database
        invoiceRepository.saveAndFlush(invoice);

        int databaseSizeBeforeUpdate = invoiceRepository.findAll().size();

        // Update the invoice using partial update
        Invoice partialUpdatedInvoice = new Invoice();
        partialUpdatedInvoice.setId(invoice.getId());

        partialUpdatedInvoice
                .setInvoiceNumber(UPDATED_INVOICE_NUMBER)
                .setAmount(UPDATED_AMOUNT)
                .setBalance(UPDATED_BALANCE)
                .setDate(UPDATED_DATE)
                .setDueDate(UPDATED_DUE_DATE)
                .setType(UPDATED_TYPE);

        restInvoiceMockMvc
                .perform(
                        patch(ENTITY_API_URL_ID, partialUpdatedInvoice.getId())
                                .with(csrf())
                                .contentType("application/merge-patch+json")
                                .content(TestUtil.convertObjectToJsonBytes(partialUpdatedInvoice)))
                .andExpect(status().isOk());

        // Validate the Invoice in the database
        List<Invoice> invoiceList = invoiceRepository.findAll();
        assertThat(invoiceList).hasSize(databaseSizeBeforeUpdate);
        Invoice testInvoice = invoiceList.get(invoiceList.size() - 1);
        assertThat(testInvoice.getInvoiceNumber()).isEqualTo(UPDATED_INVOICE_NUMBER);
        assertThat(testInvoice.getAmount()).isEqualByComparingTo(UPDATED_AMOUNT);
        assertThat(testInvoice.getBalance()).isEqualByComparingTo(UPDATED_BALANCE);
        assertThat(testInvoice.getDate()).isEqualTo(UPDATED_DATE);
        assertThat(testInvoice.getDueDate()).isEqualTo(UPDATED_DUE_DATE);
        assertThat(testInvoice.getType()).isEqualTo(UPDATED_TYPE);
    }

    @Test
    @Transactional
    void patchNonExistingInvoice() throws Exception {
        int databaseSizeBeforeUpdate = invoiceRepository.findAll().size();
        invoice.setId(UUID.randomUUID());

        // Create the Invoice
        InvoiceDTO invoiceDTO = invoiceMapper.toDto(invoice);

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restInvoiceMockMvc
                .perform(
                        patch(ENTITY_API_URL_ID, invoiceDTO.getId())
                                .with(csrf())
                                .contentType("application/merge-patch+json")
                                .content(TestUtil.convertObjectToJsonBytes(invoiceDTO)))
                .andExpect(status().isBadRequest());

        // Validate the Invoice in the database
        List<Invoice> invoiceList = invoiceRepository.findAll();
        assertThat(invoiceList).hasSize(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithIdMismatchInvoice() throws Exception {
        int databaseSizeBeforeUpdate = invoiceRepository.findAll().size();
        invoice.setId(UUID.randomUUID());

        // Create the Invoice
        InvoiceDTO invoiceDTO = invoiceMapper.toDto(invoice);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restInvoiceMockMvc
                .perform(
                        patch(ENTITY_API_URL_ID, UUID.randomUUID())
                                .with(csrf())
                                .contentType("application/merge-patch+json")
                                .content(TestUtil.convertObjectToJsonBytes(invoiceDTO)))
                .andExpect(status().isBadRequest());

        // Validate the Invoice in the database
        List<Invoice> invoiceList = invoiceRepository.findAll();
        assertThat(invoiceList).hasSize(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithMissingIdPathParamInvoice() throws Exception {
        int databaseSizeBeforeUpdate = invoiceRepository.findAll().size();
        invoice.setId(UUID.randomUUID());

        // Create the Invoice
        InvoiceDTO invoiceDTO = invoiceMapper.toDto(invoice);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restInvoiceMockMvc
                .perform(
                        patch(ENTITY_API_URL)
                                .with(csrf())
                                .contentType("application/merge-patch+json")
                                .content(TestUtil.convertObjectToJsonBytes(invoiceDTO)))
                .andExpect(status().isMethodNotAllowed());

        // Validate the Invoice in the database
        List<Invoice> invoiceList = invoiceRepository.findAll();
        assertThat(invoiceList).hasSize(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void deleteInvoice() throws Exception {
        // Initialize the database
        invoiceRepository.saveAndFlush(invoice);

        int databaseSizeBeforeDelete = invoiceRepository.findAll().size();

        // Delete the invoice
        restInvoiceMockMvc
                .perform(delete(ENTITY_API_URL_ID, invoice.getId()).with(csrf()).accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isNoContent());

        // Validate the database contains one less item
        List<Invoice> invoiceList = invoiceRepository.findAll();
        assertThat(invoiceList).hasSize(databaseSizeBeforeDelete - 1);
    }
}
