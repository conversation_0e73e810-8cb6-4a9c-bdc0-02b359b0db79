package com.afakto.domain;

import java.util.UUID;

public class InvoiceTestSamples {

    public static Invoice getInvoiceSample1() {
        var result = new Invoice();
        result.setId(UUID.fromString("23d8dc04-a48b-45d9-a01d-4b728f0ad4aa"));
        return result
                .setInvoiceNumber("invoiceNumber1")
                .setCurrency("currency1");
    }

    public static Invoice getInvoiceSample2() {
        var result = new Invoice();
        result.setId(UUID.fromString("ad79f240-3727-46c3-b89f-2cf6ebd74367"));
        return result.setInvoiceNumber("invoiceNumber2")
                .setCurrency("currency2")
                .setExcluded(true)
                .setExclusionReason("exclusionReason2");
    }

    public static Invoice getInvoiceRandomSampleGenerator() {
        var result = new Invoice();
        result.setId(UUID.randomUUID());
        return result.setInvoiceNumber(UUID.randomUUID().toString()).setCurrency(UUID.randomUUID().toString());
    }
}
