package com.afakto.service.validation;

import static org.assertj.core.api.Assertions.assertThat;

import java.math.BigDecimal;
import java.time.LocalDate;

import org.junit.jupiter.api.Test;

import com.afakto.domain.enumeration.InvoiceType;
import com.afakto.service.dto.BuyerDTO;
import com.afakto.service.dto.InvoiceDTO;

/**
 * Simple unit test for InvoiceDTO validation annotations.
 * Tests basic @NotNull validations without requiring Spring context.
 */
class InvoiceDTOValidationTest {

    @Test
    void createValidInvoiceDTO_shouldWork() {
        // Given
        BuyerDTO buyerDTO = new BuyerDTO();
        buyerDTO.setCode("TEST001");
        buyerDTO.setName("Test Buyer");

        InvoiceDTO invoiceDTO = new InvoiceDTO();
        invoiceDTO.setBuyer(buyerDTO);
        invoiceDTO.setType(InvoiceType.INVOICE);
        invoiceDTO.setInvoiceNumber("INV-2024-001");
        invoiceDTO.setDate(LocalDate.of(2024, 1, 1));
        invoiceDTO.setDueDate(LocalDate.of(2024, 1, 31));
        invoiceDTO.setCurrency("EUR");
        invoiceDTO.setAmount(BigDecimal.valueOf(100));
        invoiceDTO.setBalance(BigDecimal.valueOf(50));
        invoiceDTO.setUnderFactor(false);

        // When/Then - Should not throw any exceptions
        assertThat(invoiceDTO.getInvoiceNumber()).isEqualTo("INV-2024-001");
        assertThat(invoiceDTO.getAmount()).isEqualTo(BigDecimal.valueOf(100));
        assertThat(invoiceDTO.getBalance()).isEqualTo(BigDecimal.valueOf(50));
        assertThat(invoiceDTO.getCurrency()).isEqualTo("EUR");
        assertThat(invoiceDTO.getType()).isEqualTo(InvoiceType.INVOICE);
        assertThat(invoiceDTO.getBuyer()).isNotNull();
        assertThat(invoiceDTO.getBuyer().getCode()).isEqualTo("TEST001");
    }

    @Test
    void invoiceDTO_hasRequiredAnnotations() {
        // This test verifies that the DTO has the expected validation annotations
        // The actual validation would be tested in integration tests with Spring context

        InvoiceDTO invoiceDTO = new InvoiceDTO();

        // Test that we can set and get all required fields
        invoiceDTO.setInvoiceNumber("TEST-001");
        invoiceDTO.setAmount(BigDecimal.valueOf(100));
        invoiceDTO.setBalance(BigDecimal.valueOf(50));
        invoiceDTO.setCurrency("EUR");
        invoiceDTO.setType(InvoiceType.CREDIT_NOTE);
        invoiceDTO.setDate(LocalDate.now());
        invoiceDTO.setDueDate(LocalDate.now().plusDays(30));
        invoiceDTO.setExcluded(true);
        invoiceDTO.setExclusionReason("Test exclusion reason");
        invoiceDTO.setUnderFactor(true);

        assertThat(invoiceDTO.getInvoiceNumber()).isNotNull();
        assertThat(invoiceDTO.getAmount()).isNotNull();
        assertThat(invoiceDTO.getBalance()).isNotNull();
        assertThat(invoiceDTO.getCurrency()).isNotNull();
        assertThat(invoiceDTO.getType()).isNotNull();
        assertThat(invoiceDTO.getDate()).isNotNull();
        assertThat(invoiceDTO.getDueDate()).isNotNull();
    }
}
