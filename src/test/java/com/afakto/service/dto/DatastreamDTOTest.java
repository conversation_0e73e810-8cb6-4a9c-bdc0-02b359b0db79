package com.afakto.service.dto;

import static org.assertj.core.api.Assertions.assertThat;

import java.util.UUID;

import org.junit.jupiter.api.Test;

import com.afakto.web.rest.TestUtil;

class DatastreamDTOTest {

    @Test
    void dtoEqualsVerifier() throws Exception {
        TestUtil.equalsVerifier(DatastreamDTO.class);
        DatastreamDTO datastreamDTO1 = new DatastreamDTO();
        datastreamDTO1.setId(UUID.randomUUID());
        DatastreamDTO datastreamDTO2 = new DatastreamDTO();
        assertThat(datastreamDTO1).isNotEqualTo(datastreamDTO2);
        datastreamDTO2.setId(datastreamDTO1.getId());
        assertThat(datastreamDTO1).isEqualTo(datastreamDTO2);
        datastreamDTO2.setId(UUID.randomUUID());
        assertThat(datastreamDTO1).isNotEqualTo(datastreamDTO2);
        datastreamDTO1.setId(null);
        assertThat(datastreamDTO1).isNotEqualTo(datastreamDTO2);
    }
}
