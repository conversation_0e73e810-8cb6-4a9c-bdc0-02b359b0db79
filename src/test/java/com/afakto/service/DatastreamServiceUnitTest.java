package com.afakto.service;

import static org.assertj.core.api.Assertions.assertThat;

import java.io.ByteArrayInputStream;
import java.lang.reflect.Method;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;

import org.apache.commons.io.ByteOrderMark;
import org.apache.commons.io.input.BOMInputStream;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import com.afakto.batch.ImportJob;
import com.afakto.repository.DatastreamRepository;
import com.afakto.repository.OrganizationRepository;
import com.afakto.service.mapper.DatastreamMapper;

/**
 * Unit tests for DatastreamService helper methods.
 * These tests focus on individual methods without full service setup.
 */
@ExtendWith(MockitoExtension.class)
class DatastreamServiceUnitTest {

    @Mock
    private DatastreamRepository datastreamRepository;

    @Mock
    private DatastreamMapper datastreamMapper;

    @Mock
    private ImportJob importJob;

    @Mock
    private OrganizationRepository organizationRepository;

    private DatastreamService datastreamService;

    @BeforeEach
    void setUp() {
        datastreamService = new DatastreamService(
                datastreamMapper,
                datastreamRepository,
                importJob,
                organizationRepository);
    }

    @Test
    void testNormalizeLineEndings() throws Exception {
        // Test the private normalizeLineEndings method using reflection
        Method method = DatastreamService.class.getDeclaredMethod("normalizeLineEndings", String.class);
        method.setAccessible(true);

        // Test mixed line endings
        String input = "line1\r\nline2\rline3\nline4\r\n";
        String result = (String) method.invoke(datastreamService, input);

        assertThat(result)
                .isEqualTo("line1\nline2\nline3\nline4")
                .doesNotContain("\r");
    }

    @Test
    void testNormalizeLineEndingsEdgeCases() throws Exception {
        Method method = DatastreamService.class.getDeclaredMethod("normalizeLineEndings", String.class);
        method.setAccessible(true);

        // Test empty string
        String result1 = (String) method.invoke(datastreamService, "");
        assertThat(result1).isEmpty();

        // Test single line without newline
        String result2 = (String) method.invoke(datastreamService, "single line");
        assertThat(result2).isEqualTo("single line");

        // Test only newlines - String.lines() produces empty lines that get joined
        String result3 = (String) method.invoke(datastreamService, "\r\n\r\n");
        assertThat(result3).isEqualTo("\n");

        // Test multiple consecutive newlines - String.lines() preserves empty lines
        // between content
        String result4 = (String) method.invoke(datastreamService, "line1\n\n\nline2");
        assertThat(result4).isEqualTo("line1\n\n\nline2");

        // Test problematic \r\r\n pattern
        String result5 = (String) method.invoke(datastreamService, "line1\r\r\nline2\r\r\nline3");
        assertThat(result5).isEqualTo("line1\n\nline2\n\nline3");
    }

    @Test
    void testBOMDetection() throws Exception {
        // Test BOM detection directly using BOMInputStream (since createBOMInputStream
        // was removed)
        byte[] bomUtf8 = { (byte) 0xEF, (byte) 0xBB, (byte) 0xBF };
        String content = "test content";
        byte[] contentBytes = content.getBytes(StandardCharsets.UTF_8);
        byte[] fileBytes = new byte[bomUtf8.length + contentBytes.length];
        System.arraycopy(bomUtf8, 0, fileBytes, 0, bomUtf8.length);
        System.arraycopy(contentBytes, 0, fileBytes, bomUtf8.length, contentBytes.length);

        ByteArrayInputStream inputStream = new ByteArrayInputStream(fileBytes);
        BOMInputStream bomInputStream = BOMInputStream.builder()
                .setInputStream(inputStream)
                .setByteOrderMarks(ByteOrderMark.UTF_8)
                .get();

        assertThat(bomInputStream).isNotNull();
        assertThat(bomInputStream.hasBOM()).isTrue();
        assertThat(bomInputStream.getBOM()).isEqualTo(ByteOrderMark.UTF_8);
    }

    @Test
    void testDetectCharsetWithBOM() throws Exception {
        // Test charset detection with BOM using detectCharsetFromStream
        Method method = DatastreamService.class.getDeclaredMethod("detectCharsetFromStream", BOMInputStream.class);
        method.setAccessible(true);

        // UTF-8 BOM + content
        byte[] bomUtf8 = { (byte) 0xEF, (byte) 0xBB, (byte) 0xBF };
        String content = "Café,Résumé";
        byte[] contentBytes = content.getBytes(StandardCharsets.UTF_8);
        byte[] fileBytes = new byte[bomUtf8.length + contentBytes.length];
        System.arraycopy(bomUtf8, 0, fileBytes, 0, bomUtf8.length);
        System.arraycopy(contentBytes, 0, fileBytes, bomUtf8.length, contentBytes.length);

        ByteArrayInputStream inputStream = new ByteArrayInputStream(fileBytes);
        BOMInputStream bomInputStream = BOMInputStream.builder()
                .setInputStream(inputStream)
                .setByteOrderMarks(ByteOrderMark.UTF_8)
                .get();

        Charset detectedCharset = (Charset) method.invoke(datastreamService, bomInputStream);

        assertThat(detectedCharset).isEqualTo(StandardCharsets.UTF_8);
    }

    @Test
    void testDetectCharsetWithoutBOM() throws Exception {
        // Test charset detection without BOM (ICU4J fallback)
        Method method = DatastreamService.class.getDeclaredMethod("detectCharsetFromStream", BOMInputStream.class);
        method.setAccessible(true);

        // Plain UTF-8 content without BOM
        String content = "Simple ASCII content without accents";
        byte[] fileBytes = content.getBytes(StandardCharsets.UTF_8);

        ByteArrayInputStream inputStream = new ByteArrayInputStream(fileBytes);
        BOMInputStream bomInputStream = BOMInputStream.builder()
                .setInputStream(inputStream)
                .setByteOrderMarks(ByteOrderMark.UTF_8)
                .get();

        Charset detectedCharset = (Charset) method.invoke(datastreamService, bomInputStream);

        // Should detect some charset (ICU4J may detect various charsets for ASCII
        // content)
        assertThat(detectedCharset).isNotNull();
        assertThat(detectedCharset.name()).isNotEmpty();
    }

    @Test
    void testDetectCharsetEmptyFile() throws Exception {
        // Test charset detection with empty file
        Method method = DatastreamService.class.getDeclaredMethod("detectCharsetFromStream", BOMInputStream.class);
        method.setAccessible(true);

        ByteArrayInputStream inputStream = new ByteArrayInputStream(new byte[0]);
        BOMInputStream bomInputStream = BOMInputStream.builder()
                .setInputStream(inputStream)
                .setByteOrderMarks(ByteOrderMark.UTF_8)
                .get();

        Charset detectedCharset = (Charset) method.invoke(datastreamService, bomInputStream);

        // Should fall back to UTF-8
        assertThat(detectedCharset).isEqualTo(StandardCharsets.UTF_8);
    }

    @Test
    void testDetectCharsetWithICU4J() throws Exception {
        // Test ICU4J charset detection method
        Method method = DatastreamService.class.getDeclaredMethod("detectCharsetWithICU4J", byte[].class);
        method.setAccessible(true);

        // French content that should be detected as ISO-8859-1 or similar
        String content = "Café de la Paix\nRésumé professionnel\nNaïve Solutions";
        byte[] isoBytes = content.getBytes(StandardCharsets.ISO_8859_1);

        Charset detectedCharset = (Charset) method.invoke(datastreamService, isoBytes);

        assertThat(detectedCharset).isNotNull();
        // Should detect some charset (ISO-8859-1, windows-1252, or fall back to UTF-8)
        assertThat(detectedCharset.name()).isNotEmpty();
    }

    @Test
    void testDetectCharsetWithICU4JEmptyBytes() throws Exception {
        // Test ICU4J with empty byte array
        Method method = DatastreamService.class.getDeclaredMethod("detectCharsetWithICU4J", byte[].class);
        method.setAccessible(true);

        Charset detectedCharset = (Charset) method.invoke(datastreamService, new byte[0]);

        // Should fall back to UTF-8
        assertThat(detectedCharset).isEqualTo(StandardCharsets.UTF_8);
    }
}
