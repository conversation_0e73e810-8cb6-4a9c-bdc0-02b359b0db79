package com.afakto.repository;

import java.util.Collection;
import java.util.Set;
import java.util.UUID;

import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.Repository;

import com.afakto.domain.BaseEntity;

public interface DashboardRepository extends Repository<BaseEntity, UUID> {
    // Get the factor debt, per currency
    @Query(value = """
            SELECT bt.currency,
                   0.0,
                   COALESCE(SUM(bt.amount), 0.0)
              FROM Bank_Transaction bt
             WHERE bt.category IN ('CURRENT_BALANCE', 'CURRENT_UNAVAILABLE')
               AND (bt.company_id, bt.currency, bt.date) IN
                   (SELECT bt2.company_id, bt2.currency, MAX(bt2.date)
                      FROM bank_transaction bt2
                     WHERE bt2.category IN ('CURRENT_BALANCE', 'CURRENT_UNAVAILABLE')
                       AND bt2.company_id IN :companies
                     GROUP BY bt2.company_id, bt2.currency)
             GROUP BY bt.currency
            """, nativeQuery = true)
    Collection<Object[]> getFactorDebt(Set<UUID> companies);

    // Get the historical factor debt, per currency and month, over six months
    // cash-in contracts imply summing only bank transfers
    @Query(value = """
            SELECT bt.currency,
                   DATE(DATE_TRUNC('month', bt.date)),
                   SUM(CASE WHEN bt.category = 'BANK_TRANSFER' THEN -bt.amount
                            WHEN NOT c.cash_in THEN -bt.amount
                            ELSE 0
                            END)
              FROM Bank_Transaction bt
              JOIN Contract c ON bt.company_id = c.company_id AND bt.currency = c.currency AND c.activation_date <= CURRENT_DATE
             WHERE bt.category IN ('BANK_TRANSFER', 'CLIENT_COLLECTION')
               AND bt.date >= DATE_TRUNC('month', CURRENT_DATE - INTERVAL '5 months')
               AND bt.company_id IN :companies
             GROUP BY bt.currency, DATE_TRUNC('month', bt.date)
             ORDER BY bt.currency, DATE_TRUNC('month', bt.date)
             """, nativeQuery = true)
    Collection<Object[]> getHistory(Set<UUID> companies);

    @Query(value = """
            SELECT i.currency,
                   0.0,
                   COALESCE(SUM(i.balance), 0.0)
              FROM Invoice i
              JOIN Buyer b ON i.buyer_id = b.id
              LEFT JOIN Credit_limit cl on b.credit_limit_id = cl.id and i.currency = cl.currency
              JOIN Contract c ON b.company_id = c.company_id AND i.currency = c.currency
              LEFT JOIN Credit_Insurance_Policy cip ON cip.id = c.credit_insurance_policy_id
             WHERE NOT i.is_under_factor
               AND i.due_date >= CURRENT_DATE
               AND NOT b.excluded
               AND NOT i.excluded
               AND b.company_id IN :companies
               AND c.activation_date <= CURRENT_DATE
               AND (c.non_guarantee_line > 0 OR COALESCE(cl.amount, cip.blind_cover_amount) > 0)
             GROUP BY i.currency
            """, nativeQuery = true)
    Collection<Object[]> availableToSell(Set<UUID> companies);

    /*
     * The differing date and created_date orders is due to BNP bank statements'
     * lines sorted and imported newest first
     */
    @Query(value = """
            select currency,
                   0.0,
                   coalesce(sum(balance), 0.0)
              from bank_transaction bt
             where created_date =
                   (select min(created_date)
                      from bank_transaction
                     where date = (select max(date)
                                     from bank_transaction
                                    where company_id = bt.company_id
                                      and currency = bt.currency
                                      and company_id in :companies)
                       and company_id = bt.company_id
                       and currency = bt.currency)
               and company_id in :companies
             group by currency
            """, nativeQuery = true)
    Collection<Object[]> potentialFunding(Set<UUID> companies);

    @Query(value = """
            select currency,
                   0.0,
                   coalesce(abs(sum(amount)), 0.0)
              from Bank_Transaction
             where category = 'CURRENT_GUARANTEE_FUND'
               and date >= current_date + interval '1 day' - interval '2 months'
               and (company_id, currency, date) in
                   (select company_id, currency, max(date)
                      from bank_transaction
                     where category = 'CURRENT_GUARANTEE_FUND'
                       and company_id in :companies
                     group by company_id, currency)
               and company_id in :companies
             group by currency
            """, nativeQuery = true)
    Collection<Object[]> guaranteeFund(Set<UUID> companies);

    @Query(value = """
            select currency,
                   0.0,
                   coalesce(sum(bff.amount_outstanding - bff.amount_secured), 0.0)
              from Buyer b
              join Buyer_From_Factor bff on bff.buyer_id = b.id
             where b.company_id in :companies
             group by currency
            """, nativeQuery = true)
    Collection<Object[]> amountAtRisk(Set<UUID> companies);

    @Query(value = """
            SELECT i.currency,
                   0.0,
                   COALESCE(SUM(i.balance), 0.0)
              FROM Invoice i
              JOIN Buyer b ON b.id = i.buyer_id
             WHERE b.company_id IN :companies
             GROUP BY i.currency
            """, nativeQuery = true)
    Collection<Object[]> amountOutstanding(Set<UUID> companies);

    @Query(value = """
            SELECT i.currency,
                   0.0,
                   COALESCE(SUM(iff.amount_unsecured), 0.0)
              FROM Invoice i
              JOIN Invoice_From_Factor iff ON iff.invoice_id = i.id
              JOIN Buyer b ON b.id = i.buyer_id
             WHERE b.company_id IN :companies
               AND i.is_under_factor
               AND i.balance != 0
               AND iff.amount_unsecured != 0
             GROUP BY i.currency
            """, nativeQuery = true)
    Collection<Object[]> amountUnsecured(Set<UUID> companies);

    @Query(value = """
            SELECT bt.currency,
                   0.0,
                   COALESCE(SUM(-bt.amount), 0)
              FROM Bank_Transaction bt
              JOIN Contract c ON bt.company_id = c.company_id AND bt.currency = c.currency AND c.activation_date <= CURRENT_DATE
              JOIN Company c2 ON bt.company_id = c2.id AND c2.fiscal_year_closing_month IS NOT NULL
             WHERE bt.category IN ('FACTORING_COMMISSION', 'FEES_NOT_GTIE_INVOICES', 'FUNDING_COMMISSION', 'OTHER_FEES')
               AND bt.date >= DATE_TRUNC('YEAR', CURRENT_DATE) +
                   INTERVAL '1 month' * (c2.fiscal_year_closing_month) -
                   CASE WHEN EXTRACT(MONTH FROM CURRENT_DATE) <= c2.fiscal_year_closing_month
                   THEN INTERVAL '1 YEAR'
                   ELSE INTERVAL '0 YEAR'
                   END
               AND bt.company_id IN :companies
             GROUP BY bt.currency
            """, nativeQuery = true)
    Collection<Object[]> amountFiscalYearFees(Set<UUID> companies);

    @Query(value = """
            SELECT b.currency,
                   0.0,
                   COALESCE(SUM(b.balance), 0.0)
              FROM Buyer b
              LEFT JOIN Credit_limit cl ON b.credit_limit_id = cl.id AND b.currency = cl.currency
              LEFT JOIN Contract c ON b.company_id = c.company_id AND b.currency = c.currency AND c.activation_date <= CURRENT_DATE
              LEFT JOIN Credit_Insurance_Policy cip ON cip.id = c.credit_insurance_policy_id
             WHERE (b.excluded
                    OR (COALESCE(c.non_guarantee_line, 0) = 0 AND COALESCE(cl.amount, cip.blind_cover_amount, 0) = 0))
               AND b.company_id IN :companies
               AND b.currency IS NOT NULL
             GROUP BY b.currency
            """, nativeQuery = true)
    Collection<Object[]> getExcludedBuyers(Set<UUID> companies);

    @Query(value = """
            SELECT c.currency,
                   (SELECT COALESCE(SUM(bt.amount), 0.0)
                      FROM Bank_Transaction bt
                     WHERE bt.category IN ('CURRENT_BALANCE', 'CURRENT_UNAVAILABLE')
                       AND c.currency = bt.currency
                       AND (bt.company_id, bt.date) IN
                           (SELECT bt2.company_id, MAX(bt2.date)
                              FROM bank_transaction bt2
                             WHERE bt2.category IN ('CURRENT_BALANCE', 'CURRENT_UNAVAILABLE')
                               AND c.currency = bt2.currency
                               AND bt2.company_id IN :companies
                             GROUP BY bt2.company_id))
                   AS currentOutstanding,
                   (SELECT COALESCE(SUM(i.balance), 0)
                      FROM Invoice i
                      JOIN Buyer b ON i.buyer_id = b.id
                      LEFT JOIN Credit_limit cl ON b.credit_limit_id = cl.id AND i.currency = cl.currency
                      JOIN Contract c2 ON b.company_id = c2.company_id AND i.currency = c2.currency
                      LEFT JOIN Credit_Insurance_Policy cip ON cip.id = c2.credit_insurance_policy_id
                     WHERE c.currency = i.currency
                       AND NOT i.is_under_factor
                       AND i.due_date >= CURRENT_DATE
                       AND NOT b.excluded
                       AND NOT i.excluded
                       AND b.company_id IN :companies
                       AND c2.activation_date <= CURRENT_DATE
                       AND (COALESCE(c2.non_guarantee_line, 0) > 0 OR COALESCE(cl.amount, cip.blind_cover_amount) > 0))
                   AS availableToSell,
                   (SELECT SUM(COALESCE(c2.guarantee_line, 0)) + SUM(COALESCE(c2.non_guarantee_line, 0))
                      FROM Contract c2
                     WHERE c.currency = c2.currency
                       AND c2.company_id IN :companies
                       AND c2.activation_date <= CURRENT_DATE)
                   -
                   (SELECT COALESCE(SUM(bt.amount), 0.0)
                      FROM Bank_Transaction bt
                     WHERE bt.category IN ('CURRENT_BALANCE', 'CURRENT_UNAVAILABLE')
                       AND c.currency = bt.currency
                       AND (bt.company_id, bt.date) IN
                           (SELECT bt2.company_id, MAX(bt2.date)
                              FROM bank_transaction bt2
                             WHERE bt2.category IN ('CURRENT_BALANCE', 'CURRENT_UNAVAILABLE')
                               AND c.currency = bt2.currency
                               AND bt2.company_id IN :companies
                             GROUP BY bt2.company_id))
                   -
                   (SELECT COALESCE(SUM(i.balance), 0)
                      FROM Invoice i
                      JOIN Buyer b ON i.buyer_id = b.id
                      LEFT JOIN Credit_limit cl ON b.credit_limit_id = cl.id AND i.currency = cl.currency
                      JOIN Contract c2 ON b.company_id = c2.company_id AND i.currency = c2.currency
                      LEFT JOIN Credit_Insurance_Policy cip ON cip.id = c2.credit_insurance_policy_id
                     WHERE c.currency = i.currency
                       AND NOT i.is_under_factor
                       AND i.due_date >= CURRENT_DATE
                       AND NOT b.excluded
                       AND NOT i.excluded
                       AND b.company_id IN :companies
                       AND c2.activation_date <= CURRENT_DATE
                       AND (COALESCE(c2.non_guarantee_line, 0) > 0 OR COALESCE(cl.amount, cip.blind_cover_amount) > 0))
                   AS remainingAuthorized
              FROM Contract c
             WHERE c.company_id IN :companies
               AND c.activation_date <= CURRENT_DATE
             GROUP BY c.currency
            """, nativeQuery = true)
    Collection<Object[]> contractSituation(Set<UUID> companies);

    /**
     * Get coverage data per currency - non-covered amounts and total balances for
     * frontend calculation
     */
    @Query(value = """
            SELECT b.currency,
                   COALESCE(SUM(GREATEST(b.balance - COALESCE(cl.amount, 0), 0)), 0) as non_covered_balance_amount,
                   COALESCE(SUM(b.balance), 0) as total_balance_amount
              FROM Buyer b
              LEFT JOIN Credit_limit cl ON b.credit_limit_id = cl.id AND b.currency = cl.currency
             WHERE NOT b.excluded
               AND b.company_id IN :companies
               AND b.currency IS NOT NULL
               AND b.balance IS NOT NULL
             GROUP BY b.currency
            """, nativeQuery = true)
    Collection<Object[]> getCoverage(Set<UUID> companies);

    /**
     * Get top buyers for each currency
     */
    @Query(value = """
            SELECT currency, buyer_id, buyer_name, amount
              FROM (SELECT DISTINCT currency FROM Contract ORDER BY currency) currencies
             CROSS JOIN LATERAL (
                    SELECT b.id AS buyer_id, b.name AS buyer_name, SUM(i.balance) AS amount
                      FROM Buyer b
                      JOIN Invoice i ON i.buyer_id = b.id AND i.currency = currencies.currency
                     WHERE b.company_id IN :companies
                     GROUP BY b.id, b.name
                     ORDER BY amount DESC
                     LIMIT 10
                   )
            """, nativeQuery = true)
    Collection<Object[]> topBuyers(Set<UUID> companies);

    /**
     * Get top unvailable buyers for each currency
     */
    @Query(value = """
            SELECT currency, buyer_id, buyer_name, amount
              FROM (SELECT DISTINCT currency FROM Contract ORDER BY currency) currencies
             CROSS JOIN LATERAL (
                    SELECT b.id AS buyer_id, b.name AS buyer_name, SUM(iff.amount_secured) + SUM(iff.amount_unsecured) AS amount
                      FROM Buyer b
                      JOIN Invoice i ON i.buyer_id = b.id AND i.currency = currencies.currency
                      JOIN invoice_from_factor iff ON iff.invoice_id = i.id
                     WHERE b.company_id IN :companies
                       AND i.is_under_factor
                       AND i.balance != 0
                       AND iff.amount_unsecured != 0
                     GROUP BY b.id, b.name
                     ORDER BY amount DESC
                     LIMIT 10
                   )
            """, nativeQuery = true)
    Collection<Object[]> getTopUnavailables(Set<UUID> companies);
}
