package com.afakto.repository;

import com.afakto.domain.Buyer;
import com.afakto.domain.Company;
import jakarta.transaction.Transactional;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.data.jpa.repository.*;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;

/**
 * Spring Data SQL repository for the buyer entity.
 */
@Repository
public interface BuyerRepository extends JpaRepository<Buyer, UUID>, JpaSpecificationExecutor<Buyer> {
    @EntityGraph(attributePaths = {
            "address",
            "contact",
            "buyerFromFactor",
            "buyerFromInsurer",
            "creditLimit",
            "paymentTerms"
    })
    Page<Buyer> findAll(Specification<Buyer> specification, Pageable pageable);

    // Get all buyers which have invoices with "isUnderFactor" true
    @Query("""
                SELECT DISTINCT b
                  FROM Buyer b
                  JOIN Invoice i ON i.buyer = b
                 WHERE b.company = :company
                   AND b.buyerFromFactor IS NULL
                   AND i.currency = :currency
                   AND i.isUnderFactor = true
            """)
    Page<Buyer> findAllWithoutFactor(Company company, String currency, Pageable pageable);

    @Cacheable(cacheNames = "buyerByOrgIdAndCompanyCodeAndCode")
    Optional<Buyer> findOneByCompanyOrgIdAndCompanyCodeAndCode(String orgId, String companyCode, String code);

    Optional<Buyer> findOneByCompanyCodeAndCode(String companyCode, String code);

    @Cacheable(cacheNames = "buyerByCompanyAndCode")
    Optional<Buyer> findOneByCompanyAndCode(Company company, String code);

    Optional<Buyer> findOneByCompanyAndNumber(Company company, String number);

    Optional<Buyer> findOneByCompanyAndBuyerFromInsurerInsurerCode(Company company, String insurerCode);

    Optional<Buyer> findOneByCompanyAndBuyerFromFactorNumberAndBuyerFromFactorCurrency(
            Company company, String number, String currency);

    Optional<Buyer> findOneByCompanyOrgIdAndNumber(String orgId, String number);

    @Modifying
    @Query("UPDATE BuyerFromFactor bff SET bff.amountApproved = :amountApproved WHERE bff.buyer.company = :company AND bff.number = :number AND bff.currency = :currency")
    @Transactional
    int updateAmountApproved(Company company, String number, String currency, BigDecimal amountApproved);

    @Modifying
    @Query("""
            UPDATE BuyerFromFactor
               SET amountOutstanding = 0, amountDraftReceived = 0, amountFunded = 0, amountSecured = 0, lastModifiedBy = 'system', lastModifiedDate = CURRENT_TIMESTAMP, version = version + 1
             WHERE (amountOutstanding <> 0 OR amountDraftReceived <> 0 OR amountFunded <> 0 OR amountSecured <> 0)
               AND buyer.company.orgId = :orgId
               AND buyer.company.code = :code
               AND currency = :currency
               AND id NOT IN :importedIds
            """)
    @Transactional
    int resetAllBalancesByOrgIdAndCompanyCodeAndCurrency(
            String orgId,
            String code,
            String currency,
            Set<UUID> importedIds);

    /**
     * Updates all company's buyers buyerFromFactorUnknown
     */
    @Modifying
    @Query("""
            UPDATE Buyer b
               SET b.buyerFromFactorUnknown =
                   CASE WHEN b.id IN (SELECT ub.id FROM Buyer ub WHERE ub IN :unknownBuyers) THEN true ELSE false END
             WHERE b.company = :company
            """)
    @Transactional
    int updateBuyerFromFactorUnknownByCompanyAndBuyers(Company company, Set<Buyer> unknownBuyers);

    /**
     * Updates all buyers' currency and balance for a specific company
     *
     * @param company The company whose buyers should be updated
     * @return The number of rows affected
     */
    @Modifying
    @Query(value = """
            UPDATE Buyer b
               SET currency = subq.invoice_currency,
                   balance = subq.total_balance
              FROM (
                    SELECT i.buyer_id,
                           i.currency as invoice_currency,
                           COALESCE(SUM(i.balance), 0) as total_balance,
                           ROW_NUMBER() OVER (PARTITION BY i.buyer_id ORDER BY MAX(i.invoice_date) DESC) as rn
                      FROM Invoice i
                      JOIN Buyer b2 ON i.buyer_id = b2.id
                     WHERE b2.company_id = :#{#company.id}
                     GROUP BY i.buyer_id, i.currency
                   ) subq
             WHERE b.id = subq.buyer_id
               AND subq.rn = 1
               AND b.company_id = :#{#company.id}
            """, nativeQuery = true)
    @Transactional
    int updateAllCaches(Company company);

    /**
     * Updates a single buyer's currency and balance
     *
     * @param buyer The buyer to update
     * @return The number of rows affected
     */
    @Modifying
    @Query(value = """
            UPDATE Buyer b
               SET currency = subq.invoice_currency,
                   balance = subq.total_balance
              FROM (
                    SELECT MAX(currency) as invoice_currency,
                           COALESCE(SUM(balance), 0) as total_balance
                      FROM Invoice
                     WHERE buyer_id = :#{#buyer.id}
                     GROUP BY currency
                   ) subq
             WHERE b.id = :#{#buyer.id}
               """, nativeQuery = true)
    @Transactional
    int updateCache(Buyer buyer);

    /**
     * Updates a buyer's currency to the given value in the Buyer object.
     *
     * @param buyer The buyer object containing the new currency
     * @return The number of rows affected
     */
    @Modifying
    @Query("""
             UPDATE Buyer b
                SET b.currency = :#{#buyer.currency}
              WHERE b = :buyer
            """)
    @Transactional
    int updateBuyerCurrency(Buyer buyer);

    long countByCompanyId(UUID id);
}
