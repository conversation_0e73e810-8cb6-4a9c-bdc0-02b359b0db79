package com.afakto.batch.bnp;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

import org.apache.commons.lang3.StringUtils;

import com.afakto.domain.BankTransaction;
import com.afakto.domain.Datastream;
import com.afakto.domain.enumeration.BankTransactionType;
import com.afakto.repository.ContractRepository;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;

@Data
@Slf4j
public class BnpAccountStatementLine {
    private final DateTimeFormatter dtf = DateTimeFormatter.ofPattern("ddMMyy");

    private String recordCode;
    private String bankCode;
    private String transactionBNPCode;
    private String bankCounter;
    private String currencyCode;
    private Integer numberOfDecimals;
    private String accountNumber;
    private String interbankOperationBank;
    private String operationDate;
    private String rejectReasonCode;
    private String valueDate;
    private String label;
    private String documentNumber;
    private String amount;
    private Character amountSign;
    private BigDecimal balance;

    public BankTransaction toBankTransaction(
            ContractRepository contractRepository,
            BnpAccountStatementCategorisation bnpAccountStatementCategorisation,
            Datastream datastream) {
        accountNumber = StringUtils.stripStart(accountNumber, "0");

        var contract = contractRepository.findOneByCompanyOrgIdAndFactorAccountNumber(
                datastream.getOrgId(),
                accountNumber);
        if (contract.isEmpty())
            return null;

        return new BankTransaction()
                .setCompany(contract.orElseThrow().getCompany())
                .setType(getBankTransactionType())
                .setDate(LocalDate.parse(operationDate, dtf))
                .setValueDate(LocalDate.parse(valueDate, dtf))
                .setTransactionReferenceNumber(documentNumber)
                .setCurrency(currencyCode)
                .setAmount(getAmount())
                .setBalance(balance)
                .setCategory(bnpAccountStatementCategorisation.getCategory(transactionBNPCode))
                .setNarrative(label)
                .setIdentificationCode(bankCode)
                .setReferenceAccountServiceInstitution(accountNumber);
    }

    /*
     * Negative should be DEBIT, positive should be CREDIT
     */
    BankTransactionType getBankTransactionType() {
        if (amountSign == '{' || amountSign <= 'I')
            return BankTransactionType.CREDIT;
        return BankTransactionType.DEBIT;
    }

    /*
     * The last character of each amount contains two datas : the sign of the
     * amount, and the last digit, as below :
     * A -> I = 1 -> 9
     * J -> R = -1 -> -9
     * { = 0 +
     * } = 0 -
     * Examples :
     * M = 2251E -> 225,15
     * M = 1130{ -> 113,00
     * M = 181538M -W -18153,83
     * M = 6025} -> -602,50
     */
    BigDecimal getAmount() {
        int lastChar = 0;
        if ('A' <= amountSign && amountSign <= 'I')
            lastChar = amountSign - '@';
        else if ('J' <= amountSign && amountSign <= 'R')
            lastChar = amountSign - 'I';

        var result = new BigDecimal(amount + lastChar)
                .movePointLeft(numberOfDecimals);

        if (BankTransactionType.DEBIT.equals(getBankTransactionType()))
            return result.negate();
        else
            return result;
    }
}
