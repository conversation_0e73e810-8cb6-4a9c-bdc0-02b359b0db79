package com.afakto.batch.bnp;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.NoSuchElementException;
import java.util.concurrent.atomic.AtomicReference;

import org.apache.commons.lang3.StringUtils;
import org.springframework.batch.core.ExitStatus;
import org.springframework.batch.core.ItemWriteListener;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.StepExecution;
import org.springframework.batch.core.StepExecutionListener;
import org.springframework.batch.core.repository.JobRepository;
import org.springframework.batch.core.step.builder.StepBuilder;
import org.springframework.batch.item.Chunk;
import org.springframework.batch.item.ExecutionContext;
import org.springframework.batch.item.data.builder.RepositoryItemWriterBuilder;
import org.springframework.batch.item.file.FlatFileItemReader;
import org.springframework.batch.item.file.FlatFileParseException;
import org.springframework.batch.item.file.LineMapper;
import org.springframework.batch.item.file.builder.FlatFileItemReaderBuilder;
import org.springframework.batch.item.file.mapping.BeanWrapperFieldSetMapper;
import org.springframework.batch.item.file.mapping.DefaultLineMapper;
import org.springframework.batch.item.file.transform.FixedLengthTokenizer;
import org.springframework.batch.item.file.transform.Range;
import org.springframework.batch.item.file.transform.RangeArrayPropertyEditor;
import org.springframework.core.convert.converter.Converter;
import org.springframework.core.convert.support.DefaultConversionService;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Component;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.util.ObjectUtils;

import com.afakto.batch.common.BatchErrorHandlers;
import com.afakto.batch.common.LineWrapper;
import com.afakto.domain.BankTransaction;
import com.afakto.domain.Datastream;
import com.afakto.domain.DatastreamFailure;
import com.afakto.repository.BankTransactionRepository;
import com.afakto.repository.ContractRepository;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Component
@RequiredArgsConstructor
@Slf4j
/**
 * Import bank transactions from a BNP account statement file. Extension .22
 *
 * The incoming file is generally divided in two parts, only one really
 * corresponding to a contract.
 *
 * Moreover, the first and last line of each section correspond to the previous
 * statement and the current statement balances.
 */
public class BnpAccountStatementImport {
    private final BankTransactionRepository bankTransactionRepository;
    private final BnpAccountStatementCategorisation bnpAccountStatementCategorisation;
    private final ContractRepository contractRepository;

    private final JobRepository jobRepository;
    private final PlatformTransactionManager transactionManager;

    public Step createStep(Datastream datastream, Resource resource) {
        AtomicReference<BigDecimal> balance = new AtomicReference<>(null);

        return new StepBuilder("importBankTransactionStep", jobRepository)
                .<LineWrapper<BnpAccountStatementLine>, BankTransaction>chunk(25, transactionManager)
                .reader(createAggregateReader(datastream, resource, balance))
                .processor(lineWrapper -> processLine(datastream, lineWrapper))
                .writer(new RepositoryItemWriterBuilder<BankTransaction>()
                        .repository(bankTransactionRepository)
                        .build())
                .faultTolerant()
                .skip(FlatFileParseException.class)
                .skip(NoSuchElementException.class)
                .skipLimit(1000)
                .listener(BatchErrorHandlers.createReadErrorListener(datastream))
                .listener(BatchErrorHandlers.createProcessErrorListener(datastream))
                .listener(new ItemWriteListener<BankTransaction>() {
                    @Override
                    public void beforeWrite(Chunk<? extends BankTransaction> items) {
                        // Count inserts before writing to database
                        // This avoids double-counting during chunk replays
                        items.forEach(item -> {
                            if (item.getId() == null)
                                datastream.incrementInserts();
                            else
                                datastream.incrementUpdates();
                        });
                    }
                })
                .listener(new StepExecutionListener() {
                    @Override
                    public ExitStatus afterStep(StepExecution stepExecution) {
                        var updated = bankTransactionRepository.updateBalances(balance.get());
                        log.info("Updated bank transactions: {}. Starting balance: {}", updated, balance.get());
                        return null;
                    }
                })
                .build();
    }

    /**
     * Process a line wrapper containing a BNP account statement line.
     * Converts it directly to a BankTransaction, letting toBankTransaction handle
     * all validation.
     */
    BankTransaction processLine(Datastream datastream, LineWrapper<BnpAccountStatementLine> lineWrapper) {
        return lineWrapper.entity()
                .toBankTransaction(contractRepository, bnpAccountStatementCategorisation, datastream);
    }

    FlatFileItemReader<LineWrapper<BnpAccountStatementLine>> createAggregateReader(
            Datastream datastream, Resource resource, AtomicReference<BigDecimal> balance) {
        return new FlatFileItemReader<LineWrapper<BnpAccountStatementLine>>() {
            private final FlatFileItemReader<LineWrapper<BnpAccountStatementLine>> delegate = new FlatFileItemReaderBuilder<LineWrapper<BnpAccountStatementLine>>()
                    .name("accountStatementReader")
                    .resource(resource)
                    .lineMapper(createLineMapper())
                    .build();

            @Override
            public void open(ExecutionContext executionContext) {
                delegate.open(executionContext);
            }

            @Override
            public void close() {
                delegate.close();
            }

            @Override
            public LineWrapper<BnpAccountStatementLine> read() throws Exception {
                LineWrapper<BnpAccountStatementLine> lineWrapper;
                while ((lineWrapper = delegate.read()) != null) {
                    BnpAccountStatementLine line = lineWrapper.entity();

                    // Initialize balance from first 01 record, then skip it
                    if ("01".equals(line.getRecordCode()) && balance.get() == null) {
                        balance.set(line.getAmount());
                        continue;
                    }

                    // Process 04 records (transactions) and aggregate with 05 records
                    if ("04".equals(line.getRecordCode())) {
                        processTransactionRecord(lineWrapper.lineNumber(), line);
                        return lineWrapper;
                    }

                    // Skip all other record types
                }
                return null;
            }

            private void processTransactionRecord(int lineNumber, BnpAccountStatementLine line) throws Exception {
                LineWrapper<BnpAccountStatementLine> next = delegate.read();

                if (next != null && "05".equals(next.entity().getRecordCode())) {
                    if (StringUtils.isNotBlank(next.entity().getLabel()))
                        line.setLabel(line.getLabel() + next.entity().getLabel());
                } else {
                    // Missing 05 record - log error
                    log.error("[BNP account statement import] A 05 record is missing, " +
                            "following line could be missed: {}",
                            next != null ? next.entity() : null);
                    datastream.getFailures().add(
                            new DatastreamFailure()
                                    .setLine(lineNumber)
                                    .setMessage("A 05 record is missing")
                                    .setRaw(next != null ? next.rawInput() : "null"));
                }
            }
        };
    }

    /**
     * Creates a line mapper that wraps BnpAccountStatementLine in LineWrapper
     * with raw input and line number.
     */
    private LineMapper<LineWrapper<BnpAccountStatementLine>> createLineMapper() {
        var range = new RangeArrayPropertyEditor();
        range.setAsText("1,3,8,12,17,20-20,22,33,35,41,43,49-79,82-88,91,104-104");

        var lineTokenizer = new FixedLengthTokenizer();
        lineTokenizer.setColumns((Range[]) range.getValue());
        lineTokenizer.setStrict(false);
        lineTokenizer.setNames(
                "recordCode", "bankCode", "transactionBNPCode", "bankCounter", "currencyCode",
                "numberOfDecimals",
                "accountNumber", "interbankOperationBank", "operationDate", "rejectReasonCode",
                "valueDate", "label", "documentNumber", "amount", "amountSign");

        var fieldSetMapper = new BeanWrapperFieldSetMapper<BnpAccountStatementLine>();
        fieldSetMapper.setTargetType(BnpAccountStatementLine.class);
        fieldSetMapper.setStrict(false);
        fieldSetMapper.setConversionService(createConversionService());

        var baseLineMapper = new DefaultLineMapper<BnpAccountStatementLine>();
        baseLineMapper.setLineTokenizer(lineTokenizer);
        baseLineMapper.setFieldSetMapper(fieldSetMapper);

        // Wrap the base mapper to return LineWrapper
        return (line, lineNumber) -> {
            BnpAccountStatementLine bnpLine = baseLineMapper.mapLine(line, lineNumber);
            return new LineWrapper<>(lineNumber, line, bnpLine);
        };
    }

    private DefaultConversionService createConversionService() {
        DefaultConversionService conversionService = new DefaultConversionService();
        DefaultConversionService.addDefaultConverters(conversionService);
        conversionService.addConverter(new Converter<String, LocalDate>() {
            @Override
            public LocalDate convert(String text) {
                return ObjectUtils.isEmpty(text) ? null : LocalDate.parse(text, DateTimeFormatter.BASIC_ISO_DATE);
            }
        });
        // Remove leading 0, which appear "before" the - negative sign
        conversionService.addConverter(new Converter<String, BigDecimal>() {
            @Override
            public BigDecimal convert(String text) {
                return new BigDecimal(StringUtils.stripStart(text, "0"));
            }
        });
        return conversionService;
    }
}
