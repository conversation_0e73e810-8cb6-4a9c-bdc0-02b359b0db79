package com.afakto.service;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Collection;
import java.util.Currency;
import java.util.Map;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;

import org.springframework.data.util.Pair;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.afakto.domain.Company;
import com.afakto.repository.DashboardRepository;
import com.afakto.repository.UserRepository;
import com.afakto.security.SecurityUtils;

import lombok.RequiredArgsConstructor;

/**
 * Get and organise data coming from the database, destined to the dashboard
 *
 * Is is organised by currency, and return pairs:
 * - one element for last month,
 * - the other for current date
 */
@RequiredArgsConstructor
@Service
@Transactional(readOnly = true)
public class DashboardService {
    private final DashboardRepository repository;
    private final UserRepository userRepository;

    public Map<Currency, Pair<BigDecimal, BigDecimal>> getFactorDebt(Set<UUID> companies) {
        return repository.getFactorDebt(getUserCompaniesPerimeter(companies))
                .stream()
                .collect(Collectors.toMap(
                        // Currency
                        o -> Currency.getInstance((String) o[0]),
                        o -> Pair.of((BigDecimal) o[1], (BigDecimal) o[2])));
    }

    public Map<Currency, Map<LocalDate, BigDecimal>> getHistory(Set<UUID> companies) {
        // Transform the list of Currency, date and amount into a double map
        return repository.getHistory(getUserCompaniesPerimeter(companies))
                .stream()
                .collect(
                        Collectors.groupingBy(
                                // Currency
                                o -> Currency.getInstance((String) o[0]),
                                Collectors.toMap(
                                        // From sql date to LocalDate
                                        o -> ((java.sql.Date) o[1]).toLocalDate(),
                                        // Amount
                                        o -> ((BigDecimal) o[2]))));
    }

    public Map<Currency, Pair<BigDecimal, BigDecimal>> getAvailableToSell(Set<UUID> companies) {
        return repository.availableToSell(getUserCompaniesPerimeter(companies))
                .stream()
                .collect(Collectors.toMap(
                        // Currency
                        o -> Currency.getInstance((String) o[0]),
                        o -> Pair.of((BigDecimal) o[1], (BigDecimal) o[2])));
    }

    public Map<Currency, Pair<BigDecimal, BigDecimal>> getPotentialFunding(Set<UUID> companies) {
        return repository.potentialFunding(getUserCompaniesPerimeter(companies))
                .stream()
                .collect(Collectors.toMap(
                        // Currency
                        o -> Currency.getInstance((String) o[0]),
                        o -> Pair.of((BigDecimal) o[1], (BigDecimal) o[2])));
    }

    public Map<Currency, Pair<BigDecimal, BigDecimal>> getGuaranteeFund(Set<UUID> companies) {
        return repository.guaranteeFund(getUserCompaniesPerimeter(companies))
                .stream()
                .collect(Collectors.toMap(
                        // Currency
                        o -> Currency.getInstance((String) o[0]),
                        o -> Pair.of((BigDecimal) o[1], (BigDecimal) o[2])));
    }

    public Map<Currency, Pair<BigDecimal, BigDecimal>> getAmountAtRisk(Set<UUID> companies) {
        return repository.amountAtRisk(getUserCompaniesPerimeter(companies))
                .stream()
                .collect(Collectors.toMap(
                        // Currency
                        o -> Currency.getInstance((String) o[0]),
                        o -> Pair.of((BigDecimal) o[1], (BigDecimal) o[2])));
    }

    public Map<Currency, Pair<BigDecimal, BigDecimal>> getAmountOutstanding(Set<UUID> companies) {
        return repository.amountOutstanding(getUserCompaniesPerimeter(companies))
                .stream()
                .collect(Collectors.toMap(
                        // Currency
                        o -> Currency.getInstance((String) o[0]),
                        o -> Pair.of((BigDecimal) o[1], (BigDecimal) o[2])));
    }

    public Map<Currency, Pair<BigDecimal, BigDecimal>> getAmountUnsecured(Set<UUID> companies) {
        return repository.amountUnsecured(getUserCompaniesPerimeter(companies))
            .stream()
            .collect(Collectors.toMap(
                // Currency
                o -> Currency.getInstance((String) o[0]),
                o -> Pair.of((BigDecimal) o[1], (BigDecimal) o[2])));
    }

    public Map<Currency, Pair<BigDecimal, BigDecimal>> getAmountFiscalYearFees(Set<UUID> companies) {
        return repository.amountFiscalYearFees(getUserCompaniesPerimeter(companies))
                .stream()
                .collect(Collectors.toMap(
                        // Currency
                        o -> Currency.getInstance((String) o[0]),
                        o -> Pair.of((BigDecimal) o[1], (BigDecimal) o[2])));
    }

    public Map<Currency, Pair<BigDecimal, BigDecimal>> getCoverage(Set<UUID> companies) {
        return repository.getCoverage(getUserCompaniesPerimeter(companies))
                .stream()
                .collect(Collectors.<Object[], Currency, Pair<BigDecimal, BigDecimal>>toMap(
                        // Currency
                        o -> Currency.getInstance((String) o[0]),
                        // Return non-covered amount and total balance amount for frontend calculation
                        o -> {
                            BigDecimal nonCoveredBalanceAmount = (BigDecimal) o[1];
                            BigDecimal totalBalanceAmount = (BigDecimal) o[2];
                            return Pair.of(nonCoveredBalanceAmount, totalBalanceAmount);
                        }));
    }

    public Map<Currency, Map<String, BigDecimal>> getContractSituation(Set<UUID> companies) {
        return repository.contractSituation(getUserCompaniesPerimeter(companies))
                .stream()
                .collect(Collectors.toMap(
                        // Currency
                        o -> Currency.getInstance((String) o[0]),
                        o -> Map.of(
                                "currentOutstanding", (BigDecimal) o[1],
                                "availableToSell", (BigDecimal) o[2],
                                "remainingAuthorized", (BigDecimal) o[3])));
    }

    public Collection<Map<String, Object>> getTopBuyers(Set<UUID> companies) {
        return repository.topBuyers(getUserCompaniesPerimeter(companies))
                .stream()
                .map(o -> Map.of(
                        "currency", o[0],
                        "id", ((UUID) o[1]).toString(),
                        "name", (String) o[2],
                        "amount", o[3]))
                .toList();
    }

    public Collection<Map<String, Object>> getTopUnavailables(Set<UUID> companies) {
        return repository.getTopUnavailables(getUserCompaniesPerimeter(companies))
                .stream()
                .map(o -> Map.of(
                        "currency", o[0],
                        "id", ((UUID) o[1]).toString(),
                        "name", (String) o[2],
                        "amount", o[3]))
                .toList();
    }

    public Map<Currency, Pair<BigDecimal, BigDecimal>> getExcludedBuyers(Set<UUID> companies) {
        return repository
            .getExcludedBuyers(getUserCompaniesPerimeter(companies))
            .stream()
            .collect(Collectors.toMap(o -> Currency.getInstance((String) o[0]), o -> Pair.of((BigDecimal) o[1], (BigDecimal) o[2])));
    }

    /**
     * Setup a perimeter of companies to filter the results
     *
     * If the companies parameter is null or empty, the perimeter is the companies
     * of the current user
     * else, the perimeter is the intersection between the companies of the current
     * user and the companies parameter
     *
     * @param companies the companies manually selected by the user
     * @return the companies perimeter
     */
    private Set<UUID> getUserCompaniesPerimeter(Set<UUID> companies) {
        return userRepository.findOneByLogin(SecurityUtils.getCurrentUserLogin().orElseThrow())
                .orElseThrow(() -> new IllegalStateException("User not found"))
                .getCompanies()
                .stream()
                .map(Company::getId)
                .filter(c -> companies == null || companies.isEmpty() || companies.contains(c))
                .collect(Collectors.toSet());
    }
}
