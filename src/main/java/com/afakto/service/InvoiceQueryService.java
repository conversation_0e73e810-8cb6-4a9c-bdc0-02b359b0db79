package com.afakto.service;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

import org.apache.commons.lang3.function.TriFunction;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.afakto.domain.Buyer;
import com.afakto.domain.BuyerFromFactor_;
// for static metamodels
import com.afakto.domain.Buyer_;
import com.afakto.domain.Cession_;
import com.afakto.domain.Company_;
import com.afakto.domain.Contract;
import com.afakto.domain.Contract_;
import com.afakto.domain.CreditInsurancePolicy_;
import com.afakto.domain.CreditLimit_;
import com.afakto.domain.Gap_;
import com.afakto.domain.Invoice;
import com.afakto.domain.InvoiceFromFactor_;
import com.afakto.domain.Invoice_;
import com.afakto.domain.contractdata.FinancialInformation_;
import com.afakto.domain.enumeration.InvoiceType;
import com.afakto.repository.ContractRepository;
import com.afakto.repository.InvoiceRepository;
import com.afakto.service.criteria.InvoiceCriteria;
import com.afakto.service.dto.InvoiceDTO;
import com.afakto.service.mapper.InvoiceMapper;
import com.afakto.service.perimeter.UserCompaniesQueryBase;

import jakarta.persistence.EntityManager;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.JoinType;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;
import jakarta.persistence.criteria.Subquery;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import tech.jhipster.service.QueryService;
import tech.jhipster.service.filter.BigDecimalFilter;
import tech.jhipster.service.filter.BooleanFilter;

/**
 * Service for executing complex queries for {@link Invoice} entities in the
 * database.
 * The main input is a {@link InvoiceCriteria} which gets converted to
 * {@link Specification},
 * in a way that all the filters must apply.
 * It returns a {@link Page} of {@link InvoiceDTO} which fulfills the criteria.
 */
@RequiredArgsConstructor
@Service
@Slf4j
@Transactional(readOnly = true)
public class InvoiceQueryService extends QueryService<Invoice> {
    private final ContractRepository contractRepository;
    private final EntityManager entityManager;
    private final InvoiceMapper invoiceMapper;
    private final InvoiceRepository invoiceRepository;
    private final UserCompaniesQueryBase<Invoice> perimeter;

    /**
     * Return a {@link Page} of {@link InvoiceDTO} which matches the criteria from
     * the database.
     *
     * @param criteria The object which holds all the filters, which the entities
     *                 should match.
     * @param page     The page, which should be returned.
     * @return the matching entities.
     */
    @Transactional(readOnly = true)
    public Page<InvoiceDTO> findByCriteria(InvoiceCriteria criteria, Pageable page) {
        log.debug("find by criteria : {}, page: {}", criteria, page);
        final Specification<Invoice> specification = createSpecification(criteria);

        // Sorting by "created" column on top of existing sort
        Sort sort = page.getSort().and(Sort.by("createdDate").descending());
        page = PageRequest.of(page.getPageNumber(), page.getPageSize(), sort);

        return invoiceRepository.findAll(specification, page).map(invoiceMapper::toDto);
    }

    /**
     * Return the number of matching entities in the database.
     *
     * @param criteria The object which holds all the filters, which the entities
     *                 should match.
     * @return the number of matching entities.
     */
    @Transactional(readOnly = true)
    public long countByCriteria(InvoiceCriteria criteria) {
        log.debug("count by criteria : {}", criteria);
        final Specification<Invoice> specification = createSpecification(criteria);
        return invoiceRepository.count(specification);
    }

    /**
     * Function to convert {@link InvoiceCriteria} to a {@link Specification}
     *
     * @param criteria The object which holds all the filters, which the entities
     *                 should match.
     * @return the matching {@link Specification} of the entity.
     */
    protected Specification<Invoice> createSpecification(InvoiceCriteria criteria) {
        Specification<Invoice> specification = Specification.where(perimeter.userCompaniesPerimeter("buyer"));

        if (criteria == null)
            return specification;

        // This has to be called first, because the distinct method returns null
        if (criteria.getDistinct() != null) {
            specification = specification.and(distinct(criteria.getDistinct()));
        }
        if (criteria.getCompany() != null) {
            specification = specification.and(
                    buildSpecification(criteria.getCompany(),
                            root -> root.join(Invoice_.buyer)
                                    .join(Buyer_.company)
                                    .get(Company_.id)));
        }
        if (criteria.getBuyer() != null) {
            specification = specification.and(
                    buildSpecification(criteria.getBuyer(),
                            root -> root.join(Invoice_.buyer)
                                    .get(Buyer_.id)));
        }
        if (criteria.getType() != null) {
            specification = specification.and(buildSpecification(criteria.getType(), Invoice_.type));
        }
        if (criteria.getInvoiceNumber() != null) {
            specification = specification
                    .and(buildStringSpecification(criteria.getInvoiceNumber(), Invoice_.invoiceNumber));
        }
        if (criteria.getDate() != null) {
            specification = specification.and(buildRangeSpecification(criteria.getDate(), Invoice_.date));
        }
        if (criteria.getDueDate() != null) {
            specification = specification.and(buildRangeSpecification(criteria.getDueDate(), Invoice_.dueDate));
        }
        if (criteria.getBuyerExcluded() != null) {
            specification = specification.and(
                    buildSpecification(criteria.getBuyerExcluded(),
                            root -> root.join(Invoice_.buyer)
                                    .get(Buyer_.excluded)));
        }
        if (criteria.getPaidOrExcluded() != null) {
            // This is a specific mechanism, to list invoices with balance equals to 0
            var balanceFilter = new BigDecimalFilter();
            balanceFilter.setEquals(new BigDecimal(0));
            var booleanFilter = new BooleanFilter();
            booleanFilter.setEquals(true);
            specification = specification.and(
                    buildRangeSpecification(balanceFilter, Invoice_.balance)
                            .or(buildSpecification(booleanFilter,
                                    root -> root.join(Invoice_.buyer).get(Buyer_.excluded)))
                            .or(buildSpecification(booleanFilter, Invoice_.excluded)));
        }
        if (criteria.getPaidOrExcludedOrDueDate() != null) {
            // This is a specific mechanism, to list invoices with balance equals to 0
            // *or* due date in the range specified
            var balanceFilter = new BigDecimalFilter();
            balanceFilter.setEquals(new BigDecimal(0));
            var booleanFilter = new BooleanFilter();
            booleanFilter.setEquals(true);
            specification = specification.and(
                    buildRangeSpecification(balanceFilter, Invoice_.balance)
                            .or(buildSpecification(booleanFilter,
                                    root -> root.join(Invoice_.buyer).get(Buyer_.excluded)))
                            .or(buildSpecification(booleanFilter, Invoice_.excluded))
                            .or(buildRangeSpecification(criteria.getPaidOrExcludedOrDueDate(), Invoice_.dueDate)));
        }
        if (criteria.getFutureGapForContract() != null) {
            specification = findByFutureGapForContract(criteria, specification);
        }
        if (criteria.getCurrency() != null) {
            specification = specification.and(buildStringSpecification(criteria.getCurrency(), Invoice_.currency));
        }
        if (criteria.getAmount() != null) {
            specification = specification.and(buildRangeSpecification(criteria.getAmount(), Invoice_.amount));
        }
        if (criteria.getBalance() != null) {
            specification = specification.and(buildRangeSpecification(criteria.getBalance(), Invoice_.balance));
        }
        if (criteria.getPaymentDate() != null) {
            specification = specification
                    .and(buildRangeSpecification(criteria.getPaymentDate(), Invoice_.paymentDate));
        }
        if (criteria.getHasCover() != null) {
            specification = specification.and(createHasCoverSpecification(criteria.getHasCover()));
        }
        if (criteria.getExcluded() != null) {
            specification = specification.and(buildSpecification(criteria.getExcluded(), Invoice_.excluded));
        }

        if (criteria.getIsUnderFactor() != null) {
            specification = specification
                    .and(buildSpecification(criteria.getIsUnderFactor(), Invoice_.isUnderFactor));
        }

        if (criteria.getInvoiceFromFactor_amountFunded() != null) {
            specification = specification.and(
                    buildSpecification(criteria.getInvoiceFromFactor_amountFunded(),
                            root -> root.join(Invoice_.invoiceFromFactor, JoinType.LEFT)
                                    .get(InvoiceFromFactor_.amountFunded)));
        }
        if (criteria.getInvoiceFromFactor_amountUnfunded() != null) {
            specification = specification.and(
                    buildSpecification(criteria.getInvoiceFromFactor_amountUnfunded(),
                            root -> root.join(Invoice_.invoiceFromFactor, JoinType.LEFT)
                                    .get(InvoiceFromFactor_.amountUnfunded)));
        }
        if (criteria.getInvoiceFromFactor_amountUnavailable() != null) {
            specification = specification.and(
                    buildSpecification(criteria.getInvoiceFromFactor_amountUnavailable(),
                            root -> root.join(Invoice_.invoiceFromFactor, JoinType.LEFT)
                                    .get(InvoiceFromFactor_.amountUnavailable)));
        }
        if (criteria.getBuyerFromFactor_amountApproved() != null) {
            specification = specification.and(
                    buildSpecification(criteria.getBuyerFromFactor_amountApproved(),
                            root -> root.join(Invoice_.buyer)
                                    .join(Buyer_.buyerFromFactor)
                                    .get(BuyerFromFactor_.amountApproved)));
        }
        if (criteria.getCession() != null) {
            specification = specification.and(
                    buildSpecification(criteria.getCession(),
                            root -> root.join(Invoice_.cessions, JoinType.LEFT)
                                    .get(Cession_.id)));
        }

        if (criteria.getReconciliationJournal() != null) {
            specification = specification
                    .and(buildStringSpecification(criteria.getReconciliationJournal(), Invoice_.reconciliationJournal));
        }
        if (criteria.getGap() != null) {
            if (Boolean.TRUE.equals(criteria.getGap().getEquals())) {
                specification = specification.and(
                        buildSpecification(criteria.getGap(),
                                root -> root.join(Invoice_.gap, JoinType.LEFT).isNotNull()));
            } else {
                specification = specification.and(
                        buildSpecification(criteria.getGap(),
                                root -> root.join(Invoice_.gap, JoinType.LEFT).isNotNull()));
            }
        }
        if (criteria.getGapType() != null) {
            specification = specification.and(
                    buildSpecification(criteria.getGapType(),
                            root -> root.join(Invoice_.gap, JoinType.LEFT)
                                    .get(Gap_.type)));
        }
        if (criteria.getGapCredit() != null) {
            specification = specification.and(
                    buildSpecification(criteria.getGapCredit(),
                            root -> root.join(Invoice_.gap, JoinType.LEFT)
                                    .get(Gap_.credit)));
        }
        if (criteria.getGapCession() != null) {
            specification = specification.and(
                    buildSpecification(criteria.getGapCession(),
                            root -> root.join(Invoice_.gap, JoinType.LEFT)
                                    .join(Gap_.cession, JoinType.LEFT)
                                    .get(Cession_.id)));
        }

        return specification;
    }

    private Specification<Invoice> findByFutureGapForContract(InvoiceCriteria criteria,
            Specification<Invoice> specification) {
        return specification.and((root, query, cb) -> {
            UUID contractId = criteria.getFutureGapForContract().getEquals();
            Contract contract = contractRepository.findById(contractId)
                    .orElseThrow(() -> new IllegalArgumentException("Contract not found with ID: " + contractId));

            var predicates = new ArrayList<>(getBasePredicates(root, cb, contract));
            getGapPredicates(root, cb, contract).ifPresent(gap -> predicates.add(cb.or(gap.toArray(new Predicate[0]))));

            return cb.and(predicates.toArray(new Predicate[0]));
        });
    }

    private List<Predicate> getBasePredicates(Root<Invoice> root, CriteriaBuilder cb, Contract contract) {
        return List.of(
                cb.equal(root.join(Invoice_.buyer).join(Buyer_.company), contract.getCompany()),
                cb.equal(root.get(Invoice_.currency), contract.getFinancialInformation().getCurrency()),
                cb.equal(root.get(Invoice_.isUnderFactor), true));
    }

    private Optional<List<Predicate>> getGapPredicates(Root<Invoice> root, CriteriaBuilder cb, Contract contract) {
        var predicates = new ArrayList<Predicate>();

        if (contract.isCashIn() && contract.getJournal() != null) {
            predicates.add(cb.and(
                    root.get(Invoice_.reconciliationJournal).isNotNull(),
                    cb.notEqual(cb.coalesce(root.get(Invoice_.reconciliationJournal), ""), ""),
                    cb.notEqual(root.get(Invoice_.reconciliationJournal), contract.getJournal())));
        }

        if (contract.getDefaultOverDue() != null) {
            predicates.add(cb.lessThan(
                    root.get(Invoice_.dueDate),
                    LocalDate.now().minusDays(contract.getDefaultOverDue())));
        }

        predicates.add(cb.or(
                cb.equal(root.join(Invoice_.buyer).get(Buyer_.excluded), true),
                cb.equal(root.get(Invoice_.excluded), true)));

        return predicates.isEmpty() ? Optional.empty() : Optional.of(predicates);
    }

    /**
     * Creates a specification for the hasCover filter.
     *
     * @param hasCoverFilter The filter to apply
     * @return A specification that filters invoices based on whether they have
     *         cover
     */
    private Specification<Invoice> createHasCoverSpecification(BooleanFilter hasCoverFilter) {
        return (root, query, builder) -> {
            // Subquery to get the buyer's credit limit amount
            Subquery<BigDecimal> buyerAmount = query.subquery(BigDecimal.class);
            Root<Buyer> buyerRoot = buyerAmount.from(Buyer.class);
            buyerAmount.select(buyerRoot.join(Buyer_.creditLimit).get(CreditLimit_.amount))
                    .where(builder.equal(buyerRoot, root.get(Invoice_.buyer)));

            // Function to create a contract subquery with common conditions
            java.util.function.Function<java.util.function.Function<Root<Contract>, jakarta.persistence.criteria.Path<BigDecimal>>, Subquery<BigDecimal>> createContractSubquery = pathSelector -> {
                Subquery<BigDecimal> subquery = query.subquery(BigDecimal.class);
                Root<Contract> contractRoot = subquery.from(Contract.class);
                subquery.select(pathSelector.apply(contractRoot))
                        .where(builder.and(
                                builder.equal(
                                        contractRoot.join(Contract_.financialInformation)
                                                .get(FinancialInformation_.currency),
                                        root.get(Invoice_.currency)),
                                builder.equal(
                                        contractRoot.join(Contract_.company),
                                        root.join(Invoice_.buyer).get(Buyer_.company))));
                return subquery;
            };

            // Get contract subqueries using the function
            Subquery<BigDecimal> blindCoverAmount = createContractSubquery.apply(
                    contractRoot -> contractRoot.join(Contract_.creditInsurancePolicy)
                            .get(CreditInsurancePolicy_.blindCoverAmount));

            Subquery<BigDecimal> nonGuaranteeLine = createContractSubquery.apply(
                    contractRoot -> contractRoot.join(Contract_.financialInformation)
                            .get(FinancialInformation_.nonGuaranteeLine));

            // Create coalesce for standard cover (buyer credit limit or blind cover amount)
            CriteriaBuilder.Coalesce<BigDecimal> standardCover = builder.coalesce();
            standardCover.value(buyerAmount);
            standardCover.value(blindCoverAmount);
            standardCover.value(BigDecimal.ZERO);

            if (Boolean.TRUE.equals(hasCoverFilter.getEquals())) {
                // Has cover if either standard cover OR non-guarantee line is non-zero
                return builder.or(
                        builder.notEqual(standardCover, BigDecimal.ZERO),
                        builder.and(
                                builder.isNotNull(nonGuaranteeLine),
                                builder.notEqual(nonGuaranteeLine, BigDecimal.ZERO)));
            } else {
                // No cover if both standard cover AND non-guarantee line are zero or null
                return builder.and(
                        builder.equal(standardCover, BigDecimal.ZERO),
                        builder.or(
                                builder.isNull(nonGuaranteeLine),
                                builder.equal(nonGuaranteeLine, BigDecimal.ZERO)));
            }
        };
    }

    public record InvoiceAggregation(
            InvoiceType type,
            long count,
            BigDecimal totalAmount,
            BigDecimal totalBalance) {
    }

    @Transactional(readOnly = true)
    public List<InvoiceAggregation> aggregateByType(InvoiceCriteria criteria) {
        log.debug("aggregate by type with criteria: {}", criteria);
        final Specification<Invoice> specification = createSpecification(criteria);

        return executeAggregationQuery(InvoiceAggregation.class, (root, query, cb) -> {
            query.multiselect(
                    root.get(Invoice_.type),
                    cb.count(root),
                    cb.sum(root.get(Invoice_.amount)),
                    cb.sum(root.get(Invoice_.balance)))
                    .where(specification.toPredicate(root, query, cb))
                    .groupBy(root.get(Invoice_.type));

            return query;
        });
    }

    private <T> List<T> executeAggregationQuery(Class<T> resultType,
            TriFunction<Root<Invoice>, CriteriaQuery<T>, CriteriaBuilder, CriteriaQuery<T>> queryBuilder) {
        CriteriaBuilder cb = entityManager.getCriteriaBuilder();
        CriteriaQuery<T> query = cb.createQuery(resultType);
        Root<Invoice> root = query.from(Invoice.class);

        return entityManager.createQuery(queryBuilder.apply(root, query, cb))
                .getResultList();
    }
}
