package com.afakto.service.mapper;

import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

import com.afakto.domain.CreditInsurancePolicy;
import com.afakto.service.dto.CreditInsurancePolicyDTO;

/**
 * Mapper for the entity {@link com.afakto.domain.CreditInsurancePolicy}
 * and its DTO {@link CreditInsurancePolicyDTO}.
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface CreditInsurancePolicyMapper extends EntityMapper<CreditInsurancePolicyDTO, CreditInsurancePolicy> {
}
