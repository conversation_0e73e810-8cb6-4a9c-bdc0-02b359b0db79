package com.afakto.service.mapper;

import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

import com.afakto.domain.Buyer;
import com.afakto.service.dto.BuyerDTO;

/**
 * Mapper for the entity {@link Buyer} and its DTO {@link BuyerDTO}.
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface BuyerMapper extends EntityMapper<BuyerDTO, Buyer> {
}
