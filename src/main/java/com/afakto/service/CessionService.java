package com.afakto.service;

import static com.afakto.domain.enumeration.GapType.EXCLUSION;
import static com.afakto.domain.enumeration.GapType.OVERDUE;
import static com.afakto.domain.enumeration.GapType.RECONCILIATION;

import java.io.IOException;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.HashSet;
import java.util.Optional;
import java.util.UUID;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.FileSystemResource;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import com.afakto.domain.Cession;
import com.afakto.domain.Gap;
import com.afakto.domain.Invoice;
import com.afakto.repository.CessionRepository;
import com.afakto.repository.InvoiceRepository;
import com.afakto.service.dto.CessionDTO;
import com.afakto.service.factor.FactorBnpService;
import com.afakto.service.factor.FactorSgService;
import com.afakto.service.mapper.CessionMapper;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * Service Implementation for managing {@link com.afakto.domain.Cession}.
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class CessionService {
    private final FactorBnpService factorBnpService;
    private final FactorSgService factorSgService;
    private final CessionMapper cessionMapper;
    private final CessionRepository cessionRepository;
    private final InvoiceRepository invoiceRepository;

    @Value("file:${application.filesystem}/out/")
    private Resource toExport;

    /**
     * Save a cession.
     *
     * @param cessionDTO the entity to save.
     * @return the persisted entity.
     */
    public CessionDTO save(CessionDTO cessionDTO) {
        log.info("Creating cession for {} ({})",
                cessionDTO.getContract().getCompany().getName(),
                cessionDTO.getContract().getFinancialInformation().getCurrency());
        Cession cession = cessionMapper.toEntity(cessionDTO);

        processCessionInvoices(cession);

        // So that all related data can be requested from spring batch
        cession = cessionRepository.saveAndFlush(cession);
        log.info("Cession saved to the database");

        // Can be long, thus is launched in parallel
        generateDatastreams(cession);

        return cessionMapper.toDto(cession);
    }

    private void processCessionInvoices(Cession cession) {
        var toToggle = new HashSet<Invoice>(cession.getInvoices());
        var toRemove = new HashSet<Invoice>();

        var current = invoiceRepository.findAllByBuyerCompanyAndCurrencyAndIsUnderFactorIsTrue(
                cession.getContract().getCompany(),
                cession.getContract().getFinancialInformation().getCurrency());
        for (var invoice : current)
            manageCurrent(cession, toRemove, invoice);

        // Note: setUnderFactor should only be changed after this calculation
        cession.calc(toRemove);

        toToggle.addAll(toRemove);
        toggleUnderFactor(toToggle);

        manageGaps(cession, toRemove);
    }

    private boolean isZero(BigDecimal value) {
        return value != null && value.compareTo(BigDecimal.ZERO) == 0;
    }

    private void manageCurrent(Cession cession, HashSet<Invoice> toRemove, Invoice invoice) {
        if (isZero(invoice.getBalance())) {
            // Balance of 0 means that invoice has been paid
            toRemove.add(invoice);
            if (invoice.getPaymentDate() == null) {
                invoice.setPaymentDate(LocalDate.now());
            }
        } else if (invoice.getDueDate() != null
                && cession.getContract().getDefaultOverDue() != null
                && invoice.getDueDate().isBefore(
                        LocalDate.now().minusDays(cession.getContract().getDefaultOverDue()))) {
            // or due date is over due
            toRemove.add(invoice);
        } else if (invoice.getBuyer().isExcluded()) {
            // or buyer is excluded
            toRemove.add(invoice);
        } else if (invoice.isExcluded()) {
            // or invoice is excluded
            toRemove.add(invoice);
        } else {
            cession.getInvoices().add(invoice);
        }
    }

    private void toggleUnderFactor(HashSet<Invoice> invoices) {
        for (var invoice : invoices)
            invoice.setUnderFactor(!invoice.isUnderFactor());
        // This is required because cession saves do not percolate to invoices
        invoiceRepository.saveAll(invoices);
    }

    private void manageGaps(Cession cession, HashSet<Invoice> toRemove) {
        if (!cession.getContract().isCashIn() || ObjectUtils.isEmpty(cession.getContract().getJournal())) {
            return; // No gaps needed for non-cash-in contracts
        }

        log.info("Cash in journal: {}", cession.getContract().getJournal());

        // Create gaps collection
        HashSet<Gap> gaps = new HashSet<>();

        // Debtor gaps
        for (Invoice invoice : toRemove) {
            // Case 1: Reconciliation gap (paid invoice with different journal)
            if (isZero(invoice.getBalance())
                    && !ObjectUtils.isEmpty(invoice.getReconciliationJournal())
                    && !cession.getContract().getJournal().equalsIgnoreCase(invoice.getReconciliationJournal())) {
                gaps.add(new Gap().setInvoice(invoice).setType(RECONCILIATION));
            }
            // Case 2: Overdue gap
            else if (invoice.getDueDate() != null
                    && cession.getContract().getDefaultOverDue() != null
                    && invoice.getDueDate().isBefore(
                            LocalDate.now().minusDays(cession.getContract().getDefaultOverDue()))) {
                gaps.add(new Gap().setInvoice(invoice).setType(OVERDUE));
            }
            // Case 3: Exclusion gap (buyer or invoice excluded)
            else if (invoice.getBuyer().isExcluded() || invoice.isExcluded()) {
                gaps.add(new Gap().setInvoice(invoice).setType(EXCLUSION));
            }
        }

        // Creditor gaps (invoices not under factor but with reconciliation journal)
        var gapCreditors = invoiceRepository
                .findAllByBuyerCompanyAndCurrencyAndIsUnderFactorIsFalseAndReconciliationJournalAndGapIsNull(
                        cession.getContract().getCompany(),
                        cession.getContract().getFinancialInformation().getCurrency(),
                        cession.getContract().getJournal());
        log.info("Gaps: {} - debitor; {} - creditor", gaps.size(), gapCreditors.size());

        if (gaps.isEmpty() && gapCreditors.isEmpty())
            return;

        // Add creditor gaps
        gaps.addAll(
                gapCreditors.stream()
                        .map(invoice -> new Gap().setInvoice(invoice).setType(RECONCILIATION).setCredit(true))
                        .toList());

        // Setup relationship and set gaps on cession
        gaps.forEach(gap -> gap.setCession(cession));
        cession.setGaps(gaps);
    }

    private void generateDatastreams(Cession cession) {
        switch (cession.getContract().getFactorInstitution().getName().toLowerCase()) {
            case "bnp paribas factor":
                factorBnpService.generateDatastreams(cession);
                break;
            case "societe generale factoring":
                factorSgService.generateDatastreams(cession);
                break;
            default:
                log.warn("No datastream generation for factor {}",
                        cession.getContract().getFactorInstitution().getName());
                throw new IllegalArgumentException(
                        "Unknown factor: " + cession.getContract().getFactorInstitution().getName());
        }
    }

    /**
     * Update a cession.
     *
     * @param cessionDTO the entity to save.
     * @return the persisted entity.
     */
    public CessionDTO update(CessionDTO cessionDTO) {
        log.debug("Request to update Cession : {}", cessionDTO);
        Cession cession = cessionMapper.toEntity(cessionDTO);
        cession = cessionRepository.save(cession);
        return cessionMapper.toDto(cession);
    }

    /**
     * Partially update a cession.
     *
     * @param cessionDTO the entity to update partially.
     * @return the persisted entity.
     */
    public Optional<CessionDTO> partialUpdate(CessionDTO cessionDTO) {
        log.debug("Request to partially update Cession : {}", cessionDTO);

        return cessionRepository
                .findById(cessionDTO.getId())
                .map(existingCession -> {
                    cessionMapper.partialUpdate(existingCession, cessionDTO);

                    return existingCession;
                })
                .map(cessionRepository::save)
                .map(cessionMapper::toDto);
    }

    /**
     * Get one cession by id.
     *
     * @param id the id of the entity.
     * @return the entity.
     */
    @Transactional(readOnly = true)
    public Optional<CessionDTO> findOne(UUID id) {
        log.debug("Request to get Cession : {}", id);
        return cessionRepository.findOneWithEagerRelationships(id).map(cessionMapper::toDto);
    }

    /**
     * Get one cession's file by id.
     *
     * @param id the id of the entity.
     *
     * @return the entity's file.
     * @throws IOException
     */
    @Transactional(readOnly = true)
    public Resource getFile(UUID id, UUID datastreamId) throws IOException {
        var cession = cessionRepository.findById(id).orElseThrow();
        var datastream = cession.getDatastreams().stream().filter(ff -> ff.getId().equals(datastreamId)).findFirst()
                .orElseThrow();
        return new FileSystemResource(
                toExport
                        .getFile()
                        .toPath()
                        .resolve(cession.getCompany().getOrgId())
                        .resolve(datastream.getType().toString().toLowerCase())
                        .resolve(datastream.getPath())
                        .resolve(datastream.getName()));
    }

    /**
     * Delete the cession by id.
     *
     * @param id the id of the entity.
     */
    public void delete(UUID id) {
        log.debug("Request to delete Cession : {}", id);
        cessionRepository.deleteById(id);
    }
}
