package com.afakto.service.insurer;

import com.afakto.domain.Buyer;
import com.afakto.domain.BuyerFromInsurer;
import com.afakto.domain.Contract;
import com.afakto.repository.BuyerFromInsurerRepository;
import com.afakto.repository.BuyerRepository;
import com.afakto.service.dto.BuyerDTO;
import com.afakto.service.feign.FeignInsurerCofaceHeader;
import com.afakto.service.feign.FeignInsurerCofaceHelper;
import com.afakto.service.feign.FeignInsurerCofaceService;
import com.afakto.service.feign.InsurerSearchResult;
import com.afakto.service.insurer.coface.mapping.ConsultCofaceModel;
import com.afakto.service.insurer.coface.Request;
import com.afakto.service.insurer.coface.Consult;
import com.afakto.service.insurer.coface.Search;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import feign.FeignException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * This service is used to import data from Insurer's COFACE API.
 * <p></p>
 * API documentation:
 * <a href="https://developers.coface.com/docs-technical?sw=CofaServe%20-%20API%20Product.yaml&swl=API%20CofaServe%20-%20Product#tag/Product">...</a>
 */
@RequiredArgsConstructor
@Service
@Slf4j
@Transactional
public class InsurerCofaceService {
    private final BuyerRepository buyerRepository;
    private final BuyerFromInsurerRepository buyerFromInsurerRepository;
    private final FeignInsurerCofaceService feignInsurerService;

    private final Search search;
    private final Request request;
    private final Consult consult;

    private final FeignInsurerCofaceHelper feignInsurerCofaceHelper;
    private final ObjectMapper objectMapper;

    public void requestCreditLimit(Buyer buyer, BigDecimal amount) {
        FeignInsurerCofaceHeader header = feignInsurerCofaceHelper.from(buyer);

        String insurerCode = Optional.ofNullable(buyer.getBuyerFromInsurer())
            .map(BuyerFromInsurer::getInsurerCode)
            .orElseGet(() -> request.getInsurerCode(header, buyer));

        JsonNode response = request.sendRequestCreditLimit(buyer, header, insurerCode, amount);

        BuyerFromInsurer buyerFromInsurer = Optional.ofNullable(buyer.getBuyerFromInsurer())
            .orElseGet(BuyerFromInsurer::new);

        buyerFromInsurer.setInsurerName("coface");
        buyerFromInsurer.setInsurerCode(response != null
            ? response.get("easyNumber").asText()
            : insurerCode);

        buyerFromInsurer = buyerFromInsurerRepository.save(buyerFromInsurer.setBuyer(buyer));
        buyerRepository.save(buyer.setBuyerFromInsurer(buyerFromInsurer));
    }

    public int updateCreditLimits(Contract contract) {
        FeignInsurerCofaceHeader header = feignInsurerCofaceHelper.from(contract);
        List<ConsultCofaceModel.Company> companies;
        try {
            companies = objectMapper.treeToValue(feignInsurerService.getProducts(header.apiKey(), header.idToken(), header.policyId()), ConsultCofaceModel.class).companies();
        } catch (JsonProcessingException e) {
            log.error(e.getMessage());
            return 0;
        }

        consult.alreadyImported = new HashMap<>();
        var toSave = companies.stream()
                .map(cover -> {
                    var buyer = consult.findBuyer(contract.getCompany(), cover);
                    if (buyer == null)
                        return null;
                    return consult.setupBuyerFromInsurer(buyer, cover);
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        consult.alreadyImported = null;
        log.warn("Saving {} buyers", toSave.size());
        return buyerFromInsurerRepository.saveAll(toSave).size();
    }

    public Buyer updateCreditLimit(Buyer buyer) {
        FeignInsurerCofaceHeader header = feignInsurerCofaceHelper.from(buyer);

        var easyNumber = buyer.getBuyerFromInsurer() == null ? null : buyer.getBuyerFromInsurer().getInsurerCode();
        ConsultCofaceModel.Company cofaceNode = consult.getCover(header, easyNumber, buyer.getCode());

        if (cofaceNode == null)
            throw new IllegalArgumentException("Couldn't find cover for buyer");

        if (!buyer.getCode().equals(cofaceNode.customerReferenceValue()))
            consult.patchCustomerReference(header, easyNumber, buyer.getCode());
        var toSave = consult.setupBuyerFromInsurer(buyer, cofaceNode);
        if (toSave == null)
            return buyer;
        return buyer.setBuyerFromInsurer(buyerFromInsurerRepository.save(toSave));
    }

    public BuyerFromInsurer updateInsurerDecision(Buyer buyer, String deliveryId) {
        log.info("InsurerDecision update");
        FeignInsurerCofaceHeader header = feignInsurerCofaceHelper.from(buyer);

        var easyNumber = buyer.getBuyerFromInsurer() == null ? null : buyer.getBuyerFromInsurer().getInsurerCode();
        JsonNode insurerDecision = feignInsurerService.getDeliveryDecisionForEasyNumber(header.apiKey(), header.idToken(), header.policyId(), easyNumber, deliveryId);

        if (insurerDecision == null)
            throw new IllegalArgumentException("Couldn't find insurer decision for credit limit code " + deliveryId);

        buyer.getBuyerFromInsurer().setRawDecision(objectMapper.convertValue(insurerDecision, new TypeReference<>() {}));
        return buyerFromInsurerRepository.save(buyer.getBuyerFromInsurer());

//        JsonNode creditLimitPeriods = insurerDecision.path("product").path("creditLimitPeriods");
//        JsonNode firstPeriod = creditLimitPeriods.get(0);
//        JsonNode reasonCodeNode = firstPeriod
//            .path("creditPeriodCondition")
//            .path("reasonCodes")
//            .get(0)
//            .path("code");
//
//        var code = reasonCodeNode.asText(null);
//
//        if (code == null)
//            throw new IllegalArgumentException("Missing 'code' field");
//        var insurerComments = firstPeriod.path("underwriterComments");
//        if (insurerComments != null && insurerComments.has(0))
//            creditLimitRequest.setInsurerComment(insurerComments.get(0).asText(null));
//        creditLimitRequest.setInsurerDecision(code);
//        return creditLimitRequest;
    }

    public InsurerSearchResult searchBuyer(BuyerDTO buyer) {
        log.debug("COFACE credit insurance policy found, now searching");
        List<BuyerDTO> buyers = new ArrayList<>();

        ConsultCofaceModel node;
        try {
            node = search.search(buyer);
        } catch (FeignException exception) {
            return InsurerSearchResult.failure(search.getCofaceErrorCode(exception.getMessage()));
        }

        for (ConsultCofaceModel.Company cofaceCompany : node.companies()) {
            BuyerDTO filledBuyer = search.mapToBuyerData(cofaceCompany);
            if (filledBuyer.getNumberType() != null)
                buyers.add(filledBuyer);
        }

        return InsurerSearchResult.success(buyers);
    }

}
