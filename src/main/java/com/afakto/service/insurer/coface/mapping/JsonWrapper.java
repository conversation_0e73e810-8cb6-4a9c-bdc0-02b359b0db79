package com.afakto.service.insurer.coface.mapping;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

public record JsonWrapper<T>(T model, JsonNode raw) {
    public static <T> JsonWrapper<T> from<PERSON>son(JsonNode json, Class<T> clazz, ObjectMapper mapper) throws JsonProcessingException {
        T model = mapper.treeToValue(json, clazz);
        return new JsonWrapper<>(model, json);
    }
}
