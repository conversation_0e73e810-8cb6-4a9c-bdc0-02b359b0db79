package com.afakto.web.rest;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Collection;
import java.util.Currency;
import java.util.Map;
import java.util.Set;
import java.util.UUID;

import org.springframework.data.util.Pair;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.afakto.service.CurrencyService;
import com.afakto.service.DashboardService;

import lombok.RequiredArgsConstructor;

@RequestMapping("/api/dashboard")
@RequiredArgsConstructor
@RestController
public class DashboardResource {
    private final CurrencyService currencyService;
    private final DashboardService service;

    @GetMapping("factorDebt")
    public ResponseEntity<Map<Currency, Pair<BigDecimal, BigDecimal>>> getFactorDebt(
            @RequestParam(required = false) Set<UUID> companies) {
        var result = service.getFactorDebt(companies);
        result = currencyService.convert(result);
        return ResponseEntity.ok().body(result);
    }

    @GetMapping("history")
    public ResponseEntity<Map<Currency, Map<LocalDate, BigDecimal>>> getHistory(
            @RequestParam(required = false) Set<UUID> companies) {
        var result = service.getHistory(companies);
        result = currencyService.convertHistory(result);
        return ResponseEntity.ok().body(result);
    }

    @GetMapping("availableToSell")
    public ResponseEntity<Map<Currency, Pair<BigDecimal, BigDecimal>>> getAvailableToSell(
            @RequestParam(required = false) Set<UUID> companies) {
        var result = service.getAvailableToSell(companies);
        result = currencyService.convert(result);
        return ResponseEntity.ok().body(result);
    }

    @GetMapping("potentialFunding")
    public ResponseEntity<Map<Currency, Pair<BigDecimal, BigDecimal>>> getPotentialFunding(
            @RequestParam(required = false) Set<UUID> companies) {
        var result = service.getPotentialFunding(companies);
        result = currencyService.convert(result);
        return ResponseEntity.ok().body(result);
    }

    @GetMapping("guaranteeFund")
    public ResponseEntity<Map<Currency, Pair<BigDecimal, BigDecimal>>> getGuaranteeFund(
            @RequestParam(required = false) Set<UUID> companies) {
        var result = service.getGuaranteeFund(companies);
        result = currencyService.convert(result);
        return ResponseEntity.ok().body(result);
    }

    @GetMapping("amountAtRisk")
    public ResponseEntity<Map<Currency, Pair<BigDecimal, BigDecimal>>> getAmountAtRisk(
            @RequestParam(required = false) Set<UUID> companies) {
        var result = service.getAmountAtRisk(companies);
        result = currencyService.convert(result);
        return ResponseEntity.ok().body(result);
    }

    @GetMapping("amountOutstanding")
    public ResponseEntity<Map<Currency, Pair<BigDecimal, BigDecimal>>> getAmountOutstanding(
            @RequestParam(required = false) Set<UUID> companies) {
        var result = service.getAmountOutstanding(companies);
        result = currencyService.convert(result);
        return ResponseEntity.ok().body(result);
    }

    @GetMapping("amountUnsecured")
    public ResponseEntity<Map<Currency, Pair<BigDecimal, BigDecimal>>> getAmountUnsecured(
            @RequestParam(required = false) Set<UUID> companies) {
        var result = service.getAmountUnsecured(companies);
        result = currencyService.convert(result);
        return ResponseEntity.ok().body(result);
    }

    @GetMapping("amountFiscalYearFees")
    public ResponseEntity<Map<Currency, Pair<BigDecimal, BigDecimal>>> getAmountFiscalYearFees(
            @RequestParam(required = false) Set<UUID> companies) {
        var result = service.getAmountFiscalYearFees(companies);
        result = currencyService.convert(result);
        return ResponseEntity.ok().body(result);
    }

    @GetMapping("contractSituation")
    public ResponseEntity<Map<Currency, Map<String, BigDecimal>>> getContractSituation(
            @RequestParam(required = false) Set<UUID> companies) {
        var result = service.getContractSituation(companies);
        result = currencyService.convertContractSituation(result);
        return ResponseEntity.ok().body(result);
    }

    @GetMapping("coverage")
    public ResponseEntity<Map<Currency, Pair<BigDecimal, BigDecimal>>> getCoverage(
            @RequestParam(required = false) Set<UUID> companies) {
        var result = service.getCoverage(companies);
        result = currencyService.convert(result);
        return ResponseEntity.ok().body(result);
    }

    @GetMapping("topBuyers")
    public ResponseEntity<Collection<Map<String, Object>>> getTopBuyers(
            @RequestParam(required = false) Set<UUID> companies) {
        var result = service.getTopBuyers(companies);
        result = currencyService.convertBuyers(result);
        return ResponseEntity.ok().body(result);
    }

    @GetMapping("topUnavailables")
    public ResponseEntity<Collection<Map<String, Object>>> getTopUnavailables(
            @RequestParam(required = false) Set<UUID> companies) {
        var result = service.getTopUnavailables(companies);
        result = currencyService.convertInvoices(result);
        return ResponseEntity.ok().body(result);
    }

    @GetMapping("excludedBuyers")
        public ResponseEntity<Map<Currency, Pair<BigDecimal, BigDecimal>>> getExcludedBuyers(
            @RequestParam(required = false) Set<UUID> companies
        ) {
            var result = service.getExcludedBuyers(companies);
            result = currencyService.convert(result);
            return ResponseEntity.ok().body(result);
        }


}
