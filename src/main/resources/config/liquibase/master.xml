<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">

    <property name="now" value="now()" dbms="h2"/>
    <property name="now" value="current_timestamp" dbms="postgresql"/>
    <property name="floatType" value="float4" dbms="postgresql, h2"/>
    <property name="floatType" value="float" dbms="mysql, oracle, mssql, mariadb"/>
    <property name="clobType" value="longvarchar" dbms="h2"/>
    <property name="clobType" value="clob" dbms="mysql, oracle, mssql, mariadb, postgresql"/>
    <property name="uuidType" value="uuid" dbms="h2, postgresql"/>
    <property name="datetimeType" value="datetime(6)" dbms="mysql, mariadb"/>
    <property name="datetimeType" value="datetime" dbms="oracle, mssql, postgresql, h2"/>

    <include file="config/liquibase/changelog/00000000000000_changelog.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/00000000000001_changelog.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/00000000000002_changelog.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/00000000000003_changelog.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/00000000000004_changelog.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/00000000000005_changelog.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/00000000000006_changelog.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/00000000000007_changelog.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/00000000000008_changelog.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/00000000000009_changelog.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/00000000000010_changelog.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/00000000000011_changelog.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/00000000000012_changelog.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/00000000000013_changelog.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/00000000000014_changelog.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/00000000000015_changelog.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/00000000000016_changelog.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/00000000000017_changelog.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/00000000000018_changelog.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/00000000000019_changelog.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/00000000000020_changelog.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/00000000000021_changelog.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/00000000000022_changelog.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/00000000000023_changelog.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/00000000000024_changelog.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/00000000000025_changelog.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/00000000000026_changelog.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/00000000000027_changelog.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/00000000000027_changeloga.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/00000000000027_changelogb.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/00000000000029_changelog.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20240201153050_changelog.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20240206220222_changelog.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20240208152042_changelog.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20240208153312_changelog.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20240208164143_changelog.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20240213182726_changelog.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20240215161046_factors.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20240226214517_spring_batch.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20240228180959_changelog.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20240305152325_changelog.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20240311171837_changelog.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20240313162447_changelog.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20240319152821_changelog.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20240322175729_changelog.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20240409135640_paymentDate.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20240424162329_added_entity_Cession.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20240516074207_bank_transaction_balance.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20240521151833_changelog.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20240607155318_bank_transaction_adjustments.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20240611083049_defaultOverDue.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20240626105939_auditing.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20240628113601_user_id_type_uuid.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20240702172200_roles.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20240702173903_user_companies.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20240711152543_user_org_id.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20240715120708_company_org_id.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20240725140619_added_entity_CategoryToAccount.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20240820162705_added_entity_FlowFile.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20240821192205_added_entity_FlowFileFailure.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20240822075331_FlowFile_attributes.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20240923093449_add_contract_country.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20240923154627_cession_flowFiles_and_metadata.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20240924103906_credit_limit_simplified.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20241004134445_category_to_account_currency.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20241015203033_user_preferences.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20241031110838_country_simplification.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20241106172643_added_entity_Comment.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20241118145158_misc.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20241118145833_company_buyer_code_exclusions.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20241121123355_company_buyer_code_exclusions_removal.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20241121184054_invoice_from_factor_missing_fields.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20241122091807_buyer_excluded.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20241122165943_buyer_exclusion_reason.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20241213205606_comment_users.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20241220104504_organization.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20241224145039_contract_cashin.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20241226180807_creditInsurancePolicy_buyerId_notUnique.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20241230113956_buyerFromInsurer.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20250106170109_company_fiscalYearStartMonth.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20250109170443_company_fiscalYearClosingMonth.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20250130101953_changelog.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20250131084941_contract_uniquePolicy.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20250205152104_contract_defaultCreditLimitAmount.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20250307174306_buyer_fromFactorUnknown.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20250310162631_db_synchro.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20250313170553_categoryToAccount_reinvoicing.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20250319055759_datastream.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20250319073218_datastream_failure.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20250319185740_gap.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20250324035708_blind_cover_amount.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20250404012828_buyer_from_insurer_codes.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20250404051855_creditrequest_code.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20250404091343_categoryToAccounts_renames.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20250411162855_envers_audit.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20250411164648_envers_extra_attributes.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20250416005407_last_request_code.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20250424064929_insurer-decision.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20250502020219_insurer-comment.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20250510162757_buyer_amount_cache.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20250530000000_migrate_guarantee_fund_categories.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20250610051420_raw_decision_buyer_from_insurer.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20250618120000_invoice_excluded.xml" relativeToChangelogFile="false"/>
    <!-- jhipster-needle-liquibase-add-changelog - JHipster will add liquibase changelogs here -->
    <!-- jhipster-needle-liquibase-add-constraints-changelog - JHipster will add liquibase constraints changelogs here -->
    <!-- jhipster-needle-liquibase-add-incremental-changelog - JHipster will add incremental liquibase changelogs here -->
</databaseChangeLog>
