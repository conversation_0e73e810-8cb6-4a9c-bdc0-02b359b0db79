<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext" xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="manu (generated)" id="1718708400000-1">
        <addColumn tableName="invoice">
            <column name="excluded" type="boolean" defaultValueBoolean="false">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet author="manu (generated)" id="1718708400000-2">
        <addColumn tableName="invoice">
            <column name="exclusion_reason" type="text"/>
        </addColumn>
    </changeSet>
    <changeSet author="manu (generated)" id="1750252964515-3">
        <addColumn tableName="invoice_aud">
            <column name="excluded" type="BOOLEAN"/>
        </addColumn>
    </changeSet>
    <changeSet author="manu (generated)" id="1750252964515-4">
        <addColumn tableName="invoice_aud">
            <column name="exclusion_reason" type="VARCHAR(255)"/>
        </addColumn>
    </changeSet>
</databaseChangeLog>
