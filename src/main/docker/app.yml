# This configuration is intended for development purpose, it's **your** responsibility to harden it for production
name: afakto
services:
  app:
    image: afakto
    environment:
      - _JAVA_OPTIONS=-Xmx512m -Xms256m
      - SPRING_PROFILES_ACTIVE=prod,api-docs
      - <PERSON>NAGEMENT_PROMETHEUS_METRICS_EXPORT_ENABLED=true
      - SPRING_DATASOURCE_URL=****************************************
      - SPRING_LIQUIBASE_URL=****************************************
      - SPRING_SECURITY_OAUTH2_CLIENT_PROVIDER_OIDC_ISSUER_URI=http://keycloak:9080/realms/jhipster
      - SPRING_SECURITY_OAUTH2_CLIENT_REGISTRATION_OIDC_CLIENT_ID=web_app
      - SPRING_SECURITY_OAUTH2_CLIENT_REGISTRATION_OIDC_CLIENT_SECRET=web_app
    ports:
      - 127.0.0.1:8080:8080
    healthcheck:
      test:
        - CMD
        - curl
        - -f
        - http://localhost:8080/management/health
      interval: 5s
      timeout: 5s
      retries: 40
    depends_on:
      postgresql:
        condition: service_healthy
      keycloak:
        condition: service_healthy
  postgresql:
    extends:
      file: ./postgresql.yml
      service: postgresql
  keycloak:
    extends:
      file: ./keycloak.yml
      service: keycloak
