/*
 * This file runs in a Node context (it's NOT transpiled by Babel), so use only
 * the ES6 features that are supported by your Node version. https://node.green/
 */
// Configuration for your app
// https://quasar.dev/quasar-cli/quasar-conf-js
import { defineConfig } from '#q-app/wrappers';

export default defineConfig(() => {
  // can be async too
  return {
    // https://quasar.dev/quasar-cli-vite/supporting-ts
    supportTS: true,

    // https://quasar.dev/quasar-cli-vite/prefetch-feature
    // preFetch: true,

    // app boot file (/src/boot)
    // --> boot files are part of "main.js"
    // https://quasar.dev/quasar-cli/boot-files
    boot: ['i18n', 'axios', 'stores', 'register-all-components', 'beforeUnload'],

    // https://quasar.dev/quasar-cli/quasar-conf-js#Property%3A-css
    css: [
      'app.scss',
      'buttons.scss',
      'cards.scss',
      'dashboard.scss',
      'dense.scss',
      'filters.scss',
      'form.scss',
      'layout.scss',
      'show.scss',
      'table.scss',
      '~flag-icons/css/flag-icons.min.css',
      '~@fontsource/noto-sans/400.css',
    ],

    // https://github.com/quasarframework/quasar/tree/dev/extras
    extras: [
      // 'ionicons-v4',
      // 'mdi-v5',
      // 'fontawesome-v5',
      // 'eva-icons',
      // 'themify',
      // 'line-awesome',
      // 'roboto-font-latin-ext', // this or either 'roboto-font', NEVER both!

      // 'roboto-font',
      'material-icons', // optional, you are not bound to it
      'material-icons-outlined',
    ],

    // Full list of options: https://quasar.dev/quasar-cli-vite/quasar-config-js#build
    build: {
      vueRouterMode: 'history', // available values: 'hash', 'history'

      distDir: '../../../target/classes/static', // Use distDir instead of outDir

      env: {
        APP_VERSION: process.env.APP_VERSION ? `v${process.env.APP_VERSION}` : 'DEV',
        BUILD_TIME: new Date().getTime(),
        SPA_CDN: process.env.SPA_CDN,
      },
    },

    // Full list of options: https://quasar.dev/quasar-cli/quasar-conf-js#Property%3A-devServer
    devServer: {
      port: 8100,
      proxy: {
        '^/(api|management|swagger-resources|v3/api-docs|swagger-ui|oauth2|login|auth)': {
          target: 'http://localhost:8080',
        },
      },
      vueDevtools: false,
    },

    // https://quasar.dev/quasar-cli-vite/quasar-config-js#framework
    framework: {
      config: {
        dark: 'auto', // or Boolean true/false
      },

      // iconSet: 'material-icons', // Quasar icon set
      // lang: 'en-US', // Quasar language pack

      // For special cases outside of where the auto-import strategy can have an impact
      // (like functional components as one of the examples),
      // you can manually specify Quasar components/directives to be available everywhere:
      //
      // components: [],
      // directives: [],

      // Quasar plugins
      plugins: ['Cookies', 'Dialog', 'LocalStorage', 'Notify', 'SessionStorage'],
    },

    // animations: 'all', // --- includes all animations
    // https://quasar.dev/options/animations
    animations: 'all',
  };
});
