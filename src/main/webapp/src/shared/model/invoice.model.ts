import { I<PERSON>uyer } from 'shared/model/buyer.model';
import { ICreditLimit } from 'shared/model/credit-limit.model';
import { InvoiceType } from 'shared/model/enumerations/invoice-type.model';

export type Invoice = {
  id?: number;
  buyer: IBuyer;
  type: InvoiceType;
  invoiceNumber: string;
  date: Date;
  dueDate: Date;
  currency: string;
  amount: number;
  balance: number;
  creditLimit?: ICreditLimit;
  excluded?: boolean;
  exclusionReason?: string;
  underFactor?: boolean;
};
