<template>
  <section class="col-md col-xs-12">
    <q-card v-if="Object.keys(history).length" class="dashboard-card">
      <q-tooltip class="bg-primary">{{ $t('afaktoApp.dashboard.historyTooltip') }}</q-tooltip>
      <q-card-section style="height: 92%">
        <h5 class="relative no-margin" style="font-size: 22px; line-height: 100%; font-weight: 600">
          {{ $t('afaktoApp.dashboard.history') }}
        </h5>
        <apexchart height="100%" type="bar" :options="options" :series="series" />
      </q-card-section>
    </q-card>
  </section>
</template>

<script setup>
import { api } from 'boot/axios';
import { getCssVar, useQuasar } from 'quasar';
import { onMounted, ref, watch } from 'vue';
import { useI18n } from 'vue-i18n';

import useNotifications from 'src/util/useNotifications';

const { n } = useI18n();
const { notifyError } = useNotifications();
const $q = useQuasar();

const options = {
  chart: {
    events: {
      mounted: chart => {
        chart.windowResizeHandler();
      },
    },
    defaultLocale: 'fr',
    id: 'history',
    locales: [
      {
        name: 'fr',
        options: {
          shortMonths: ['Jan', 'Fév', 'Mar', 'Avr', 'Mai', 'Juin', 'Juil', 'Aoû', 'Sep', 'Oct', 'Nov', 'Déc'],
        },
      },
      'en',
    ],
    name: 'history',
    toolbar: false,
  },
  dataLabels: {
    enabled: false,
    formatter: (value, { seriesIndex, w }) =>
      n(value, 'compact', {
        currency: w.config.series[seriesIndex].name,
        signDisplay: 'negative',
      }),
    style: {
      colors: [$q.dark.isActive ? 'white' : 'black'],
      fontWeight: 'normal',
    },
  },
  grid: {
    position: 'back',
    show: true,
    xaxis: {
      lines: {
        show: false,
      },
    },
    yaxis: {
      lines: {
        show: true,
      },
    },
  },
  legend: { show: false },
  plotOptions: {
    bar: {
      borderRadius: 4,
      colors: {
        ranges: [
          {
            from: -Infinity,
            to: 0,
            color: getCssVar('negative'),
          },
          {
            from: 0,
            to: Infinity,
            color: getCssVar('accent'),
          },
        ],
      },
    },
  },
  tooltip: {
    x: {
      show: false,
    },
    y: {
      formatter: (value, { seriesIndex, w }) =>
        n(value, {
          currency: w.config.series[seriesIndex].name,
          signDisplay: 'negative',
        }),
    },
  },
  xaxis: {
    axisBorder: { show: false },
    axisTicks: { show: false },
    type: 'datetime',
    labels: {
      datetimeFormatter: {
        month: 'MMM',
        day: '',
      },
    },
  },
  yaxis: {
    axisBorder: {
      show: true,
    },
    labels: {
      show: true,
      formatter: value => n(value, { signDisplay: 'negative' }),
    },
  },
  annotations: {
    yaxis: [
      {
        y: 0,
        strokeDashArray: 0,
        borderWidth: 1,
        borderColor: getCssVar('secondary'),
        offsetX: 0,
      },
    ],
  },
};

const history = ref({});
const series = ref([]);

const props = defineProps({
  companies: {
    type: Array,
    default: () => [],
  },
  selectedCurrency: {
    type: String,
    default: 'EUR',
  },
});

const updateSeries = selectedCurrency => {
  series.value = [];
  Object.entries(history.value)
    .filter(([currency]) => currency === selectedCurrency)
    .forEach(([currency, values]) => {
      series.value.push({
        name: currency,
        data: Object.entries(values),
      });
    });
};

onMounted(async () => {
  try {
    history.value = (
      await api.get('/api/dashboard/history', { params: { companies: props.companies.map(company => company.id).join(',') } })
    ).data;
    updateSeries(props.selectedCurrency);
  } catch (error) {
    notifyError(error);
  }
});

watch(
  () => props.selectedCurrency,
  newCurrency => updateSeries(newCurrency),
);
</script>
