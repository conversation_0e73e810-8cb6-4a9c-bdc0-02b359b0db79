<template>
  <q-drawer overlay show-if-above side="right">
    <q-card class="backgroundSecondary" flat>
      <q-card-section class="col q-col-gutter-y-sm">
        <div class="row justify-end">
          <q-btn class="buttonNeutral" icon="close" @click="$emit('close')"></q-btn>
        </div>
        <q-item-section class="q-gutter-y-sm ">
          <div>
            <q-item-label class="show-label">
              <q-icon name="file_copy" />
              <div>User</div>
            </q-item-label>
            <q-item-label>
              <h3>{{ modelValue?.firstName }} {{ modelValue?.lastName }}</h3>
            </q-item-label>
          </div>
        </q-item-section>
      </q-card-section>
      <q-list padding>
        <q-item>
          <q-item-section>{{ $t('afaktoApp.user.login') }}</q-item-section>
          <q-item-section avatar>{{ modelValue?.login }}</q-item-section>
        </q-item>
        <q-item>
          <q-item-section> {{ $t('afaktoApp.user.email') }}</q-item-section>
          <q-item-section avatar class="self-center">{{ modelValue?.email
            }}
          </q-item-section>
        </q-item>
        <q-item>
          <q-item-section>{{ $t('afaktoApp.user.authorities') }}</q-item-section>
          <div class="col">
            <q-item-section v-for="auth in modelValue?.authorities" avatar>
              {{ auth.slice(5) }} - {{ $t(`afaktoApp.authority.values.${auth}.help`) }}
            </q-item-section>
          </div>
        </q-item>
        <q-item>
          <q-item-section>{{ $t('afaktoApp.user.companies') }}</q-item-section>
          <div class="column q-py-sm ">
            <q-item-section class="items-end " v-for="company in modelValue?.companies" avatar>
              {{ company.name }}
            </q-item-section>
          </div>
        </q-item>
        <q-item>
          <q-item-section>{{ $t('afaktoApp.user.me.referenceCurrency') }}</q-item-section>
          <q-item-section avatar> {{ modelValue?.preferences?.REFERENCE_CURRENCY }}</q-item-section>
        </q-item>
      </q-list>

      <q-separator />
      <!--      <q-list>-->
      <!--        <q-expansion-item dense-toggle expand-separator label="History & Comments">-->
      <!--          <div class="q-pa-sm">-->
      <!--            <div></div>-->
      <!--            <div></div>-->
      <!--          </div>-->
      <!--        </q-expansion-item>-->

      <!--      </q-list>-->

      <!--      <entity-meta :entity="modelValue" />-->
    </q-card>
    <footer>
      <q-btn @click="() => router.push(`/users/${modelValue.id}`)" icon="edit" class="buttonBrand" label="Edit">

      </q-btn>
    </footer>
  </q-drawer>
</template>

<script setup>
import { useRouter } from 'vue-router';
import UserShow from './UserShow.vue';

const router = useRouter();


const props = defineProps({
  modelValue: Object
});


</script>
