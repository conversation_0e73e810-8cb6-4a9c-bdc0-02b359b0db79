<template>
  <div v-if="contract">
    <buyer-details-insurer-contract v-model="contract" :entity="modelValue.buyerFromInsurer" />

    <b-input
      v-if="modelValue.creditLimit"
      :label="$t('afaktoApp.buyer.creditLimit')"
      :model-value="$n(modelValue.creditLimit.amount, 'currencyCode', { currency: modelValue.creditLimit.currency })"
      class="q-mt-lg"
      input-class="text-right"
      readonly
    >
      <template #append>
        <q-icon v-if="modelValue.balance > modelValue.creditLimit.amount" color="warning" name="warning" size="md">
          <q-tooltip>{{ $t('afaktoApp.buyer.incoherentBalance_helper') }}</q-tooltip>
        </q-icon>
      </template>
      <entity-meta-dates v-if="modelValue.creditLimit" :entity="modelValue.creditLimit" />
    </b-input>

    <buyer-details-from-insurer v-if="modelValue.buyerFromInsurer" :contract="contract" :entity="modelValue"
                                @update:entity="emit('update:modelValue', $event)" />

    <credit-limit-requests-table :contract="contract" :entity="modelValue"
                                 @update:entity="emit('update:modelValue', $event)"
                                 @credit-limit-requested="creditLimitRequested" />
  </div>

  <!-- Contract Selector Dialog -->
  <q-dialog v-model="contractSelectDialog">
    <q-card>
      <q-toolbar>
        <div class="text-h6">{{ t('afaktoApp.buyer.contractSelectorDialog') }}</div>
        <q-space />
      </q-toolbar>
      <q-card-section>
        <q-list bordered separator>
          <q-item
            v-for="(option, idx) in contractSelectOptions"
            :key="idx"
            clickable
            @click="onContractSelected(option)"
          >
            <q-item-section>
              <div><strong>Currency:</strong> {{ option.financialInformation.currency }}</div>
              <div v-if="option.creditInsurance === 'EXTERNAL'">
                <div><strong>Policy ID:</strong> {{ option.creditInsurancePolicy.policyId }}</div>
                <div><strong>Provider:</strong> {{ option.creditInsurancePolicy.externalCreditInsurance.name }}</div>
              </div>
              <div v-else>
                <strong>Insurance:</strong> INTERNAL
              </div>
            </q-item-section>
          </q-item>
        </q-list>
      </q-card-section>
    </q-card>
  </q-dialog>
</template>

<script setup>
import { api } from 'boot/axios';
import { onMounted, ref } from 'vue';
import { useI18n } from 'vue-i18n';

import useNotifications from 'src/util/useNotifications';
import EntityMetaDates from 'pages/subcomponents/EntityMetaDates.vue';
import BuyerDetailsFromInsurer from './BuyerDetailsFromInsurer.vue';
import BuyerDetailsInsurerContract from './BuyerDetailsInsurerContract.vue';
import CreditLimitRequestsTable from '../credit-limit-request/CreditLimitRequestsTable.vue';
import { setupBeforeUnload } from 'src/util/formBeforeUnload.js';
import BuyerService from 'pages/entities/buyer/buyer.service.js';

const { t } = useI18n();
const { notifyError } = useNotifications();

const props = defineProps({
  modelValue: Object
});

const emit = defineEmits(['update:modelValue']);

const contract = ref(null);
const contractSelectDialog = ref(false);
const contractSelectOptions = ref([]);

onMounted(async () => {
  try {
    contract.value = await getBuyerContract(props.modelValue);
  } catch (error) {
    notifyError(error);
  }
});

function onContractSelected(option) {
  contract.value = option;
  contractSelectDialog.value = false;
}

async function getBuyerContract(buyer) {
  if (buyer.currency == null)
    return await getBuyerContractWithNoCurrency(buyer);
  return (await BuyerService.contract(buyer.id)).data;
}

async function getBuyerContractWithNoCurrency(buyer) {
  const contracts = await api.get('/api/contracts', {
    params: {
      'company.equals': buyer.company.id
    }
  });

  if (!contracts.data.length)
    throw ('No contract available'); // todo show message on front instead of notifyError

  if (contracts.data.length === 1)
    return contracts.data[0];

  contractSelectOptions.value = contracts.data;
  contractSelectDialog.value = true;
}

const creditLimitRequested = async creditLimit => {
  emit('update:modelValue', await BuyerService.get(props.modelValue.id)); // we update the buyer globally
  if ('ACCEPTED' !== creditLimit.status) return;

  // So that submit button will not change state
  const dirtyForm = Array.from(document.forms).find(form => form.isDirty);
  props.modelValue.creditLimit = creditLimit;
  if (!dirtyForm) setTimeout(() => setupBeforeUnload(t, document.forms[0], props.modelValue), 100);
};
</script>
