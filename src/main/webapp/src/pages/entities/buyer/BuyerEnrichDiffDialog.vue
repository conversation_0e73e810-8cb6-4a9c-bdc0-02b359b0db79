<template>
  <q-dialog :model-value="modelValue" persistent @update:model-value="val => $emit('update:modelValue', val)">
    <q-card style="min-width: 350px">
      <q-toolbar>
        <div class="text-h6">{{ t('afaktoApp.buyer.enrichedChangesTitle') }}</div>
        <q-space />
        <q-btn class="buttonNeutral" icon="close" to="#" @click="closeDialog">
          <q-tooltip>{{ $t('entity.action.close') }}</q-tooltip>
        </q-btn>
      </q-toolbar>

      <q-card-section v-if="countryDialog">
        <q-select
          v-model="newBuyer.address.country"
          :label="$t('afaktoApp.address.country')"
          :options="countryOptions" :rules="[required]" emit-value filled
          filter
          hide-bottom-space
          map-options
          même
          option-label="name"
          option-value="code"
          que
          @update:model-value="onCountrySelected"
        >
          <template v-if="newBuyer.address.country" #prepend>
            <span :class="`fi fi-${newBuyer.address.country}`"></span>
          </template>
          <template #option="scope">
            <q-item dense v-bind="scope.itemProps">
              <q-item-section side>
                <span :class="`fi fi-${scope.opt.code}`" />
              </q-item-section>
              <q-item-section>
                <q-item-label>{{ scope.opt.name }}</q-item-label>
              </q-item-section>
            </q-item>
          </template>
        </q-select>
      </q-card-section>

      <q-card-section v-else-if="selectEntityDialog && !loading">
        <buyer-enrich-selector :model-value="legalEntities" @selected-entity="selectEntity" />
      </q-card-section>

      <q-card-section v-else-if="!loading">
        <div
          v-if="(props.entity.address === newBuyer.address) || (props.entity.numberType !== newBuyer.numberType || props.entity.number !== newBuyer.number)">
          <div v-if="props.entity.numberType !== newBuyer.numberType || props.entity.number !== newBuyer.number"
               class="row justify-around items-center q-gutter-md q-mb-md">
            <div class="col">
              <div class="text-caption text-grey">{{ t('afaktoApp.buyer.enrichPreviousNumber') }}</div>
              <div class="text-body1">{{ props.entity.number }}</div>

              <q-badge v-if="
              props.entity.address?.country &&
              tm(`afaktoApp.NumberType.${props.entity.address.country.toUpperCase()}.${props.entity.numberType}`)?.label
          " color="blue"
              >{{ t(`afaktoApp.NumberType.${newBuyer.address.country.toUpperCase()}.${newBuyer.numberType}.label`) }}
                <q-tooltip>
                  {{ $t(`afaktoApp.NumberType.${newBuyer.numberType}`).replaceAll('_', ' ') }}
                </q-tooltip>
              </q-badge>
              <q-badge v-else color="blue">{{
                  $t(`afaktoApp.NumberType.${props.entity.numberType}`).replaceAll('_', ' ')
                }}
              </q-badge>
            </div>
            <div class="col-auto flex items-center">
              <q-icon class="text-grey-7" name="arrow_forward" size="md" />
            </div>
            <div class="col">
              <div class="text-caption text-grey">{{ t('afaktoApp.buyer.enrichNewNumber') }}</div>
              <div class="text-body1">{{ newBuyer.number }}</div>
              <q-badge color="blue"
              >{{ t(`afaktoApp.NumberType.${newBuyer.address.country.toUpperCase()}.${newBuyer.numberType}.label`) }}
                <q-tooltip>
                  {{ $t(`afaktoApp.NumberType.${newBuyer.numberType}`).replaceAll('_', ' ') }}
                </q-tooltip>
              </q-badge>
            </div>
          </div>
          <div class="row justify-around items-stretch q-gutter-md">
            <div class="col">
              <address-comp :model-value="props.entity.address" :readonly="true" />
            </div>

            <div class="col-auto flex items-center">
              <q-icon class="text-grey-7" name="arrow_forward" size="xl" />
            </div>

            <div class="col">
              <address-comp :model-value="enrichedAddress" :readonly="false" />
            </div>
          </div>
        </div>
        <div v-else class="text-center text-grey">
          {{ t('afaktoApp.buyer.noChanges') }}
        </div>
      </q-card-section>

      <q-card-section v-else> Loading...</q-card-section>

      <q-card-actions align="center">
        <q-btn
          :label="$t('entity.action.save')"
          :loading="loading"
          class="buttonBrand"
          color="primary"
          icon="label_important"
          label="Save"
          type="submit"
          @click="onSubmit(entity, enrichedAddress)"
        />
      </q-card-actions>
    </q-card>
  </q-dialog>
</template>

<script setup>
import { useI18n } from 'vue-i18n';
import { onMounted, ref } from 'vue';

import BuyerService from 'pages/entities/buyer/buyer.service';
import AddressComp from 'pages/subcomponents/AddressComp.vue';
import useNotifications from 'src/util/useNotifications';
import BuyerEnrichSelector from 'pages/entities/buyer/BuyerEnrichSelector.vue';
import { required } from 'components/rules/index.js';
import countries from 'flag-icons/country.json';

const { notifyError } = useNotifications();
const { t, tm } = useI18n();

const closeDialog = () => emit('update:modelValue', false);
const emit = defineEmits(['update:modelValue']);
const loading = ref(false);

const props = defineProps({
  modelValue: Boolean,
  entity: Object,
  countryFilter: Object,
  identifierType: Object
});

const newBuyer = ref({ ...props.entity });

const countryDialog = ref(false);
const selectEntityDialog = ref(false);

const legalEntities = ref([]);

const countryOptions = ref(countries.filter(c => c.iso));
const enrichedAddress = ref({});


onMounted(async () => {
  if (await (testOnLegalName()))
    return;

  if (await (testOnLegalNumber()))
    return;

  closeDialog();
});

async function testOnLegalName() {
  if (hasLegalNumber())
    return false;

  if (newBuyer.value.address?.country) {
    await searchLegalEntity();
    return true;
  }
  countryDialog.value = true;
  return true;
}

async function testOnLegalNumber() {
  const countryKeys = Object.keys(props.countryFilter);

  if (Object.keys(props.identifierType).length) { // already number type detected
    newBuyer.value.address.country = props.identifierType.country;
    newBuyer.value.numberType = props.identifierType.type;
    await searchLegalEntity();
    return true;
  } else if (countryKeys.length > 1) { // various number type detected
    countryOptions.value = countries.filter(c =>
      countryKeys.includes(c.code)
    );
    countryDialog.value = true;
    return true;
  }

  return false;
}

async function onCountrySelected() {
  countryDialog.value = false;
  if (hasLegalNumber())
    newBuyer.value.numberType = props.countryFilter[newBuyer.value.address.country].type;
  await searchLegalEntity();
}

async function searchLegalEntity() {
  loading.value = true;

  try {
    const legalEntitiesSearch = await BuyerService.search(newBuyer.value);
    if (!legalEntitiesSearch.data.buyers.length) {
      notifyError(t(`afaktoApp.buyer.enrichmentErrors.NOT_FOUND`));
      return closeDialog();
    }

    if (legalEntitiesSearch.data.buyers.length === 1)
      selectEntity(legalEntitiesSearch.data.buyers[0]);
    else if (legalEntitiesSearch.data.buyers.length > 1) {
      selectEntityDialog.value = true;
      legalEntities.value = legalEntitiesSearch.data.buyers;
    }

    loading.value = false;
  } catch (err) {
    if (err.response?.status === 400 && err.response.data?.errorCode)
      notifyError(t(`afaktoApp.buyer.enrichmentErrors.${err.response.data.errorCode}`));
    else
      notifyError(err);
    closeDialog();
  }
}

const hasLegalNumber = () => props.entity?.number?.length > 3;

const overrideLegalEntityNumber = (enrichedBuyer) => {
  if (hasLegalNumber()) return;
  newBuyer.value.numberType = enrichedBuyer.numberType;
  newBuyer.value.number = enrichedBuyer.number;
};

function selectEntity(selectedEntity) {
  enrichedAddress.value = selectedEntity.address || {};
  overrideLegalEntityNumber(selectedEntity);

  selectEntityDialog.value = false;
}

const onSubmit = async (entity, enrichedAddress) => {
  loading.value = true;

  entity.address = enrichedAddress;
  entity.number = newBuyer.value.number;
  entity.numberType = newBuyer.value.numberType;

  BuyerService.save(entity)
    .catch(error => notifyError(error))
    .finally(() => {
      loading.value = false;
      closeDialog();
    });
};
</script>
