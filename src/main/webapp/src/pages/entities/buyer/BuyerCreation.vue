<template>
  <q-dialog persistent @update:model-value="val => $emit('update:modelValue', val)">
    <q-card class="column no-wrap" style="min-width: 800px; height: 800px">
      <q-toolbar class="bg-animate q-py-md bg-backgroundSecondary">
        <h5 style="margin: 0">{{ $t('afaktoApp.buyer.home.titleCreate') }}</h5>
        <q-space />

        <q-btn class="buttonNeutral" icon="close" to="#" @click="closeDialog">
          <q-tooltip>{{ $t('entity.action.close') }}</q-tooltip>
        </q-btn>
      </q-toolbar>

      <div class="row no-wrap fit">
        <div style="width: 30%; flex-shrink: 0">
          <q-stepper ref="stepper" v-model="tab" animated class="fit" color="primary" style="border-radius: 0px"
                     vertical>
            <q-step :name="'main'" :title="t('afaktoApp.buyer.create.basicInformation')" prefix="1" />
            <q-step :name="'buyerDetails'" :title="t('afaktoApp.buyer.create.buyerDetails')" prefix="2" />
          </q-stepper>
        </div>

        <q-tab-panels v-model="tab" :vertical="$q.platform.is?.desktop" animated style="width: 100%">
          <q-tab-panel name="main">
            <q-form ref="formRef" style="display: contents">
              <h6 style="margin-top: 1em; margin-bottom: 0">{{ t('afaktoApp.buyer.create.basicInformation') }}</h6>
              <!--                    <p class="text-green">Let's start with some basic information</p>-->
              <!--                    <div class="enrich-tooltip">-->
              <!--                      <p>-->
              <!--                        {{ $t('afaktoApp.buyer.create.enrichTooltip') }}-->
              <!--                      </p>-->
              <!--                    </div>-->
              <div class="q-col-gutter-md">
                <q-select
                  id="company-select"
                  v-model="entity.company"
                  :label="entity.company ? '' : $t('afaktoApp.buyer.company')"
                  :options="useAuthenticationStore().account.companies"
                  :rules="[required]"
                  class="q-pa-none"
                  filled
                  hide-bottom-space
                  option-label="name"
                  option-value="id"
                />

                <b-input
                  v-model="identifier"
                  :label="$t('afaktoApp.buyer.identifier')"
                  hide-bottom-space
                  @update:model-value="checkIdentifierInput"
                />

                <div v-if="identifierType?.label">
                  <q-badge color="blue">{{ identifierType.label }}</q-badge>
                </div>
                <div v-else-if="!identifierType?.label && !Object.keys(countryFilter).length">
                  <q-badge color="blue">Search by name</q-badge>
                </div>
                <div v-else>Multiple country detected for this number, please choose one</div>
                <q-select
                  id="country-select"
                  v-model="entity.address.country"
                  :label="entity.address.country ? '' : $t('afaktoApp.contract.country')"
                  :options="filteredCountries"
                  :rules="[required]"
                  emit-value
                  filled
                  hide-bottom-space
                  map-options
                  option-label="name"
                  option-value="code"
                  @update:model-value="checkCountry"
                >
                  <template v-if="entity.address.country" #prepend>
                    <span :class="`fi fi-${entity.address.country}`"></span>
                  </template>
                  <template #option="scope">
                    <q-item dense v-bind="scope.itemProps">
                      <q-item-section side>
                        <span :class="`fi fi-${scope.opt.code}`" />
                      </q-item-section>
                      <q-item-section>
                        <q-item-label>{{ scope.opt.name }}</q-item-label>
                      </q-item-section>
                    </q-item>
                  </template>
                </q-select>
                <!--                <b-selector-->
                <!--                  v-model="entity.address.country"-->
                <!--                  class="required"-->
                <!--                  :label="entity.address.country ? '' : $t('afaktoApp.contract.country')"-->
                <!--                  :option-label="'name'"-->
                <!--                  :option-value="'code'"-->
                <!--                  :rules="[required]"-->
                <!--                  :options-preload="true"-->
                <!--                  @update:modelValue="checkCountry"-->
                <!--                  :static-options="filteredCountries"-->
                <!--                >-->
                <!--                <template #prepend v-if="entity.address.country">-->
                <!--                  <span :class="`fi fi-${entity.address.country}`"></span>-->
                <!--                </template>-->
                <!--                <template #option="scope">-->
                <!--                  <q-item v-bind="scope.itemProps" dense>-->
                <!--                    <q-item-section side>-->
                <!--                      <span :class="`fi fi-${scope.opt.code}`" />-->
                <!--                    </q-item-section>-->
                <!--                    <q-item-section>-->
                <!--                      <q-item-label>{{ scope.opt.name }}</q-item-label>-->
                <!--                    </q-item-section>-->
                <!--                  </q-item>-->
                <!--                </template>-->
                <!--                </b-selector>-->
                <div class="row justify-end">
                  <q-btn color="primary" icon="auto_fix_high" @click.stop="getLegalEntity()" />
                  <q-btn color="secondary" label="Manual" @click="goToNextStep()" />
                </div>
                <div v-if="searchLegalEntity === false">
                  <buyer-enrich-selector :model-value="legalEntities" @selected-entity="copyEntity" />
                </div>
                <div v-else-if="searchLegalEntity === true">Loading...</div>
                <!--                <div class="row justify-end">-->
                <!--                  <q-btn color="secondary" label="Manual" @click="goToNextStep()" />-->
                <!--                </div>-->
              </div>
            </q-form>
          </q-tab-panel>

          <q-tab-panel name="buyerDetails">
            <q-form ref="formRef" style="display: contents">
              <b-input v-model="entity.code" :label="$t('afaktoApp.buyer.code')" :rules="[required]" class="required"
                       hide-bottom-space />
              <q-input
                v-model="entity.number"
                :label="$t('afaktoApp.buyer.number')"
                :rules="[required]"
                class="required"
                filled
                unmasked-value
              >
                <template #prepend>
                  <q-select
                    v-model="entity.numberType"
                    :label="$t('afaktoApp.buyer.numberType')"
                    :options="numberTypeOptions"
                    :rules="[required]"
                    bg-color="transparent"
                    class="required"
                    emit-value
                    map-options
                    option-label="label"
                    option-value="value"
                    style="min-width: 12em"
                  />
                </template>
              </q-input>

              <b-toggle v-model="entity.excluded"
                        :label="$t('afaktoApp.buyer.excluded') + ' - ' + $t('afaktoApp.buyer.excluded_help')" />
              <q-icon color="warning" name="remove_done" right size="sm" style="vertical-align: baseline" />
              <q-input
                v-if="entity.excluded"
                v-model="entity.exclusionReason"
                :label="t('afaktoApp.buyer.exclusionReason')"
                filled
                rows="2"
                type="textarea"
              />

              <address-comp v-model="entity.address" />
              <contact-comp v-model="entity.contact" />
            </q-form>
          </q-tab-panel>
        </q-tab-panels>
      </div>

      <div class="q-pa-sm row justify-between items-center q-gutter-sm bg-backgroundSecondary" style="margin-top: auto">
        <q-btn :label="$t('entity.action.cancel')" class="buttonNeutral bg-neutralLowest" to="#" @click="closeDialog" />

        <!-- Right side: step buttons and save -->
        <div class="q-gutter-sm">
          <q-btn :disable="isFirstStep()" class="bg-neutralLowest" label="<" @click="goToPreviousStep" />
          <q-btn :disable="isLastStep()" class="bg-neutralLowest" label=">" @click="goToNextStep" />
          <q-btn :disable="!isLastStep()" :label="$t('entity.action.save')" class="buttonBrand" type="submit"
                 @click="goToNextStep" />
        </div>
      </div>
    </q-card>
  </q-dialog>
</template>

<script setup>
import { useQuasar } from 'quasar';
import { computed, ref } from 'vue';
import { useI18n } from 'vue-i18n';
import { useRoute } from 'vue-router';

import BuyerService from 'pages/entities/buyer/buyer.service';
import AddressComp from 'pages/subcomponents/AddressComp.vue';
import ContactComp from 'pages/subcomponents/ContactComp.vue';
import { required } from 'src/components/rules';
import { useAuthenticationStore } from 'src/stores/authentication-store';
import { findIdType } from 'src/util/findIdType.js';
import useNotifications from 'src/util/useNotifications';
import BuyerEnrichSelector from 'pages/entities/buyer/BuyerEnrichSelector.vue';

const { notifyError } = useNotifications();
const $q = useQuasar();
const route = useRoute();
const { t, tm } = useI18n();
const tab = ref(route.query.tab || 'main');
const emit = defineEmits(['update:modelValue']);

const props = defineProps({
  entity: Object
});

const formRef = ref(null);

const stepNames = ['main', 'dataEnrichment', 'buyerDetails'];

const { identifier, identifierType, countryFilter, filteredCountries, evaluateIdentifier, checkCountry } = findIdType(
  props.entity,
  useI18n()
);

const checkIdentifierInput = async inputValue => {
  await evaluateIdentifier(inputValue);
  if (identifierType.value.country) props.entity.address.country = identifierType.value.country;
  else if (Object.keys(countryFilter.value).length > 1) props.entity.address.country = null;
};

const searchLegalEntity = ref(null);
const legalEntities = ref([]);

async function getLegalEntity() {
  if (!identifier.value) return;

  if (identifierType.value?.type) {
    props.entity.number = identifier.value;
    props.entity.numberType = identifierType.value.type;
  } else {
    props.entity.name = identifier;
    props.entity.number = null;
    props.entity.numberType = null;
  }

  searchLegalEntity.value = true;
  legalEntities.value = [];

  try {
    const enrichedBuyers = await BuyerService.search(props.entity);
    legalEntities.value = enrichedBuyers.data.buyers;
  } catch (err) {
    if (err.response?.status === 400 && err.response.data?.errorCode)
      notifyError(t(`afaktoApp.buyer.enrichmentErrors.${err.response.data.errorCode}`));
    else notifyError(err);
  }
  searchLegalEntity.value = false;
}

const copyEntity = row => {
  props.entity.address = row.address;
  props.entity.number = row.number;
  props.entity.numberType = row.numberType;
  props.entity.name = row.name;
  goToNextStep();
};

const onSubmit = () => {
  BuyerService.save(props.entity)
    .then(() => closeDialog())
    .catch(error => notifyError(error));
};

const closeDialog = () => emit('update:modelValue', false);

const stepper = ref(null);

const isFirstStep = () => stepNames.indexOf(tab.value) === 0;
const isLastStep = () => stepNames.indexOf(tab.value) === stepNames.length - 1;

async function goToNextStep() {
  if (formRef.value?.validate) {
    const valid = await formRef.value.validate();
    if (!valid) return;
  }

  if (isLastStep()) return onSubmit();

  stepper.value?.next();
}

function goToPreviousStep() {
  if (isFirstStep()) return;

  stepper.value?.previous();
}

const numberTypeOptions = computed(() => {
  const countryCode = props.entity?.address?.country.toUpperCase();
  if (countryCode === null) return [];
  const countryNumberTypes = tm(`afaktoApp.NumberType.${countryCode}`);

  if (!countryNumberTypes || typeof countryNumberTypes !== 'object') return [];

  return Object.entries(countryNumberTypes).map(([key, value]) => ({
    label: value.label,
    value: key
  }));
});

</script>
<style>

.enrich-div {
  border: solid 1px $borderPrimary;
  border-radius: 8px;
}
</style>
