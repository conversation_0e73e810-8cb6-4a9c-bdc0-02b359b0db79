<template>
  <div>
    <div class="row q-gutter-sm">
      <div v-for="col in columns" :key="col.name" class="col text-center">
        <template v-if="col.name === 'creditInsurancePolicy.externalCreditInsurance.name'">
          <div>
            <div>
              <strong>{{ col.label }}</strong>
            </div>
            <q-tooltip>{{ col.field }}</q-tooltip>
            <img
              :alt="col.field"
              :src="'/insurers/' + col.field + '.png'"
              style="height: 1.5rem"
            />
          </div>
        </template>
        <template v-else>
          <div>
            <q-tooltip v-if="col.tooltip">{{ col.tooltip }}</q-tooltip>
            <strong>{{ col.label }}</strong>
          </div>
          <span>
            {{ col.format ? col.format(col.field) : col.field }}
          </span>
        </template>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue';
import { useI18n } from 'vue-i18n';

const { t } = useI18n();

const columns = computed(() => [
  {
    field: insurerName.value,
    label: t('afaktoApp.externalCreditInsurance.home.title'),
    name: 'creditInsurancePolicy.externalCreditInsurance.name',
    sortable: true
  },
  {
    field: props.modelValue.creditInsurancePolicy?.policyId,
    label: t('afaktoApp.externalCreditInsurance.policy.policyId'),
    name: 'creditInsurancePolicy.policyId'
  },
  {
    field: props.entity?.insurerCode,
    label: t('afaktoApp.externalCreditInsurance.detail.insurerCodeTitle'),
    tooltip: insurerName?.value
      ? t(`afaktoApp.externalCreditInsurance.detail.insurerCode.${insurerName.value}`)
      : null,
    name: 'buyerFromInsurer.insurerCode'
  },
  {
    field: props.modelValue.creditInsurancePolicy?.blindCoverAmount,
    style: 'currency',
    format: (value) => {
      if (value == null) return '';

      return new Intl.NumberFormat('fr-FR', {
        style: 'currency',
        currency: props.modelValue.financialInformation?.currency || 'EUR',
        currencyDisplay: 'symbol'
      }).format(value);
    },
    label: t('afaktoApp.externalCreditInsurance.policy.blindCoverAmount'),
    name: 'creditInsurancePolicy.blindCoverAmount'
  }
].filter(col => col.field != null));

const insurerName = computed(() => {
  return props.modelValue.creditInsurancePolicy?.externalCreditInsurance?.name?.toUpperCase() || null;
});

const props = defineProps({
  modelValue: Object, // contract
  entity: Object // buyerFromInsurer
});

</script>
