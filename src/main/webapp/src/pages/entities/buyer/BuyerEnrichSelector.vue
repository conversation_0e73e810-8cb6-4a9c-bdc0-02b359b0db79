<template>
  <div class="row items-center justify-around q-gutter-md q-mb-sm">
    <div>{{ filteredLegalEntities.length }} companies found</div>
    <q-input v-model="postalCodeFilter" debounce="300" dense filled label="Filter by Postal Code"
             style="max-width: 250px" />
  </div>

  <q-table
    v-model:pagination="pagination"
    :columns="[]"
    :filter="postalCodeFilter"
    :filter-method="filterMethod"
    :rows="modelValue"
    :rows-per-page-options="[5, 10, 0]"
    hide-header
    row-key="number"
  >
    <template #body="props">
      <q-tr :props="props">
        <q-td class="no-padding">
          <q-card
            bordered
            class="q-py-sm q-px-md bg-brandLowest bg-brandMedium full-width"
            style="cursor: pointer; border-radius: 0 !important;"
            @click="$emit('selected-entity', props.row)"
          >
            <div class="text-neutralHighest q-mb-sm text-weight-bolder" style="font-size: 18px">
              {{ props.row.name }}
            </div>
            <div class="text-subtitle2 text-neutralHigher">
              <div class="flex items-center q-gutter-x-xs">
                <div>
                  <q-icon class="text-h6" name="description" />
                  {{ props.row.number }}
                </div>
              </div>
            </div>
            <div class="text-subtitle2 text-neutralHigher">
              <div class="flex items-center q-gutter-x-xs overflow-hidden no-wrap ellipsis">
                <q-icon class="text-h6" name="home" />
                <div>
                  {{ props.row.address?.streetName }},
                  {{ props.row.address?.city }},
                  {{ props.row.address?.postalCode }}
                </div>
              </div>
            </div>
          </q-card>
        </q-td>
      </q-tr>
    </template>
  </q-table>
</template>

<script setup>

import { computed, ref } from 'vue';

const emit = defineEmits(['selected-entity']);

const postalCodeFilter = ref('');

const pagination = ref({
  page: 1,
  rowsPerPage: 5
});

const filterMethod = (rows, terms) => {
  if (!terms) return rows;
  return rows.filter(row => {
    return row.address?.postalCode?.toString().toLowerCase().includes(terms.toLowerCase());
  });
};

const props = defineProps({
  modelValue: Array // legalEntities
});

const filteredLegalEntities = computed(() => {
  if (!postalCodeFilter.value) return props.modelValue;
  return props.modelValue.filter(entity => entity.address?.postalCode?.toString().startsWith(postalCodeFilter.value));
});

</script>
