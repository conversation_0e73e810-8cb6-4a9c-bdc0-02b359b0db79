<template>
  <q-toolbar>
    <q-space />
    <q-btn v-if="hasRoleAdmin" icon="loupe" label="Raw Decision" @click="showRawDecisionData = true" />
    <q-btn v-if="hasRoleAdmin" icon="loupe" label="Raw" @click="showRawData = true" />
    <q-btn v-if="hasRoleWriter" :label="$t('entity.action.update')" icon="sync" @click="updateCreditLimit" />
  </q-toolbar>

  <q-dialog v-model="showRawData">
    <q-card>
      <q-card-section>
        <q-btn class="float-right" color="primary" icon="close" @click="showRawData = false" />
        <div class="text-h3">Raw Data</div>
      </q-card-section>

      <json-viewer :model-value="entity.buyerFromInsurer.raw" />
    </q-card>
  </q-dialog>
  <q-dialog v-model="showRawDecisionData">
    <q-card>
      <q-card-section>
        <q-btn class="float-right" color="primary" icon="close" @click="showRawDecisionData = false" />
        <div class="text-h3">Raw Decision Data</div>
      </q-card-section>

      <json-viewer :model-value="entity.buyerFromInsurer.rawDecision" />
    </q-card>
  </q-dialog>

  <buyer-details-from-insurer-atradius
    v-if="entity.buyerFromInsurer.insurerName === 'atradius' && entity.buyerFromInsurer.raw"
    :model-value="entity.buyerFromInsurer.raw"
  />
  <buyer-details-from-insurer-coface
    v-if="entity.buyerFromInsurer.insurerName === 'coface' && entity.buyerFromInsurer.raw"
    :contract="contract"
    :model-value="entity"
  />

  <q-inner-loading :showing="updating">
    <q-spinner-gears color="primary" size="10em" />
  </q-inner-loading>
</template>

<script setup>
import { api } from 'boot/axios';
import { useQuasar } from 'quasar';
import { ref } from 'vue';
import { useI18n } from 'vue-i18n';
import { useRoute } from 'vue-router';

import JsonViewer from 'pages/subcomponents/JsonViewer.vue';
import { useAuthenticationStore } from 'src/stores/authentication-store';
import { useInsurerCreditLimitStore } from 'src/stores/insurer-credit-limit-store.ts';
import useNotifications from 'src/util/useNotifications';

import BuyerDetailsFromInsurerAtradius from './BuyerDetailsFromInsurerAtradius.vue';
import BuyerDetailsFromInsurerCoface from './BuyerDetailsFromInsurerCoface.vue';

const baseApiUrl = '/api/buyers';
const hasRoleWriter = useAuthenticationStore().hasRoleWriter;
const hasRoleAdmin = useAuthenticationStore().hasRoleAdmin;
const { notifyError } = useNotifications();
const $q = useQuasar();
const route = useRoute();
const showRawData = ref(false);
const showRawDecisionData = ref(false);
const { t } = useI18n();
const updating = ref(false);

const emit = defineEmits(['update:entity']);

const props = defineProps({
  entity: Object,
  contract: Object
});

const updateCreditLimit = async () => {
  updating.value = true;
  try {
    const response = await api.post(`${baseApiUrl}/${route.params.id}/updateCreditLimit`);

    const updated = props.entity?.creditLimit?.version !== response.data?.creditLimit?.version;
    emit('update:entity', response.data); // update the entity globally

    if (updated)
      $q.notify({
        color: 'positive',
        icon: 'sync',
        message: t('afaktoApp.buyer.buyerFromInsurer.updatedCreditLimit')
      });
    else
      $q.notify({
        color: 'info',
        icon: 'sync',
        message: t('afaktoApp.buyer.buyerFromInsurer.noChange')
      });

    useInsurerCreditLimitStore().markUpdated();
  } catch (error) {
    notifyError(error);
  } finally {
    updating.value = false;
  }
};
</script>
