<template>
  <q-page>
    <q-toolbar>
      <h1>{{ $t('afaktoApp.contract.home.title') }}</h1>

      <q-space />

      <div class="buttonDiv">
        <rows-export
          class="buttonNeutral"
          :base-api-url="baseApiUrl"
          :columns="columns"
          :filters="filters"
          :pagination="pagination"
          :visible-columns="visibleColumns"
        />

        <div class="btn-separator" />

        <q-btn class="buttonBrand" v-if="hasRoleConfig" icon="add" :label="$t('afaktoApp.contract.home.createLabel')" to="/contracts/new" />
      </div>
    </q-toolbar>

    <q-toolbar>
      <columns-filtering v-model="filters" :columns="columns.filter(col => col.filter)" />
      <columns-visibility v-model="visibleColumns" :columns="columns" />
    </q-toolbar>

    <q-table
      v-model:pagination="pagination"
      binary-state-sort
      :columns="columns"
      row-key="id"
      :rows="rows"
      :rows-per-page-options="[0]"
      :visible-columns="visibleColumns"
      @request="onRequest"
      @row-click="(_event, { id }) => router.push(`/contracts/${id}`)"
    />
  </q-page>
</template>

<script setup>
import { api } from 'boot/axios';
import { computed, ref, watch } from 'vue';
import { useI18n } from 'vue-i18n';
import { useRoute, useRouter } from 'vue-router';

import { useAuthenticationStore } from 'src/stores/authentication-store';
import { filtersToQueryParams } from 'src/util/filtering';
import { format } from 'src/util/format';
import useNotifications from 'src/util/useNotifications';

const baseApiUrl = '/api/contracts';
const hasRoleConfig = useAuthenticationStore().hasRoleConfig;
const { notifyError } = useNotifications();
const route = useRoute();
const router = useRouter();
const { n, t } = useI18n();
const store = useAuthenticationStore();

const pagination = ref({
  sortBy: route.query.sortBy || 'activationDate',
  descending: route.query.descending !== 'false',
  page: Number.parseInt(route.query.page || 1),
  rowsPerPage: store._account.preferences.ROWS_PER_PAGE || 25,
  rowsNumber: 15,
});

const columns = computed(() => [
  {
    align: 'left',
    field: row => row.company?.name,
    filter: {
      field: 'company',
      type: 'enum',
      values: useAuthenticationStore().account.companies.map(company => ({ value: company.id, label: company.name })),
    },
    label: t('afaktoApp.contract.company.name'),
    name: 'company.name',
    sortable: true,
  },
  {
    field: row => row.financialInformation?.currency,
    filter: { type: 'currency' },
    label: t('afaktoApp.invoice.currency'),
    name: 'financialInformation_currency',
    sortable: true,
  },
  {
    align: 'left',
    field: row => row.factorInstitution?.name,
    filter: {
      field: 'factorInstitution',
      optionLabel: 'name',
      optionValue: 'id',
      type: 'select',
      url: '/api/factor-institutions?sort=name',
    },
    label: t('afaktoApp.contract.factorInstitution.name'),
    name: 'factorInstitution.name',
    sortable: true,
  },
  {
    align: 'left',
    field: 'contractNumber',
    filter: { type: 'text' },
    label: t('afaktoApp.contract.contractNumber'),
    name: 'contractNumber',
    sortable: true,
  },
  {
    align: 'left',
    field: 'factorAccountNumber',
    filter: { type: 'text' },
    label: t('afaktoApp.contract.factorAccountNumber'),
    name: 'factorAccountNumber',
    sortable: true,
  },
  {
    align: 'left',
    field: 'programType',
    filter: { type: 'enum' },
    format: (value, _row) => (value == null ? '' : t(`afaktoApp.contract.programType_${value}`)),
    label: t('afaktoApp.contract.programType'),
    name: 'programType',
    sortable: true,
  },
  {
    field: row => row.financialInformation?.guaranteeLine,
    filter: { type: 'number' },
    format: (value, row) => (value == null ? '' : n(value, 'currency', { currency: row.financialInformation?.currency })),
    label: t('afaktoApp.contract.financialInformation.guaranteeLine'),
    name: 'financialInformation_guaranteeLine',
    sortable: true,
  },
  {
    field: row => row.financialInformation?.nonGuaranteeLine,
    filter: { type: 'number' },
    format: (value, row) => (value == null ? '' : n(value, 'currency', { currency: row.financialInformation?.currency })),
    label: t('afaktoApp.contract.financialInformation.nonGuaranteeLine'),
    name: 'financialInformation_nonGuaranteeLine',
    sortable: true,
  },
  {
    align: 'left',
    field: row => row.creditInsurancePolicy?.externalCreditInsurance?.name,
    filter: {
      field: 'insurer',
      optionLabel: 'name',
      optionValue: 'id',
      type: 'select',
      url: '/api/external-credit-insurances?sort=name',
    },
    hidden: true,
    label: t('afaktoApp.contract.creditInsurance.name'),
    name: 'creditInsurancePolicy.externalCreditInsurance.name',
    sortable: true,
  },
  {
    field: row => row.creditInsurancePolicy?.blindCoverAmount,
    filter: { type: 'number' },
    format: (value, row) => (value == null ? '' : n(value, 'currency', { currency: row.financialInformation?.currency })),
    hidden: true,
    label: t('afaktoApp.externalCreditInsurance.policy.blindCover'),
    name: 'creditInsurancePolicy_blindCoverAmount',
    sortable: true,
  },
  {
    align: 'left',
    field: 'activationDate',
    filter: { type: 'date' },
    format: value => value && format(value),
    label: t('afaktoApp.contract.activationDate'),
    name: 'activationDate',
    sortable: true,
  },
]);

const visibleColumns = ref([]);
const filters = ref({});
watch(filters, () => onRequest({ pagination: pagination.value }), { deep: true });

const rows = ref([]);

const onRequest = async props => {
  const { page, rowsPerPage, sortBy, descending } = props.pagination;
  try {
    const response = await api.get(baseApiUrl, {
      params: {
        page: page - 1,
        size: rowsPerPage === 0 ? pagination.value.rowsNumber : rowsPerPage,
        sort: `${sortBy},${descending ? 'desc' : 'asc'}`,
        ...filtersToQueryParams(filters.value),
      },
    });
    rows.value = response.data;

    pagination.value = { rowsNumber: response.headers['x-total-count'], page, rowsPerPage, sortBy, descending };
    router.replace({ query: { page, sortBy, descending, rowsPerPage } });
  } catch (error) {
    notifyError(error);
  }
};
</script>
