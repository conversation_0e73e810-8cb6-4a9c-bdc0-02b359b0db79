<template>
  <q-drawer overlay show-if-above side="right">
    <q-card class="backgroundSecondary" flat>
      <q-card-section class="col q-col-gutter-y-sm">
        <div class="row justify-end">
          <q-btn class="buttonNeutral" icon="close" @click="$emit('close')"></q-btn>
        </div>
        <q-item-section class="q-gutter-y-sm ">
          <div>
            <q-item-label class="show-label">
            <q-icon name="file_copy" />
            <div>{{ modelValue?.type.replace(/_/g, ' ') }}</div>
          </q-item-label>
          <q-item-label>
            <h3>{{ modelValue?.name }}</h3>
          </q-item-label>
          </div>
          <q-item-label>
            <q-btn icon="o_file_download" class="buttonNeutral" @click="downloadFile(modelValue)" :label="$t(`entity.action.download`)" />
          </q-item-label>
        </q-item-section>
        <!--      Download button -->
      </q-card-section>
      <q-list padding>
        <q-item>
          <q-item-section>Path</q-item-section>
          <q-item-section avatar>{{ modelValue?.path }}</q-item-section>
        </q-item>
        <q-item>
          <q-item-section> Inserts</q-item-section>
          <q-item-section avatar>{{ $n(modelValue?.inserts) }}</q-item-section>
        </q-item>
        <q-item>
          <q-item-section>Updates</q-item-section>
          <q-item-section avatar>{{ modelValue?.updates }}</q-item-section>
        </q-item>
        <q-item>
          <q-item-section>Updates</q-item-section>
          <q-item-section avatar>{{ modelValue?.deletes }}</q-item-section>
        </q-item>
      </q-list>

      <q-separator />
      <q-list>
        <q-expansion-item v-if="modelValue?.failuresCount" dense-toggle expand-separator default-opened label="Failures">
          <q-table :columns="columns" :rows="modelValue.failures" virtual-scroll></q-table>
        </q-expansion-item>
      </q-list>

      <!--      <q-separator />-->

      <!--      <q-list>-->
      <!--        <q-expansion-item dense-toggle expand-separator label="History & Comments">-->
      <!--          <entity-meta :entity="modelValue" />-->
      <!--        </q-expansion-item>-->
      <!--      </q-list>-->
    </q-card>
  </q-drawer>
</template>

<script setup>
import { computed } from 'vue';
import EntityMeta from 'pages/subcomponents/EntityMeta.vue';
import { api } from 'boot/axios.js';
import { exportFile } from 'quasar';
import useNotifications from 'src/util/useNotifications';

const baseApiUrl = '/api/datastreams';
const { notifyError } = useNotifications();

const downloadFile = async modelValue => {
  try {
    const response = await api.get(`${baseApiUrl}/${modelValue.id}/download`, {
      responseType: 'blob',
    });
    exportFile(modelValue.name, response.data);
  } catch (error) {
    notifyError(error);
  }
};

const columns = computed(() => [
  {
    name: 'Line',
    label: 'Line',
    field: 'line',
    align: 'left',
  },
  {
    name: 'Reason',
    label: 'Reason',
    field: 'message',
    align: 'left',
  },
]);

const props = defineProps({
  modelValue: Object,
});

const emit = defineEmits(['close']);
</script>
