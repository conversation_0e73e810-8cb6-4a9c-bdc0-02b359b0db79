<template>
  <q-toolbar v-if="hasRoleWriter">
    <q-space />

    <q-btn
      :disable="hasDeliveryPending"
      :label="$t(`afaktoApp.creditLimitRequest.detail.title_${contract.creditInsurance}`)"
      class="buttonBrand"
      icon="add"
      @click="dialog = true"
    >
      <q-tooltip v-if="hasDeliveryPending" anchor="bottom middle" self="top middle">
        {{ $t('afaktoApp.creditLimitRequest.detail.deliveryPending') }}
      </q-tooltip>
    </q-btn>

    <q-dialog v-model="dialog">
      <credit-limit-request-edit v-model:dialog="dialog" :buyer="props.entity" :contract="contract"
                                 @credit-limit-requested="onCreditLimitRequested" />
    </q-dialog>
  </q-toolbar>

  <q-table
    v-if="rows.length"
    :columns="columns"
    :rows="rows"
    :rows-per-page-options="[0]"
    binary-state-sort
    hide-bottom
    @request="onRequest"
  >
    <template #top-left>
      <h3>{{ $t(`afaktoApp.creditLimitRequest.home.table`) }}</h3>
    </template>
    <template #body-cell-createdDate="props">
      <q-td :props="props">
        {{ $d(new Date(props.row.createdDate)) }}
        <q-tooltip>{{ $d(new Date(props.row.createdDate), 'long') }}</q-tooltip>
      </q-td>
    </template>
    <template #body-cell-status="props">
      <q-td :props="props">
        <q-tooltip>{{ $t(`afaktoApp.creditLimitRequest.status.${props.value}`) }}</q-tooltip>
        <span>
        <q-icon :color="$t(`afaktoApp.creditLimitRequest.status.${props.value}_color`)"
                :name="$t(`afaktoApp.creditLimitRequest.status.${props.value}_icon`)" size="md" />
        </span>
      </q-td>
    </template>
  </q-table>
</template>

<script setup>
import { api } from 'boot/axios';
import { computed, onMounted, ref, watch } from 'vue';
import { useI18n } from 'vue-i18n';
import { useRoute } from 'vue-router';
import { useAuthenticationStore } from 'src/stores/authentication-store';
import { useInsurerCreditLimitStore } from 'src/stores/insurer-credit-limit-store.ts';
import useNotifications from 'src/util/useNotifications.js';

import CreditLimitRequestEdit from './CreditLimitRequestEdit.vue';

const { n, t } = useI18n();
const hasRoleWriter = useAuthenticationStore().hasRoleWriter;
const { notifyError } = useNotifications();
const route = useRoute();

const emit = defineEmits(['credit-limit-requested', 'update:entity']);
const rows = ref([]);

const pagination = ref({
  sortBy: route.query.sortBy || 'createdDate',
  descending: route.query.descending !== 'false',
  page: Number.parseInt(route.query.page || 1),
  rowsPerPage: Number.parseInt(route.query.rowsPerPage) || 4
});

const dialog = ref(false);

const columns = computed(() => {
  return [
    {
      align: 'center',
      field: 'createdDate',
      label: t('global.field.createdDate'),
      name: 'createdDate',
      sortable: true
    },
    {
      align: 'left',
      field: 'createdBy',
      label: t('global.field.createdBy'),
      name: 'createdBy',
      sortable: true
    },
    {
      align: 'center',
      classes: props.contract.creditInsurance === 'INTERNAL' ? 'hidden' : '',
      field: 'requestedAmount',
      format: (value, row) => (value != null ? n(value, 'currency', { currency: row.currency }) : ''),
      headerClasses: props.contract.creditInsurance === 'INTERNAL' ? 'hidden' : '',
      label: t('afaktoApp.creditLimitRequest.requestedAmount'),
      name: 'requestedAmount',
      sortable: true
    },
    {
      align: 'right',
      field: 'amount',
      format: (value, row) => (row.status !== 'REQUESTED' && value != null ? n(value, 'currency', { currency: row.currency }) : ''),
      label: t('afaktoApp.creditLimit.amount'),
      name: 'amount',
      sortable: true
    },
    {
      align: 'center',
      classes: props.contract.creditInsurance === 'INTERNAL' ? 'hidden' : '',
      field: 'rate',
      headerClasses: props.contract.creditInsurance === 'INTERNAL' ? 'hidden' : '',
      label: t('afaktoApp.creditLimitRequest.approvedRate'),
      sortable: true,
      format: (value, row) => {
        if (!row.requestedAmount || ['REQUESTED', 'ERROR'].includes(row.status)) return '';
        return n((row.amount / row.requestedAmount) * 100, { signDisplay: 'negative' }, 'percent') + ' %';
      }
    },
    {
      align: 'center',
      classes: props.contract.creditInsurance === 'INTERNAL' ? 'hidden' : '',
      field: 'status',
      headerClasses: props.contract.creditInsurance === 'INTERNAL' ? 'hidden' : '',
      label: t('afaktoApp.creditLimitRequest.status.title'),
      name: 'status',
      sortable: true
    }
  ];
});

const hasDeliveryPending = ref(false);

const onCreditLimitRequested = data => {
  emit('credit-limit-requested', data);
  rows.value.unshift(data);
};

const onRequest = async props => {
  const { page, rowsPerPage, sortBy, descending } = props.pagination;

  try {
    const url = `/api/buyers/${route.params.id}/credit-limit-requests`;
    const response = await api.get(url, {
      params: {
        page: page - 1,
        size: rowsPerPage === 0 ? pagination.value.rowsNumber : rowsPerPage,
        sort: `${sortBy},${descending ? 'desc' : 'asc'}`
      }
    });
    rows.value = response.data;

    pagination.value.rowsNumber = response.headers['x-total-count'];
  } catch (err) {
    notifyError(err);
  }

  pagination.value.page = page;
  pagination.value.rowsPerPage = rowsPerPage;
  pagination.value.sortBy = sortBy;
  pagination.value.descending = descending;
};

const props = defineProps({
  entity: Object,
  contract: Object
});

const store = useInsurerCreditLimitStore();

watch(
  () => store.updated,
  val => {
    if (val) {
      refreshContent();
      store.clearUpdate();
    }
  }
);

async function refreshContent() {
  const creditLimitRequests = await api.get(`/api/buyers/${route.params.id}/credit-limit-requests`);
  hasDeliveryPending.value = creditLimitRequests.data.length
    ? creditLimitRequests.data[creditLimitRequests.data.length - 1].status === 'REQUESTED'
    : false;

  await onRequest({ pagination: pagination.value });
}

onMounted(refreshContent);
</script>
