<template>
  <div class="comments-list">
    <ul v-if="comments.length > 0">
      <li v-for="comment in comments" :key="comment.id" class="comment-item">
        <div class="comment-text">{{ comment.text }}</div>
        <div class="comment-meta">
          {{ $d(comment.createdDate, 'long') }}
          {{ $t('global.field.by') }}
          👤 {{ comment.createdBy }}
        </div>
      </li>
    </ul>
  </div>
</template>

<script setup>
defineProps({
  comments: {
    type: Array,
    default: () => []
  }
});
</script>

<style scoped>
.comments-list ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.comment-item {
  position: relative;
  padding-left: 1.2em;
  margin-bottom: 0.8em;
}

.comment-item::before {
  content: "•";
  position: absolute;
  left: 0;
  color: var(--q-primary);
  font-weight: bold;
}

.comment-text {
  white-space: pre-line;
  margin-bottom: 0.2em;
}

.comment-meta {
  color: var(--q-secondary);
  font-size: smaller;
  text-align: right;
}
</style>
