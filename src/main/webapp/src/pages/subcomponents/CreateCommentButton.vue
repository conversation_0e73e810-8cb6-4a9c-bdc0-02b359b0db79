<template>
  <div>
    <!-- Comment button -->
    <q-btn 
      v-if="hasRoleWriter"
      icon="o_maps_ugc" 
      flat 
      round 
      color="primary"
      @click="showCommentDialog = true"
      :title="$t('afaktoApp.comment.text_helper')"
      v-bind="$attrs"
    />

    <!-- Comment dialog -->
    <q-dialog v-model="showCommentDialog">
      <q-card style="min-width: 400px">
        <q-card-section>
          <div class="text-h6">{{ $t('afaktoApp.comment.text_helper') }}</div>
        </q-card-section>
        
        <q-card-section>
          <q-input 
            v-model="text" 
            :label="$t('afaktoApp.comment.text_helper')" 
            rows="3" 
            type="textarea"
            autofocus
          />
        </q-card-section>
        
        <q-card-actions align="right">
          <q-btn flat :label="$t('entity.action.cancel')" @click="cancelComment" />
          <q-btn color="primary" :label="$t('entity.action.save')" @click="saveComment" />
        </q-card-actions>
      </q-card>
    </q-dialog>
  </div>
</template>

<script setup>
import pluralize from 'pluralize';

import { api } from 'boot/axios';
import { ref } from 'vue';
import { useRoute } from 'vue-router';

import { useAuthenticationStore } from 'src/stores/authentication-store';

const route = useRoute();

const baseApiUrl = '/api/comments';
const hasRoleWriter = useAuthenticationStore().hasRoleWriter;
const text = ref('');
const showCommentDialog = ref(false);

const props = defineProps({
  entity: {
    type: Object,
    required: true,
  },
  entityType: {
    type: String,
    required: false,
    default: null,
  },
});

const emit = defineEmits(['comment-created']);

// Determine entity type from route or prop
const getEntityType = () => {
  if (props.entityType) {
    return props.entityType;
  }
  return pluralize.singular(route.path.split('/')[1]);
};

const saveComment = async () => {
  if (!text.value) return;

  try {
    const newComment = (
      await api.post(baseApiUrl, {
        text: text.value,
        relatedEntityId: props.entity.id,
        relatedEntityType: getEntityType(),
      })
    ).data;
    
    text.value = '';
    showCommentDialog.value = false;
    
    // Emit event so other components can react
    emit('comment-created', newComment);
  } catch (error) {
    console.error('Failed to save comment:', error);
  }
};

const cancelComment = () => {
  text.value = '';
  showCommentDialog.value = false;
};
</script>
