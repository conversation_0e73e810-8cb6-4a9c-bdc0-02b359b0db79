.q-page > .q-table__container {
  th:first-child:has(.q-checkbox),
  td:first-child:has(.q-checkbox) {
    display: none;
  }
}

.q-page > .q-drawer-container {
  overflow-wrap: anywhere;

  .q-drawer {
    width: 30vw !important;
    border-left: solid 1px $borderSecondary;
  }

  & > .q-card__section,
  .q-card__section--vert {
    padding: 12px 16px 24px;
  }

  h3 {
    line-height: normal;
  }

  div.q-item__label {
    font-size: 16px;
  }

  .q-item__section--avatar {
    font-weight: bold;
  }


  .q-card__section {
    border-radius: 0 !important;
  }

  .show-label {
      display: flex;
      align-items: center;
      gap: 4px;
      padding: 2px 4px;
    height: fit-content;
      border-radius: 4px;
      width: fit-content;
      background-color: $infoLowest;
      color: $infoMedium;
    }

  .q-card {
    border: none;
    border-radius: 0 !important;
    box-shadow: none !important;

    & > .q-card__section {
      background-color: $backgroundSecondary;
    }
  }

  .q-separator {
    margin: 0 16px;
  }

  .q-list {
    padding: 16px;

    .q-item {
      padding: 0;
    }
  }

  .q-expansion-item .q-focus-helper {
    display: none;
  }

  .col > .q-item__section--avatar {
    padding-inline: 0;
    padding-block: 12px;
    text-align: end;

  }

  .column > .q-item__section--avatar {
    padding-right: 0 !important;
    align-items: flex-end;
  }

  footer {
    background-color: $backgroundSecondary;
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    margin-top: auto;
    padding: 8px 16px;
    display: flex;
    justify-content: flex-end;
    border-top: solid 1px $borderSecondary;

  }
}

// Dark mode
.body--dark .q-page > .q-drawer-container {
  color: white !important;

  .q-card__section {
    background-color: $backgroundSecondaryDark;
  }

  .q-drawer__content, .q-list {
    background-color: $backgroundPrimaryDark !important;
  }

  .show-label {
    background-color: $infoMedium;
    color: $infoLowest;
  }
}
