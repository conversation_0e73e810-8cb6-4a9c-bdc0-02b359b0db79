.dashboard-card {
  height: 100%;
}

.situation-card {
  .situation-card-header {
    display: flex;
    font-size: 16px;
    justify-content: space-between;
    margin-bottom: 1.6em;
  }

  p {
    font-weight: bold;
  }

  h2 {
    line-height: 1.6em;
  }

  .coverage-progress {
    float: left;
    margin-bottom: -1.7em;
    max-width: calc(100% - 6em);
  }
}

.outstanding-tab-header,
.unavailable-tab-header {
  display: flex;
  gap: 10px;
  align-items: center;
}

.topBuyers {
  h2 {
    line-height: 100%;
  }
  .q-item__section {
    color: $neutralHigher;
    border-bottom: 1px solid $borderPrimary;

    .body--dark & {
      color: $neutralHigherDark;
    }
  }

  .q-list--dense > .q-item,
  .q-item--dense {
    padding: 0 16px !important;
  }

  & > .q-card {
    min-height: 100%;
  }
  .q-tab-panels {
    .q-tab-panel .q-table__control {
      margin-top: 1em;
      margin-bottom: -1em;
    }
    .q-tab-panel,
    .q-table__top,
    .q-tab-panel main {
      padding: 0;
      min-height: auto;
    }
  }
}
