@use 'sass:color'; // Import Sass color functions

// Centralized button styles
// Used across components for consistent button appearance

// Brand button (primary action)
.btn--brand {
  background-color: $brandMedium;
  color: $surface;
  border-radius: 8px;
  font-weight: 600;
  padding: 12px 24px;
  transition: all 0.3s ease;

  &:hover {
    background-color: color.adjust($brandMedium, $lightness: -10%);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  }

  &:active {
    transform: translateY(0);
  }

  &:disabled {
    background-color: $disabled;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
  }
}

// Secondary button
.btn--secondary {
  background-color: transparent;
  color: $neutralHigher;
  border: 1px solid $borderSecondary;
  border-radius: 8px;
  padding: 12px 24px;
  transition: all 0.3s ease;

  .body--dark & {
    color: $neutralHigherDark;
    border-color: $borderSecondaryDark;
  }

  &:hover {
    background-color: $backgroundSecondary;
    border-color: $neutralHigh;

    .body--dark & {
      background-color: $backgroundSecondaryDark;
    }
  }
}

// Icon button
.btn--icon {
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;

  &:hover {
    background-color: $backgroundSecondary;

    .body--dark & {
      background-color: $backgroundSecondaryDark;
    }
  }
}

// Loading state for buttons
.btn--loading {
  pointer-events: none;
  opacity: 0.65;
  transition: opacity 0.3s;
}

// Button group spacing
.btn-group {
  display: flex;
  gap: 12px;
  align-items: center;

  &--vertical {
    flex-direction: column;
  }

  &--center {
    justify-content: center;
  }

  &--end {
    justify-content: flex-end;
  }
}
