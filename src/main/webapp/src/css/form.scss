@import 'quasar/src/css/helpers/math', 'quasar/src/css/helpers/string', 'quasar/src/css/core/flex';

main .q-toolbar {
  @extend .q-gutter-md;
  margin-left: 0;
  margin-right: 0;
  padding-left: 4px;
  padding-right: 0;

  .q-btn {
    z-index: 100;
  }
}

form.q-form {
  @extend .row;
  justify-content: center;

  .q-card {
    @media (max-width: $breakpoint-xs-max) {
      width: 100%;
    }
    @media (min-width: $breakpoint-sm-min) {
      width: 500px;
    }

    .q-card__section {
      @extend .q-gutter-lg;
    }
  }

  .q-tabs.q-tabs--vertical {
    @media (min-width: $breakpoint-md-max) {
      margin-left: -10em;
    }
    .q-tabs__content {
      .q-tab__label {
        min-width: 10em;
        transition: 0.3s;
      }
      .q-tab {
        border-radius: 1em;
        border-top-right-radius: 0;
        border-bottom-right-radius: 0;
        &:hover .q-tab__label,
        &.q-tab--active .q-tab__label {
          font-size: larger;
          font-weight: bold;
        }
      }
    }
  }
  .q-tabs__content {
    // Tabs can wrap around, particularly on small screens
    flex-wrap: wrap;
  }

  .q-toggle__inner--truthy {
    color: $brandMedium !important;
  }

  .q-tab-panels {
    min-width: 50%;
    .q-tab-panel {
      padding-top: 0;

      & > label,
      .q-toggle {
        margin-bottom: 25px;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }
}

.q-field__bottom {
  padding-top: 4px;
}

.q-field--dark.q-field--highlighted .q-field__label,
.q-field--dark .q-field__control {
  color: $surface;
}

// Display required fields with a red asterisk
label.required {
  padding-bottom: 0;
  .q-field__label:after {
    color: $negative;
    content: '*';
    margin-left: 0.4em;
  }
}
