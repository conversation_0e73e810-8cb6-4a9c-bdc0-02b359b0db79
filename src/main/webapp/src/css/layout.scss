// Centralized layout utilities
// Used across components for consistent spacing and positioning

// Spacing utilities
.spacing {
  &--xs {
    gap: 4px;
  }
  &--sm {
    gap: 8px;
  }
  &--md {
    gap: 16px;
  }
  &--lg {
    gap: 24px;
  }
  &--xl {
    gap: 32px;
  }
}

// Flex utilities
.flex {
  display: flex;

  &--column {
    flex-direction: column;
  }
  &--row {
    flex-direction: row;
  }
  &--wrap {
    flex-wrap: wrap;
  }
  &--center {
    align-items: center;
    justify-content: center;
  }
  &--between {
    justify-content: space-between;
  }
  &--around {
    justify-content: space-around;
  }
  &--end {
    justify-content: flex-end;
  }
  &--start {
    justify-content: flex-start;
  }
  &--align-center {
    align-items: center;
  }
  &--align-start {
    align-items: flex-start;
  }
  &--align-end {
    align-items: flex-end;
  }
}

// Grid utilities
.grid {
  display: grid;

  &--2 {
    grid-template-columns: repeat(2, 1fr);
  }
  &--3 {
    grid-template-columns: repeat(3, 1fr);
  }
  &--4 {
    grid-template-columns: repeat(4, 1fr);
  }

  &--gap-sm {
    gap: 8px;
  }
  &--gap-md {
    gap: 16px;
  }
  &--gap-lg {
    gap: 24px;
  }
}

// Section spacing (used in dashboard)
.section {
  &--dashboard {
    margin-bottom: 24px;

    &:last-child {
      margin-bottom: 0;
    }
  }
}

// Header utilities
.header {
  &--tab {
    display: flex;
    gap: 10px;
    align-items: center;
  }

  &--page {
    margin-bottom: 24px;

    h1 {
      line-height: 2rem;
      font-size: 1.8rem;
      font-weight: bold;
      margin: 0;
    }
  }
}

// Responsive utilities
@media (max-width: $breakpoint-sm-max) {
  .responsive {
    &--hide-mobile {
      display: none;
    }
    &--full-width-mobile {
      width: 100%;
    }
  }

  .section--dashboard {
    margin-bottom: 16px;
  }
}

@media (min-width: $breakpoint-md-min) {
  .responsive {
    &--hide-desktop {
      display: none;
    }
  }
}
