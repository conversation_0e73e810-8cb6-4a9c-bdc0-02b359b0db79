@use 'sass:color'; // Import Sass color functions

// Centralized card styles
// Used across dashboard and other components

.q-card {
  border: 1px solid $borderSecondary;
  border-radius: 8px !important;
  box-shadow: 0 2px 4px #0000001a !important;

  .body--dark & {
    border: 1px solid $borderSecondaryDark;
  }

  &.q-card--dark {
    background-color: color.adjust($dark-page, $lightness: -10%);
    box-shadow: 0 0 1em #444;
  }
}

// Base card styles
%card-base {
  border: 1px solid $borderSecondary;
  border-radius: 8px !important;
  box-shadow: 0 2px 4px #0000001a !important;

  .body--dark & {
    border: 1px solid $borderSecondaryDark;
  }
}

// Standard card
.card {
  @extend %card-base;
}

// Dashboard specific card
.card--dashboard {
  @extend %card-base;
  height: 100%;
}

// Situation card (used in dashboard)
.card--situation {
  @extend %card-base;
  min-width: 18em;

  &__header {
    margin-bottom: 1.6em;
    font-weight: bolder;
  }

  p {
    font-weight: bold;
    font-size: 16px;
  }

  h2 {
    line-height: 1.6em;
  }
}

// Dark theme card
.card--dark {
  @extend %card-base;
  background-color: color.adjust($dark-page, $lightness: -10%);
  box-shadow: 0 0 1em #444;
}

// Card with no background (transparent)
.card--transparent {
  box-shadow: none;
  background: transparent !important;
}
