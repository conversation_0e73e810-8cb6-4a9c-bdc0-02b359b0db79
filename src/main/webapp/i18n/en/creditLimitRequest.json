{"afaktoApp": {"creditLimitRequest": {"home": {"title": "Credit Limit Requests", "refreshListLabel": "Refresh list", "createLabel": "Request", "createOrEditLabel": "Create or edit a Credit Limit", "search": "Search for Credit Limit", "notFound": "No Credit Limits found", "newLabel": "New Request", "table": "Credit Limit Requests"}, "created": {"ACCEPTED": "Credit limit has been set", "ERROR": "Credit limit could not be requested", "REQUESTED": "New credit limit has been requested"}, "updated": "A Credit Limit is updated with identifier { param }", "deleted": "A Credit Limit is deleted with identifier { param }", "delete": {"question": "Are you sure you want to delete Credit Limit { id }?"}, "detail": {"title_INTERNAL": "Manual Credit Limit", "title_EXTERNAL": "Request Credit Limit", "deliveryPending": "Credit limit request is locked until the pending delivery is finalized.", "divisibleAmount": "Please enter an amount divisible by { amount }"}, "id": "ID", "totalOutstanding": "Total Outstanding", "outstandingUnderFactor": "Outstanding Under Factor", "requestedAmount": "Requested Amount", "approvedRate": "% Approved", "date": "Date", "insurerDecision": "Insurer decision", "requestInsurerDecision": "Request insurer decision", "insurerComment": "Insurer comment", "requestInsurerComment": "Request insurer comment", "status": {"title": "Status", "REQUESTED": "Requested", "REQUESTED_icon": "o_help_center", "REQUESTED_color": "primary", "PARTIALLY_ACCEPTED": "Partially Accepted", "PARTIALLY_ACCEPTED_icon": "o_gpp_good", "PARTIALLY_ACCEPTED_color": "orange", "ACCEPTED": "Accepted", "ACCEPTED_icon": "o_gpp_good", "ACCEPTED_color": "positive", "REJECTED": "Rejected", "REJECTED_icon": "o_gpp_bad", "REJECTED_color": "negative", "ERROR": "Error", "ERROR_icon": "error", "ERROR_color": "negative"}}}}