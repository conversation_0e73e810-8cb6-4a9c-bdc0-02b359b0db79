{"afaktoApp": {"externalCreditInsurance": {"home": {"title": "<PERSON><PERSON><PERSON>", "refreshListLabel": "Refresh list", "createLabel": "Create a new External Credit Insurance", "createOrEditLabel": "Create or edit a External Credit Insurance", "search": "Search for External Credit Insurance", "notFound": "No External Credit Insurances found", "newLabel": "New Request"}, "created": "A new External Credit Insurance is created with identifier { param }", "updated": "An External Credit Insurance is updated with identifier { param }", "deleted": "An External Credit Insurance is deleted with identifier { param }", "delete": {"question": "Are you sure you want to delete External Credit Insurance { id }?"}, "detail": {"title": "<PERSON><PERSON><PERSON>", "insurerCodeTitle": "ID Assureur de l'acheteur", "insurerCode": {"COFACE": "COFACE EASYNUMBER"}}, "id": "ID", "name": "Nom", "type": "Type", "policy": {"title": "Police", "customerId": "Identifiant client", "policyId": "Identifiant de la police", "blindCover": "Non dénommé", "blindCover_help": "Couverture assureur par défaut", "blindCoverAmount": "Montant non dénommé"}}}}