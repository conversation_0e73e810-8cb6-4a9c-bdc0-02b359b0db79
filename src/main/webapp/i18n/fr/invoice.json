{"afaktoApp": {"invoice": {"home": {"title": "Factures", "refreshListLabel": "Actualiser la liste", "createLabel": "Ajouter une facture", "createOrEditLabel": "<PERSON><PERSON><PERSON> ou éditer une facture", "search": "Recherche de facture", "notFound": "Aucune facture trouvée"}, "created": "La facture { param } a été créée", "updated": "La facture { param } a été mise à jour", "deleted": "La facture { param } a été supprimée", "delete": {"question": "Êtes-vous certain de vouloir supprimer la facture { id }?"}, "detail": {"title": "Facture", "cession": "Cession", "factor": "Factor"}, "upload": {"title": "Importer", "subtitle": "Choisir un ou plusieurs fichiers", "samples": "Fichiers d'exemple", "created": "Factures créées: { count }", "updated": "Factures mises à jour: { count }", "headers": "Exemple d'en-têtes", "headers_help": "La première ligne du fichier doit contenir les en-têtes, leur ordre n'est pas important. Les en-têtes supportés sont:"}, "invoiceFromFactor": {"amount": "<PERSON><PERSON>", "balance": "Solde", "amountDraftReceived": "Retrait reçu", "draftDueDate": "Date d'échéance du retrait", "amountFunded": "<PERSON><PERSON><PERSON><PERSON> garan<PERSON>", "amountUnfunded": "Accepté non garanti", "amountSecured": "Sécurisé", "amountUnsecured": "Non sécurisé", "amountUnavailable": "Indisponible"}, "id": "ID", "buyer": "<PERSON><PERSON><PERSON>", "buyerCode": "Code acheteur", "type": "Type", "invoiceNumber": "Numéro de facture", "date": "Date de facture", "dueDate": "Date d'échéance", "currency": "<PERSON><PERSON>", "amount": "<PERSON><PERSON>", "balance": "Solde", "hasCover": "Avec couverture", "excluded": "Exclu", "exclusionReason": "<PERSON><PERSON><PERSON>'<PERSON>", "underFactor": "Sous factor", "reconciliationJournal": "Journal de lettrage", "gap": "<PERSON><PERSON><PERSON>", "gapType": "Type d'écart", "gapType_values": {"EXCLUSION": "Exclusion", "OVERDUE": "Dépassé", "RECONCILIATION": "Lettrage"}, "gapCredit": "Direction", "gapCredit_values": {"true": "<PERSON><PERSON><PERSON><PERSON>", "false": "<PERSON><PERSON><PERSON><PERSON>"}, "validation": {"positiveAmount": "Le montant de la facture doit être positif", "negativeAmount": "Le montant doit être négatif pour ce type", "positiveBalance": "Le solde de la facture doit être positif", "negativeBalance": "Le solde doit être négatif ou zéro pour ce type", "balanceExceedsAmount": "Le solde ne peut pas dépasser le montant", "differentSigns": "Le montant et le solde doivent avoir le même signe", "dueDateBeforeInvoiceDate": "La date d'échéance doit être égale ou postérieure à la date de facture"}}}}